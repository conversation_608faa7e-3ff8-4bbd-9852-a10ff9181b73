<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="17 -5 340 769" width="340pt" height="769pt" xmlns:dc="http://purl.org/dc/elements/1.1/"><metadata> Produced by OmniGraffle 6.6.1 <dc:date>2016-10-06 18:09:40 +0000</dc:date></metadata><defs><font-face font-family="Open Sans" font-size="12" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="16" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="10" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="8" panose-1="2 11 6 6 3 5 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="544.92188" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="500"><font-face-src><font-face-name name="OpenSans"/></font-face-src></font-face><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="1.308"/><feOffset in="blur" result="offset" dx="0" dy="2"/><feFlood flood-color="black" flood-opacity=".5" result="flood"/><feComposite in="flood" in2="offset" operator="in" result="color"/><feMerge><feMergeNode in="color"/><feMergeNode in="SourceGraphic"/></feMerge></filter><font-face font-family="Open Sans" font-size="8" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face><font-face font-family="Open Sans" font-size="7" panose-1="2 11 7 6 3 8 4 2 2 4" units-per-em="1000" underline-position="-75.195312" underline-thickness="49.804688" slope="0" x-height="549.8047" cap-height="724.1211" ascent="1068.84766" descent="-292.96875" font-weight="bold"><font-face-src><font-face-name name="OpenSans-Semibold"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Canvas 1</title><g><title>Layer 1</title><path d="M 36.346457 311.81102 L 275.46457 311.81102 C 279.88284 311.81102 283.46457 315.39274 283.46457 319.81102 L 283.46457 621.29133 C 283.46457 625.7096 279.88284 629.29133 275.46457 629.29133 L 36.346457 629.29133 C 31.928179 629.29133 28.346457 625.7096 28.346457 621.29133 L 28.346457 319.81102 C 28.346457 315.39274 31.928179 311.81102 36.346457 311.81102 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 316.81102)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="81.91257" y="13" textLength="81.29297">Network Node</tspan></text><text transform="translate(44.68504 9.8582674)" fill="#536870"><tspan font-family="Open Sans" font-size="16" font-weight="500" fill="#536870" x=".12890625" y="17" textLength="274.74219">Open vSwitch - Self-service Networks</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="9.841797" y="35" textLength="57.55078">Network T</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="66.79492" y="35" textLength="4.8984375">r</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="71.453125" y="35" textLength="17.859375">affi</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="89.3125" y="35" textLength="25.30664">c Flo</tspan><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="114.378906" y="35" textLength="150.7793">w - North/South Scenario 1</tspan></text><circle cx="42.519685" cy="737.00787" r="8.5039505" fill="#738a05"/><text transform="translate(55.90551 725)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="72.700195">vider network 1</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="94.91406">VLAN 101, 203.0.113.0/24</tspan></text><path d="M 36.346457 56.692913 L 289.6378 56.692913 C 294.05607 56.692913 297.6378 60.274635 297.6378 64.692913 L 297.6378 281.13386 C 297.6378 285.55213 294.05607 289.13386 289.6378 289.13386 L 36.346457 289.13386 C 31.928179 289.13386 28.346457 285.55213 28.346457 281.13386 L 28.346457 64.692913 C 28.346457 60.274635 31.928179 56.692913 36.346457 56.692913 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(33.346457 61.692913)" fill="#536870"><tspan font-family="Open Sans" font-size="12" font-weight="500" fill="#536870" x="87.06266" y="13" textLength="85.166016">Compute Node</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" fill="#fdf5dd"/><path d="M 50.519685 85.03937 L 91.2126 85.03937 C 95.630876 85.03937 99.2126 88.62109 99.2126 93.03937 L 99.2126 139.40157 C 99.2126 143.81985 95.630876 147.40157 91.2126 147.40157 L 50.519685 147.40157 C 46.101407 147.40157 42.519685 143.81985 42.519685 139.40157 L 42.519685 93.03937 C 42.519685 88.62109 46.101407 85.03937 50.519685 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.9226284" y="9" textLength="32.847656">Instance</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" fill="#fdf5dd"/><path d="M 149.73228 85.03937 L 275.46457 85.03937 C 279.88284 85.03937 283.46457 88.62109 283.46457 93.03937 L 283.46457 139.40157 C 283.46457 143.81985 279.88284 147.40157 275.46457 147.40157 L 149.73228 147.40157 C 145.314005 147.40157 141.73228 143.81985 141.73228 139.40157 L 141.73228 93.03937 C 141.73228 88.62109 145.314005 85.03937 149.73228 85.03937 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 90.03937)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.760673" y="9" textLength="48.210938">Linux Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="59.99578" y="18" textLength="11.740723">qbr</tspan></text></g><line x1="70.86614" y1="127.559054" x2="170.07874" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="170.07874" y1="127.559054" x2="212.59842" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><line x1="212.59842" y1="127.559054" x2="255.11811" y2="127.559054" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke-dasharray="4,4"/><g filter="url(#Shadow)"><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" fill="#2076c8"/><path d="M 64.692913 113.385826 L 77.03937 113.385826 C 81.45765 113.385826 85.03937 116.96755 85.03937 121.385826 L 85.03937 133.73228 C 85.03937 138.15056 81.45765 141.73228 77.03937 141.73228 L 64.692913 141.73228 C 60.274635 141.73228 56.692913 138.15056 56.692913 133.73228 L 56.692913 121.385826 C 56.692913 116.96755 60.274635 113.385826 64.692913 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(1)</tspan></text></g><g filter="url(#Shadow)"><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" fill="#2076c8"/><path d="M 197.92964 129.08154 C 191.51575 127.559054 194.07345 114.742204 204.30504 116.92913 C 205.25431 112.66611 217.15228 113.358047 217.0745 116.92913 C 224.53489 112.36167 234.06882 121.4691 227.67398 126.036566 C 235.34748 128.25099 227.57715 140.182015 221.27953 138.188976 C 220.77553 141.5109 209.51728 142.673385 208.52912 138.188976 C 202.15412 142.97811 188.86121 135.61455 197.92964 129.08154 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.70866 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.0714043" y="9" textLength="9.636719">(3)</tspan></text></g><g filter="url(#Shadow)"><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" fill="#2076c8"/><path d="M 163.90551 113.385826 L 176.25197 113.385826 C 180.67025 113.385826 184.25197 116.96755 184.25197 121.385826 L 184.25197 133.73228 C 184.25197 138.15056 180.67025 141.73228 176.25197 141.73228 L 163.90551 141.73228 C 159.48723 141.73228 155.90551 138.15056 155.90551 133.73228 L 155.90551 121.385826 C 155.90551 116.96755 159.48723 113.385826 163.90551 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(160.90551 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(2)</tspan></text></g><text transform="translate(319.68771 395.3955) rotate(-85.272346)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan></text><circle cx="42.519685" cy="708.6614" r="8.5039505" fill="#bd3612"/><text transform="translate(55.732283 695.6693)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="10.102539">Pr</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="9.902344" y="11" textLength="6.040039">o</tspan><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="15.7421875" y="11" textLength="64.384766">vider network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="17.09375">Aggr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="16.933594" y="23" textLength="20.632812">egate</tspan></text><g filter="url(#Shadow)"><path d="M 50.519685 170.07874 L 133.73228 170.07874 C 138.15056 170.07874 141.73228 173.66046 141.73228 178.07874 L 141.73228 224.44094 C 141.73228 228.85922 138.15056 232.44094 133.73228 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" fill="#fdf5dd"/><path d="M 50.519685 170.07874 L 133.73228 170.07874 C 138.15056 170.07874 141.73228 173.66046 141.73228 178.07874 L 141.73228 224.44094 C 141.73228 228.85922 138.15056 232.44094 133.73228 232.44094 L 50.519685 232.44094 C 46.101407 232.44094 42.519685 228.85922 42.519685 224.44094 L 42.519685 178.07874 C 42.519685 173.66046 46.101407 170.07874 50.519685 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="8.088721" y="9" textLength="8.375"> O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="16.385596" y="9" textLength="15.980469">VS T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="31.967627" y="9" textLength="49.15625">unnel Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="33.97129" y="18" textLength="21.27002">br-tun</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 192.25197 232.44094 C 187.83369 232.44094 184.25197 228.85922 184.25197 224.44094 L 184.25197 178.07874 C 184.25197 173.66046 187.83369 170.07874 192.25197 170.07874 Z" fill="#fdf5dd"/><path d="M 192.25197 170.07874 L 275.46457 170.07874 C 279.88284 170.07874 283.46457 173.66046 283.46457 178.07874 L 283.46457 224.44094 C 283.46457 228.85922 279.88284 232.44094 275.46457 232.44094 L 192.25197 232.44094 C 187.83369 232.44094 184.25197 228.85922 184.25197 224.44094 L 184.25197 178.07874 C 184.25197 173.66046 187.83369 170.07874 192.25197 170.07874 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 175.07874)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x=".73325205" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="6.952002" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="41.416846" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="35.217139" y="18" textLength="18.77832">br-int</tspan></text></g><path d="M 255.11811 127.559054 C 255.11811 127.559054 291.9685 145.98666 291.9685 170.07874 C 291.9685 194.17082 255.11811 212.59842 255.11811 212.59842" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="70.86614" y1="255.11811" x2="70.86614" y2="212.59842" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" fill="#2076c8"/><path d="M 248.94488 113.385826 L 261.29134 113.385826 C 265.70962 113.385826 269.29134 116.96755 269.29134 121.385826 L 269.29134 133.73228 C 269.29134 138.15056 265.70962 141.73228 261.29134 141.73228 L 248.94488 141.73228 C 244.5266 141.73228 240.94488 138.15056 240.94488 133.73228 L 240.94488 121.385826 C 240.94488 116.96755 244.5266 113.385826 248.94488 113.385826 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 122.059054)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(4)</tspan></text></g><g filter="url(#Shadow)"><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" fill="#2076c8"/><path d="M 248.94488 198.4252 L 261.29134 198.4252 C 265.70962 198.4252 269.29134 202.00692 269.29134 206.4252 L 269.29134 218.77165 C 269.29134 223.18993 265.70962 226.77165 261.29134 226.77165 L 248.94488 226.77165 C 244.5266 226.77165 240.94488 223.18993 240.94488 218.77165 L 240.94488 206.4252 C 240.94488 202.00692 244.5266 198.4252 248.94488 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(245.94488 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(5)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" fill="#a57706"/><path d="M 64.692913 198.4252 L 77.03937 198.4252 C 81.45765 198.4252 85.03937 202.00692 85.03937 206.4252 L 85.03937 218.77165 C 85.03937 223.18993 81.45765 226.77165 77.03937 226.77165 L 64.692913 226.77165 C 60.274635 226.77165 56.692913 223.18993 56.692913 218.77165 L 56.692913 206.4252 C 56.692913 202.00692 60.274635 198.4252 64.692913 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(8)</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 425.19685 L 218.77165 425.19685 C 223.18993 425.19685 226.77165 428.77857 226.77165 433.19685 L 226.77165 479.55905 C 226.77165 483.97733 223.18993 487.55905 218.77165 487.55905 L 50.519685 487.55905 C 46.101407 487.55905 42.519685 483.97733 42.519685 479.55905 L 42.519685 433.19685 C 42.519685 428.77857 46.101407 425.19685 50.519685 425.19685 Z" fill="#fdf5dd"/><path d="M 50.519685 425.19685 L 218.77165 425.19685 C 223.18993 425.19685 226.77165 428.77857 226.77165 433.19685 L 226.77165 479.55905 C 226.77165 483.97733 223.18993 487.55905 218.77165 487.55905 L 50.519685 487.55905 C 46.101407 487.55905 42.519685 483.97733 42.519685 479.55905 L 42.519685 433.19685 C 42.519685 428.77857 46.101407 425.19685 50.519685 425.19685 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 430.19685)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="43.252937" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="49.471687" y="9" textLength="34.625">VS Integr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="83.93653" y="9" textLength="47.0625">ation Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="77.736824" y="18" textLength="18.77832">br-int</tspan></text></g><g filter="url(#Shadow)"><path d="M 178.07874 510.23622 L 261.29134 510.23622 C 265.70962 510.23622 269.29134 513.81794 269.29134 518.23622 L 269.29134 564.59842 C 269.29134 569.0167 265.70962 572.59842 261.29134 572.59842 L 178.07874 572.59842 C 173.66046 572.59842 170.07874 569.0167 170.07874 564.59842 L 170.07874 518.23622 C 170.07874 513.81794 173.66046 510.23622 178.07874 510.23622 Z" fill="#fdf5dd"/><path d="M 178.07874 510.23622 L 261.29134 510.23622 C 265.70962 510.23622 269.29134 513.81794 269.29134 518.23622 L 269.29134 564.59842 C 269.29134 569.0167 265.70962 572.59842 261.29134 572.59842 L 178.07874 572.59842 C 173.66046 572.59842 170.07874 569.0167 170.07874 564.59842 L 170.07874 518.23622 C 170.07874 513.81794 173.66046 510.23622 178.07874 510.23622 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(175.07874 515.23622)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="9.1277833" y="9" textLength="6.296875">O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="15.346533" y="9" textLength="15.980469">VS T</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="30.928565" y="9" textLength="49.15625">unnel Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="33.97129" y="18" textLength="21.27002">br-tun</tspan></text></g><g filter="url(#Shadow)"><path d="M 50.519685 510.23622 L 133.73228 510.23622 C 138.15056 510.23622 141.73228 513.81794 141.73228 518.23622 L 141.73228 564.59842 C 141.73228 569.0167 138.15056 572.59842 133.73228 572.59842 L 50.519685 572.59842 C 46.101407 572.59842 42.519685 569.0167 42.519685 564.59842 L 42.519685 518.23622 C 42.519685 513.81794 46.101407 510.23622 50.519685 510.23622 Z" fill="#fdf5dd"/><path d="M 50.519685 510.23622 L 133.73228 510.23622 C 138.15056 510.23622 141.73228 513.81794 141.73228 518.23622 L 141.73228 564.59842 C 141.73228 569.0167 138.15056 572.59842 133.73228 572.59842 L 50.519685 572.59842 C 46.101407 572.59842 42.519685 569.0167 42.519685 564.59842 L 42.519685 518.23622 C 42.519685 513.81794 46.101407 510.23622 50.519685 510.23622 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(47.519685 515.23622)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="4.9441895" y="9" textLength="8.375"> O</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="13.2410645" y="9" textLength="19.824219">VS Pr</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="32.905127" y="9" textLength="4.8867188">o</tspan><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="37.63169" y="9" textLength="46.63672">vider Bridge</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="25.547705" y="18" textLength="17.01123">br-pr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="42.4188" y="18" textLength="4.275879">o</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="46.55454" y="18" textLength="17.110352">vider</tspan></text></g><g filter="url(#Shadow)"><path d="M 93.03937 340.15748 L 176.25197 340.15748 C 180.67025 340.15748 184.25197 343.7392 184.25197 348.15748 L 184.25197 394.51968 C 184.25197 398.93796 180.67025 402.51968 176.25197 402.51968 L 93.03937 402.51968 C 88.62109 402.51968 85.03937 398.93796 85.03937 394.51968 L 85.03937 348.15748 C 85.03937 343.7392 88.62109 340.15748 93.03937 340.15748 Z" fill="#fdf5dd"/><path d="M 93.03937 340.15748 L 176.25197 340.15748 C 180.67025 340.15748 184.25197 343.7392 184.25197 348.15748 L 184.25197 394.51968 C 184.25197 398.93796 180.67025 402.51968 176.25197 402.51968 L 93.03937 402.51968 C 88.62109 402.51968 85.03937 398.93796 85.03937 394.51968 L 85.03937 348.15748 C 85.03937 343.7392 88.62109 340.15748 93.03937 340.15748 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(90.03937 345.15748)" fill="#536870"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#536870" x="7.7996583" y="9" textLength="73.61328">Router Namespace</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="31.725684" y="18" textLength="7.3793945">qr</tspan><tspan font-family="Open Sans" font-size="7" font-weight="bold" fill="#536870" x="38.964942" y="18" textLength="18.521973">outer</tspan></text></g><path d="M 70.86614 269.29134 C 70.86614 269.29134 69.456046 295.2764 141.73228 303.30708 C 214.00852 311.33778 325.98425 297.6378 325.98425 297.6378" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 155.90551 467.71653 C 155.90551 467.71653 184.25197 449.28893 184.25197 425.19685 C 184.25197 401.10477 155.90551 382.67716 155.90551 382.67716" stroke="#2076c8" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 113.385826 467.71653 C 113.385826 467.71653 85.03937 449.28893 85.03937 425.19685 C 85.03937 401.10477 113.385826 382.67716 113.385826 382.67716" stroke="#738a05" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 70.86614 467.71653 C 70.86614 467.71653 47.897637 514.4906 47.897637 538.58267 C 47.897637 562.67475 70.86614 552.7559 70.86614 552.7559" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 198.4252 467.71653 C 198.4252 467.71653 175.74803 514.4906 175.74803 538.58267 C 175.74803 562.67475 198.4252 552.7559 198.4252 552.7559" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="243.77953" y1="552.7559" x2="243.77953" y2="609.4488" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="113.385826" y1="552.7559" x2="113.385826" y2="609.4488" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 325.98425 297.6378 C 325.98425 297.6378 320.9268 450.24505 297.6378 538.58267 C 274.34878 626.9203 243.77953 609.4488 243.77953 609.4488" stroke="#a57706" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><line x1="113.385826" y1="609.4488" x2="113.385826" y2="666.14173" stroke="#bd3612" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 64.692913 255.11811 L 77.03937 255.11811 C 81.45765 255.11811 85.03937 258.69983 85.03937 263.11811 L 85.03937 275.46457 C 85.03937 279.88284 81.45765 283.46457 77.03937 283.46457 L 64.692913 283.46457 C 60.274635 283.46457 56.692913 279.88284 56.692913 275.46457 L 56.692913 263.11811 C 56.692913 258.69983 60.274635 255.11811 64.692913 255.11811 Z" fill="#a57706"/><path d="M 64.692913 255.11811 L 77.03937 255.11811 C 81.45765 255.11811 85.03937 258.69983 85.03937 263.11811 L 85.03937 275.46457 C 85.03937 279.88284 81.45765 283.46457 77.03937 283.46457 L 64.692913 283.46457 C 60.274635 283.46457 56.692913 279.88284 56.692913 275.46457 L 56.692913 263.11811 C 56.692913 258.69983 60.274635 255.11811 64.692913 255.11811 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 263.79134)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(9)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 538.58267 L 77.03937 538.58267 C 81.45765 538.58267 85.03937 542.1644 85.03937 546.58267 L 85.03937 558.92913 C 85.03937 563.3474 81.45765 566.92913 77.03937 566.92913 L 64.692913 566.92913 C 60.274635 566.92913 56.692913 563.3474 56.692913 558.92913 L 56.692913 546.58267 C 56.692913 542.1644 60.274635 538.58267 64.692913 538.58267 Z" fill="#708284"/><path d="M 64.692913 538.58267 L 77.03937 538.58267 C 81.45765 538.58267 85.03937 542.1644 85.03937 546.58267 L 85.03937 558.92913 C 85.03937 563.3474 81.45765 566.92913 77.03937 566.92913 L 64.692913 566.92913 C 60.274635 566.92913 56.692913 563.3474 56.692913 558.92913 L 56.692913 546.58267 C 56.692913 542.1644 60.274635 538.58267 64.692913 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 547.2559)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(20)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 538.58267 L 119.559054 538.58267 C 123.97733 538.58267 127.559054 542.1644 127.559054 546.58267 L 127.559054 558.92913 C 127.559054 563.3474 123.97733 566.92913 119.559054 566.92913 L 107.2126 566.92913 C 102.79432 566.92913 99.2126 563.3474 99.2126 558.92913 L 99.2126 546.58267 C 99.2126 542.1644 102.79432 538.58267 107.2126 538.58267 Z" fill="#bd3612"/><path d="M 107.2126 538.58267 L 119.559054 538.58267 C 123.97733 538.58267 127.559054 542.1644 127.559054 546.58267 L 127.559054 558.92913 C 127.559054 563.3474 123.97733 566.92913 119.559054 566.92913 L 107.2126 566.92913 C 102.79432 566.92913 99.2126 563.3474 99.2126 558.92913 L 99.2126 546.58267 C 99.2126 542.1644 102.79432 538.58267 107.2126 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 547.2559)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(21)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 453.5433 L 119.559054 453.5433 C 123.97733 453.5433 127.559054 457.12503 127.559054 461.5433 L 127.559054 473.88976 C 127.559054 478.30804 123.97733 481.88976 119.559054 481.88976 L 107.2126 481.88976 C 102.79432 481.88976 99.2126 478.30804 99.2126 473.88976 L 99.2126 461.5433 C 99.2126 457.12503 102.79432 453.5433 107.2126 453.5433 Z" fill="#738a05"/><path d="M 107.2126 453.5433 L 119.559054 453.5433 C 123.97733 453.5433 127.559054 457.12503 127.559054 461.5433 L 127.559054 473.88976 C 127.559054 478.30804 123.97733 481.88976 119.559054 481.88976 L 107.2126 481.88976 C 102.79432 481.88976 99.2126 478.30804 99.2126 473.88976 L 99.2126 461.5433 C 99.2126 457.12503 102.79432 453.5433 107.2126 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 462.21653)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(18)</tspan></text></g><g filter="url(#Shadow)"><path d="M 237.6063 595.2756 L 249.95275 595.2756 C 254.37103 595.2756 257.95275 598.8573 257.95275 603.2756 L 257.95275 615.62204 C 257.95275 620.04032 254.37103 623.62204 249.95275 623.62204 L 237.6063 623.62204 C 233.18802 623.62204 229.6063 620.04032 229.6063 615.62204 L 229.6063 603.2756 C 229.6063 598.8573 233.18802 595.2756 237.6063 595.2756 Z" fill="#a57706"/><path d="M 237.6063 595.2756 L 249.95275 595.2756 C 254.37103 595.2756 257.95275 598.8573 257.95275 603.2756 L 257.95275 615.62204 C 257.95275 620.04032 254.37103 623.62204 249.95275 623.62204 L 237.6063 623.62204 C 233.18802 623.62204 229.6063 620.04032 229.6063 615.62204 L 229.6063 603.2756 C 229.6063 598.8573 233.18802 595.2756 237.6063 595.2756 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(234.6063 603.9488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(11)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 538.58267 L 204.59842 538.58267 C 209.0167 538.58267 212.59842 542.1644 212.59842 546.58267 L 212.59842 558.92913 C 212.59842 563.3474 209.0167 566.92913 204.59842 566.92913 L 192.25197 566.92913 C 187.83369 566.92913 184.25197 563.3474 184.25197 558.92913 L 184.25197 546.58267 C 184.25197 542.1644 187.83369 538.58267 192.25197 538.58267 Z" fill="#708284"/><path d="M 192.25197 538.58267 L 204.59842 538.58267 C 209.0167 538.58267 212.59842 542.1644 212.59842 546.58267 L 212.59842 558.92913 C 212.59842 563.3474 209.0167 566.92913 204.59842 566.92913 L 192.25197 566.92913 C 187.83369 566.92913 184.25197 563.3474 184.25197 558.92913 L 184.25197 546.58267 C 184.25197 542.1644 187.83369 538.58267 192.25197 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 547.2559)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(13)</tspan></text></g><g filter="url(#Shadow)"><path d="M 237.6063 538.58267 L 249.95275 538.58267 C 254.37103 538.58267 257.95275 542.1644 257.95275 546.58267 L 257.95275 558.92913 C 257.95275 563.3474 254.37103 566.92913 249.95275 566.92913 L 237.6063 566.92913 C 233.18802 566.92913 229.6063 563.3474 229.6063 558.92913 L 229.6063 546.58267 C 229.6063 542.1644 233.18802 538.58267 237.6063 538.58267 Z" fill="#a57706"/><path d="M 237.6063 538.58267 L 249.95275 538.58267 C 254.37103 538.58267 257.95275 542.1644 257.95275 546.58267 L 257.95275 558.92913 C 257.95275 563.3474 254.37103 566.92913 249.95275 566.92913 L 237.6063 566.92913 C 233.18802 566.92913 229.6063 563.3474 229.6063 558.92913 L 229.6063 546.58267 C 229.6063 542.1644 233.18802 538.58267 237.6063 538.58267 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(234.6063 547.2559)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(12)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 368.50393 L 162.07874 368.50393 C 166.49702 368.50393 170.07874 372.08566 170.07874 376.50393 L 170.07874 388.8504 C 170.07874 393.26867 166.49702 396.8504 162.07874 396.8504 L 149.73228 396.8504 C 145.314005 396.8504 141.73228 393.26867 141.73228 388.8504 L 141.73228 376.50393 C 141.73228 372.08566 145.314005 368.50393 149.73228 368.50393 Z" fill="#2076c8"/><path d="M 149.73228 368.50393 L 162.07874 368.50393 C 166.49702 368.50393 170.07874 372.08566 170.07874 376.50393 L 170.07874 388.8504 C 170.07874 393.26867 166.49702 396.8504 162.07874 396.8504 L 149.73228 396.8504 C 145.314005 396.8504 141.73228 393.26867 141.73228 388.8504 L 141.73228 376.50393 C 141.73228 372.08566 145.314005 368.50393 149.73228 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 377.17716)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(16)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 368.50393 L 119.559054 368.50393 C 123.97733 368.50393 127.559054 372.08566 127.559054 376.50393 L 127.559054 388.8504 C 127.559054 393.26867 123.97733 396.8504 119.559054 396.8504 L 107.2126 396.8504 C 102.79432 396.8504 99.2126 393.26867 99.2126 388.8504 L 99.2126 376.50393 C 99.2126 372.08566 102.79432 368.50393 107.2126 368.50393 Z" fill="#738a05"/><path d="M 107.2126 368.50393 L 119.559054 368.50393 C 123.97733 368.50393 127.559054 372.08566 127.559054 376.50393 L 127.559054 388.8504 C 127.559054 393.26867 123.97733 396.8504 119.559054 396.8504 L 107.2126 396.8504 C 102.79432 396.8504 99.2126 393.26867 99.2126 388.8504 L 99.2126 376.50393 C 99.2126 372.08566 102.79432 368.50393 107.2126 368.50393 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 377.17716)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(17)</tspan></text></g><g filter="url(#Shadow)"><path d="M 149.73228 453.5433 L 162.07874 453.5433 C 166.49702 453.5433 170.07874 457.12503 170.07874 461.5433 L 170.07874 473.88976 C 170.07874 478.30804 166.49702 481.88976 162.07874 481.88976 L 149.73228 481.88976 C 145.314005 481.88976 141.73228 478.30804 141.73228 473.88976 L 141.73228 461.5433 C 141.73228 457.12503 145.314005 453.5433 149.73228 453.5433 Z" fill="#2076c8"/><path d="M 149.73228 453.5433 L 162.07874 453.5433 C 166.49702 453.5433 170.07874 457.12503 170.07874 461.5433 L 170.07874 473.88976 C 170.07874 478.30804 166.49702 481.88976 162.07874 481.88976 L 149.73228 481.88976 C 145.314005 481.88976 141.73228 478.30804 141.73228 473.88976 L 141.73228 461.5433 C 141.73228 457.12503 145.314005 453.5433 149.73228 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(146.73228 462.21653)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(15)</tspan></text></g><g filter="url(#Shadow)"><path d="M 192.25197 453.5433 L 204.59842 453.5433 C 209.0167 453.5433 212.59842 457.12503 212.59842 461.5433 L 212.59842 473.88976 C 212.59842 478.30804 209.0167 481.88976 204.59842 481.88976 L 192.25197 481.88976 C 187.83369 481.88976 184.25197 478.30804 184.25197 473.88976 L 184.25197 461.5433 C 184.25197 457.12503 187.83369 453.5433 192.25197 453.5433 Z" fill="#708284"/><path d="M 192.25197 453.5433 L 204.59842 453.5433 C 209.0167 453.5433 212.59842 457.12503 212.59842 461.5433 L 212.59842 473.88976 C 212.59842 478.30804 209.0167 481.88976 204.59842 481.88976 L 192.25197 481.88976 C 187.83369 481.88976 184.25197 478.30804 184.25197 473.88976 L 184.25197 461.5433 C 184.25197 457.12503 187.83369 453.5433 192.25197 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(189.25197 462.21653)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(14)</tspan></text></g><g filter="url(#Shadow)"><path d="M 64.692913 453.5433 L 77.03937 453.5433 C 81.45765 453.5433 85.03937 457.12503 85.03937 461.5433 L 85.03937 473.88976 C 85.03937 478.30804 81.45765 481.88976 77.03937 481.88976 L 64.692913 481.88976 C 60.274635 481.88976 56.692913 478.30804 56.692913 473.88976 L 56.692913 461.5433 C 56.692913 457.12503 60.274635 453.5433 64.692913 453.5433 Z" fill="#708284"/><path d="M 64.692913 453.5433 L 77.03937 453.5433 C 81.45765 453.5433 85.03937 457.12503 85.03937 461.5433 L 85.03937 473.88976 C 85.03937 478.30804 81.45765 481.88976 77.03937 481.88976 L 64.692913 481.88976 C 60.274635 481.88976 56.692913 478.30804 56.692913 473.88976 L 56.692913 461.5433 C 56.692913 457.12503 60.274635 453.5433 64.692913 453.5433 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(61.692913 462.21653)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(19)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 595.2756 L 119.559054 595.2756 C 123.97733 595.2756 127.559054 598.8573 127.559054 603.2756 L 127.559054 615.62204 C 127.559054 620.04032 123.97733 623.62204 119.559054 623.62204 L 107.2126 623.62204 C 102.79432 623.62204 99.2126 620.04032 99.2126 615.62204 L 99.2126 603.2756 C 99.2126 598.8573 102.79432 595.2756 107.2126 595.2756 Z" fill="#bd3612"/><path d="M 107.2126 595.2756 L 119.559054 595.2756 C 123.97733 595.2756 127.559054 598.8573 127.559054 603.2756 L 127.559054 615.62204 C 127.559054 620.04032 123.97733 623.62204 119.559054 623.62204 L 107.2126 623.62204 C 102.79432 623.62204 99.2126 620.04032 99.2126 615.62204 L 99.2126 603.2756 C 99.2126 598.8573 102.79432 595.2756 107.2126 595.2756 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 603.9488)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="2.0716658" y="9" textLength="14.203125">(22)</tspan></text></g><path d="M 311.31547 299.16028 C 304.90157 297.6378 307.45927 284.82094 317.69087 287.00787 C 318.64014 282.74485 330.5381 283.43679 330.46033 287.00787 C 337.92072 282.4404 347.45465 291.54784 341.0598 296.1153 C 348.7333 298.32973 340.96297 310.26075 334.66535 308.26771 C 334.16135 311.58964 322.9031 312.75212 321.91495 308.26771 C 315.53994 313.05685 302.24704 305.69329 311.31547 299.16028 Z" fill="#a57706"/><path d="M 311.31547 299.16028 C 304.90157 297.6378 307.45927 284.82094 317.69087 287.00787 C 318.64014 282.74485 330.5381 283.43679 330.46033 287.00787 C 337.92072 282.4404 347.45465 291.54784 341.0598 296.1153 C 348.7333 298.32973 340.96297 310.26075 334.66535 308.26771 C 334.16135 311.58964 322.9031 312.75212 321.91495 308.26771 C 315.53994 313.05685 302.24704 305.69329 311.31547 299.16028 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(317.09449 292.1378)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="9" textLength="14.203125">(10)</tspan></text><line x1="212.59842" y1="212.59842" x2="113.385826" y2="212.59842" stroke="#708284" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><g filter="url(#Shadow)"><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" fill="#708284"/><path d="M 206.4252 198.4252 L 218.77165 198.4252 C 223.18993 198.4252 226.77165 202.00692 226.77165 206.4252 L 226.77165 218.77165 C 226.77165 223.18993 223.18993 226.77165 218.77165 226.77165 L 206.4252 226.77165 C 202.00692 226.77165 198.4252 223.18993 198.4252 218.77165 L 198.4252 206.4252 C 198.4252 202.00692 202.00692 198.4252 206.4252 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(203.4252 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(6)</tspan></text></g><g filter="url(#Shadow)"><path d="M 107.2126 198.4252 L 119.559054 198.4252 C 123.97733 198.4252 127.559054 202.00692 127.559054 206.4252 L 127.559054 218.77165 C 127.559054 223.18993 123.97733 226.77165 119.559054 226.77165 L 107.2126 226.77165 C 102.79432 226.77165 99.2126 223.18993 99.2126 218.77165 L 99.2126 206.4252 C 99.2126 202.00692 102.79432 198.4252 107.2126 198.4252 Z" fill="#708284"/><path d="M 107.2126 198.4252 L 119.559054 198.4252 C 123.97733 198.4252 127.559054 202.00692 127.559054 206.4252 L 127.559054 218.77165 C 127.559054 223.18993 123.97733 226.77165 119.559054 226.77165 L 107.2126 226.77165 C 102.79432 226.77165 99.2126 223.18993 99.2126 218.77165 L 99.2126 206.4252 C 99.2126 202.00692 102.79432 198.4252 107.2126 198.4252 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.2126 207.09842)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="4.354869" y="9" textLength="9.636719">(7)</tspan></text></g><path d="M 98.717045 667.6642 C 92.30315 666.14173 94.86085 653.32488 105.092446 655.5118 C 106.04171 651.2488 117.939684 651.94072 117.8619 655.5118 C 125.32229 650.94434 134.856225 660.05178 128.46138 664.61924 C 136.13488 666.83367 128.36455 678.7647 122.06693 676.77165 C 121.56293 680.09357 110.30468 681.25606 109.31652 676.77165 C 102.94152 681.5608 89.648617 674.19722 98.717045 667.6642 Z" fill="#bd3612"/><path d="M 98.717045 667.6642 C 92.30315 666.14173 94.86085 653.32488 105.092446 655.5118 C 106.04171 651.2488 117.939684 651.94072 117.8619 655.5118 C 125.32229 650.94434 134.856225 660.05178 128.46138 664.61924 C 136.13488 666.83367 128.36455 678.7647 122.06693 676.77165 C 121.56293 680.09357 110.30468 681.25606 109.31652 676.77165 C 102.94152 681.5608 89.648617 674.19722 98.717045 667.6642 Z" stroke="#536870" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/><text transform="translate(104.49606 660.64173)" fill="#fcf4dc"><tspan font-family="Open Sans" font-size="8" font-weight="bold" fill="#fcf4dc" x="1.788201" y="9" textLength="14.203125">(23)</tspan></text><circle cx="184.25197" cy="737.00787" r="8.5039505" fill="#2076c8"/><text transform="translate(197.7559 725)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="93.63281">Self-service network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="87.92969">VNI 101, 192.168.1.0/24</tspan></text><text transform="translate(197.7559 696.65354)" fill="#536870"><tspan font-family="Open Sans" font-size="10" font-weight="500" fill="#536870" x="0" y="11" textLength="76.64551">Overlay network</tspan><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#536870" x="0" y="23" textLength="41.34375">10.0.1.0/24</tspan></text><circle cx="184.25197" cy="708.6614" r="8.5039505" fill="#a57706"/><text transform="translate(90.72141 291.75432) rotate(17.305357)" fill="#2176c7"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#2176c7" x=".087890625" y="9" textLength="28.824219">VNI 101</tspan></text><text transform="translate(118.385826 632.30467)" fill="#738a05"><tspan font-family="Open Sans" font-size="8" font-weight="500" fill="#738a05" x=".095703125" y="9" textLength="35.808594">VLAN 101</tspan></text></g></g></svg>
