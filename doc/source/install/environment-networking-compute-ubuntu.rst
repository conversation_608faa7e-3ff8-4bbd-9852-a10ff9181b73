Compute node
~~~~~~~~~~~~

Configure network interfaces
----------------------------

#. Configure the first interface as the management interface:

   IP address: *********

   Network mask: ************* (or /24)

   Default gateway: ********

   .. note::

      Additional compute nodes should use *********, *********, and so on.

#. The provider interface uses a special configuration without an IP
   address assigned to it. Configure the second interface as the provider
   interface:

   Replace ``INTERFACE_NAME`` with the actual interface name. For example,
   *eth1* or *ens224*.


* Edit the ``/etc/network/interfaces`` file to contain the following:

  .. path /etc/network/interfaces
  .. code-block:: bash

     # The provider network interface
     auto INTERFACE_NAME
     iface  INTERFACE_NAME inet manual
     up ip link set dev $IFACE up
     down ip link set dev $IFACE down

  .. end




#. Reboot the system to activate the changes.

Configure name resolution
-------------------------

#. Set the hostname of the node to ``compute1``.

#. .. include:: shared/edit_hosts_file.txt
