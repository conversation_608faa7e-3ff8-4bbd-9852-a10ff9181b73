# Copyright 2023 Acronis
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import collections
import hashlib

import netaddr
from neutron_lib.agent import l2_extension
from neutron_lib.agent import topics
from neutron_lib import constants as lib_const
from neutron_lib import context as n_context
from neutron_lib.utils import net
from oslo_config import cfg
from oslo_log import log as logging
import oslo_messaging
from oslo_serialization import jsonutils
from oslo_service import loopingcall

from ryu.base import app_manager
from ryu.controller import handler
from ryu.controller import ofp_event
from ryu.ofproto import ofproto_v1_3

from neutron.agent.common import ovs_lib as ovs_lib
from neutron.agent.common import utils
from neutron.agent import firewall as firewall_agent
from neutron.agent.l2.extensions.port_check.flow_checker import privatefloating
from neutron.agent.l2.extensions.port_check import flow_checker_manager
from neutron.agent.l2.extensions.port_check import flow_tracer
from neutron.api.rpc.handlers import securitygroups_rpc as sg_rpc
from neutron.common import rpc as n_rpc
from neutron.objects import ports as port_obj
from neutron.plugins.ml2.drivers.agent import capabilities
from neutron.services.port_check.drivers import firewall_driver
from neutron.services.port_check.drivers.openvswitch import ovs_bridge
from neutron.services.port_check import plugin


LOG = logging.getLogger(__name__)


class OVSFlowMonitorRyuApp(app_manager.RyuApp):
    OFP_VERSIONS = [ofproto_v1_3.OFP_VERSION]
    _event_handlers = []
    _revive_handlers = []

    def register_event_handler(self, caller):
        self._event_handlers.append(caller)

    def register_revive_handler(self, caller):
        self._revive_handlers.append(caller)

    @handler.set_ev_cls(ofp_event.EventOFPFlowStatsReply,
                        handler.MAIN_DISPATCHER)
    def flow_monitor_reply_handler(self, ev):
        for flow_stat in ev.msg.body:
            for caller in self._event_handlers:
                caller(ev.msg.datapath.id, flow_stat)

    def start(self, int_bridge, phys_bridges):
        super(OVSFlowMonitorRyuApp, self).start()
        self.int_bridge = int_bridge
        self.phys_bridges = phys_bridges

    def start_monitor_bridge(self, bridge):
        if not bridge:
            return
        dp, ofp, ofpp = bridge._get_dp()
        msg = ofpp.OFPFlowStatsRequest(
            dp, 0, ofp.OFPTT_ALL, ofp.OFPP_ANY, ofp.OFPG_ANY)
        bridge._send_msg(msg)

    def start_monitor(self):
        self.start_monitor_bridge(self.int_bridge)
        for phy_br in self.phys_bridges:
            self.start_monitor_bridge(phy_br)


class OpenFlowMap(object):
    def __init__(self):
        self._map = collections.defaultdict(dict)

    def put(self, datapath_id, flow):
        key = self.get_key(flow)
        value = self.get_value(flow)
        self._map[datapath_id][key] = value

    def exists(self, datapath_id, flow):
        key = self.get_key(flow)
        value = self.get_value(flow)
        return self._map[datapath_id].get(key) == value

    def remove(self, flow):
        key = self.get_key(flow)
        self._map.pop(key, None)

    def on_update(self, datapath_id, flow):
        self.put(datapath_id, flow)

    def reset(self):
        self._map.clear()

    @classmethod
    def get_key(cls, flow):
        match = cls._match_dict(flow.match)
        return cls.sha256sum(table_id=flow.table_id, priority=flow.priority,
                             match=match)

    @classmethod
    def get_value(cls, flow):
        actions = cls._actions_list(flow.instructions)
        return cls.sha256sum(actions=actions)

    @staticmethod
    def sha256sum(**values):
        sha256 = hashlib.sha256(
            jsonutils.dumps(values, sort_keys=True).encode())
        return sha256.hexdigest()

    @staticmethod
    def _actions_list(instructions):
        rv = []
        for instruction in instructions:
            if not hasattr(instruction, 'actions'):
                continue
            actions = []
            is_conjuction = False
            for action in instruction.actions:
                action_dict = action.to_jsondict()
                # OFPActionPushVlan action can result in false positive
                if 'OFPActionPushVlan' in action_dict:
                    continue
                if 'NXActionLearn' in action_dict:
                    continue
                if 'NXActionRegLoad' in action_dict:
                    continue
                # ignore action load:val->reg
                if 'OFPActionSetField' in action_dict:
                    act_params = action_dict.get('OFPActionSetField')
                    oxm = act_params['field']['OXMTlv']
                    if oxm.get('field', '').startswith('reg'):
                        continue
                    # br-phy table=82 arp mac learn
                    if oxm.get('field', '') == 'eth_dst':
                        continue
                if 'NXActionRegMove' in action_dict:
                    remap = {'arp_tha', 'arp_sha', 'arp_tpa', 'arp_spa',
                             'eth_dst', 'eth_src', 'ipv6_dst'}
                    act_params = action_dict.get('NXActionRegMove')
                    for act_field, act_val in act_params.items():
                        if act_val in remap:
                            act_params[act_field] = act_val + '_nxm'
                # The 'len' and 'max_len' attrs can result in false positive
                # results for comparison actions. Ignore these attrs:
                # action dict looks like:
                # {'NXActionClass': {'experimenter': 8992, 'len': 16, ...}}
                for key, value in action_dict.items():
                    value.pop('len', None)
                    value.pop('max_len', None)
                    if key == 'NXActionConjunction':
                        is_conjuction = True
                    elif is_conjuction:
                        # From the ovs-fields documentation: "A flow with
                        # conjunction actions may also include note actions for
                        # annotations, but not any other kind of actions."
                        raise ValueError(
                            'non-conjunction and conjunction actions collison')
                    actions.append(action_dict)
            if is_conjuction:
                # From the ovs-fields documentation: "The order of conjunction
                # actions within a list of actions is not significant."
                # And neutron doesn't always keep order. But we need a defined
                # order to calculate a hash sum.
                actions.sort(
                    key=lambda item: item['NXActionConjunction']['id'])
            rv.extend(actions)
        return rv

    @staticmethod
    def _normalize_vlan_vid(vlan_vid):
        '''
        The OpenFlow standard describes this field as consisting of "12+1"
        bits. On ingress, its value is 0 if no 802.1Q header is present, and
        otherwise it holds the VLAN VID in its least significant 12 bits, with
        bit 12 (0x1000 aka OFPVID_PRESENT) also set to 1. The three most
        significant bits are always zero:

         OXM_OF_VLAN_VID
         <------------->
          3  1     12
        +---+--+--------+
        |   |P |VLAN ID |
        +---+--+--------+
          0
        '''
        value, mask = (
            (vlan_vid, 0x1fff) if isinstance(vlan_vid, int) else vlan_vid)
        mask &= 0x1fff
        value = value & mask
        return value, mask

    @classmethod
    def _match_dict(cls, match):
        rv = dict(match.items())
        for ip_src in ('ipv4_src', 'ipv6_src'):
            ip_src_val = rv.get(ip_src)
            if isinstance(ip_src_val, str):
                addr = netaddr.IPNetwork(ip_src_val)
                rv[ip_src] = (str(addr.ip), str(addr.netmask))
        # for service datapath ignore reg7 match
        rv.pop('reg7', None)
        # NOTE(akurbatov): os-ken doesn't well support a vlan_tci field,
        # i.e. if someone creates a flow with the vlan_tci=0x5000/0xe000
        # match then the os-ken is going to return something like this for
        # such match: OFPMatch(oxm_fields={'field_4194348': 'AAAAAA=='})
        # Fortunately neutron doesn't use vlan_tci functionality (with
        # priorities) and only uses vlan_vid functionality (without
        # priorities). So, we don't take priority into account and equate
        # vlan_tci and vlan_vid to the same match field:
        vlan_tci = rv.pop('vlan_tci', None)
        vlan_vid = rv.pop('vlan_vid', None)
        if vlan_tci is not None:
            if vlan_vid is not None:
                raise ValueError('Both vlan_tci and vlan_vid are provided')
            vlan_vid = vlan_tci
        if vlan_vid is not None:
            rv['vlan_vid'] = cls._normalize_vlan_vid(vlan_vid)
        return rv


class PortCheckAgentExtension(l2_extension.L2AgentExtension):
    target = oslo_messaging.Target(version='1.0')

    def __init__(self):
        super(PortCheckAgentExtension, self).__init__()
        capabilities.register(self.init_handler, lib_const.AGENT_TYPE_OVS)

    def initialize(self, connection, driver_type):
        self.of_map = OpenFlowMap()
        self.br_int = ovs_bridge.OVSIntegrationBridge(
            'br-int',
            ryu_app=self.agent_api.br_int._app,
            of_map=self.of_map)
        self.phy_br = None
        self.meta_br = None

        self.init_loop = loopingcall.FixedIntervalLoopingCall(
            self.init_check_manager)
        self.init_loop.start(
            interval=cfg.CONF.AGENT.retrieve_pfn_interval,
            initial_delay=cfg.CONF.AGENT.retrieve_pfn_interval,
            stop_on_exception=False)

        self.flow_tracer = flow_tracer.FlowTracer()

        self.create_rpc_conn()
        LOG.info('Port-check agent extenstion loaded')

    def init_check_manager(self):
        self.init_metadata_bridge()
        self.init_private_floating_bridge()
        self.ext_check_mgr = flow_checker_manager.ExtensionsFlowCheckManager(
            br_int=self.br_int, phy_br=self.phy_br, meta_br=self.meta_br,
            agent_api=self.agent_api)
        self.start_flow_monitor_app()

        self.init_loop.stop()
        return True

    def init_private_floating_bridge(self):
        context = n_context.get_admin_context_without_session()
        agent_id = 'ovs-agent-%s' % cfg.CONF.host
        pf_info = self.plugin_rpc.get_privatefloating_info(
            context, agent_id=agent_id, host=cfg.CONF.host)

        self.enable_private_floating = False
        if pf_info:
            self.enable_private_floating = (
                pf_info.get('privatefloating_enable', False))

        if 'service_datapath' in cfg.CONF.agent.extensions:
            pf_network = pf_info.get('privatefloating_network', {})
            pfn_physnet = pf_network.get('provider:physical_network')
            pfn_bridge = self.agent_api.bridge_mappings.get(pfn_physnet)
            if pfn_bridge and pfn_physnet:
                self.phy_br = ovs_bridge.OVSPhysicalBridge(
                    pfn_bridge,
                    ryu_app=self.agent_api.phys_brs.get(pfn_physnet)._app,
                    of_map=self.of_map)

    def init_metadata_bridge(self):
        if 'metadata_path' in cfg.CONF.agent.extensions:
            self.meta_br = ovs_bridge.OVSPhysicalBridge(
                'br-meta',
                ryu_app=self.agent_api.phys_brs.get('meta')._app,
                of_map=self.of_map)

    def create_rpc_conn(self):
        self._connection = n_rpc.Connection()
        self._connection.create_consumer(plugin.TOPIC_PORT_CHECK, [self])
        self._connection.consume_in_threads()

    def init_handler(self, resource, event, trigger, payload=None):
        self.ovs_agent = trigger

    def consume_api(self, agent_api):
        self.agent_api = agent_api
        self.plugin_rpc = agent_api.plugin_rpc

    def start_flow_monitor_app(self):
        app_mgr = app_manager.AppManager.get_instance()
        self.ovs_flow_monitor_app = app_mgr.instantiate(OVSFlowMonitorRyuApp)

        self.ovs_flow_monitor_app.register_event_handler(
            self.of_map.on_update)
        self.ovs_flow_monitor_app.register_revive_handler(
            self._ovs_revived)

        self.ovs_flow_monitor_app.start(
            self.br_int, [self.phy_br, self.meta_br])

    def _ovs_revived(self):
        LOG.info("Dump openflows")
        # get rid of outdated/stale openflows:
        self.of_map.reset()
        self.ovs_flow_monitor_app.start_monitor()

    def ports_check(self, context, **kwargs):
        self._ovs_revived()
        result_map = collections.defaultdict(plugin.PortCheckResult)
        ports = [port_obj.Port.obj_from_primitive(port)
                 for port in kwargs['ports']]
        try:
            self.do_ports_check(context, result_map, ports)
        except Exception as err:
            LOG.exception('do_ports_check error')
            for port in ports:
                reports = result_map[port.id]['openvswitch']
                reports.add('do_ports_check error: %s', err)
        return dict((port_id, result.to_dict())
                    for port_id, result in result_map.items())

    def do_ports_check(self, context, result_map, ports):
        LOG.info('Checking ports (count=%d)', len(ports))
        ports = self._do_ovs_check(context, result_map, ports)
        self._do_firewall_check(context, result_map, ports)

        if self.enable_private_floating and cfg.CONF.AGENT.enable_pfn_agent:
            self._do_private_floating_network_check(context, result_map, ports)

        self.ext_check_mgr.do_extensions_flow_check(context, result_map, ports)

    def _do_firewall_check(self, context, result_map, ports):
        # Intentionally use SecurityGroupServerRpcApi to honestly pull
        # data from the DB instead of local resource cache:
        sg_plugin_rpc = sg_rpc.SecurityGroupServerRpcApi(topics.PLUGIN)
        devices = {}
        security_groups = {}
        sg_member_ips = {}
        device_ids = [port.id for port in ports]
        devices_info = sg_plugin_rpc.security_group_info_for_devices(
            context, device_ids)
        devices.update(devices_info['devices'])
        security_groups.update(devices_info['security_groups'])
        sg_member_ips.update(devices_info['sg_member_ips'])

        firewall_class = firewall_agent.load_firewall_driver_class(
            cfg.CONF.SECURITYGROUP.firewall_driver)
        if issubclass(firewall_driver.OVSFirewallDriver, firewall_class):
            firewall = firewall_driver.OVSFirewallDriver(self.br_int)
        elif issubclass(
                firewall_driver.OVSStatelessFirewallDriver, firewall_class):
            firewall = firewall_driver.OVSStatelessFirewallDriver(self.br_int)
        else:
            LOG.exception('only support "OVSFirewallDriver" or '
                          '"OVSStatelessFirewallDriver" firewall driver')
            return

        for sg_id, sg_rules in security_groups.items():
            firewall.update_security_group_rules(sg_id, sg_rules)
        for remote_sg_id, member_ips in sg_member_ips.items():
            # There is a bug in security_group_info_for_devices that returns
            # member_ips to the client as a list of lists instead of list of
            # tuples as is declared on the server side. See commit 00298fe6e84
            _member_ips = {}
            for ethertype, addrs in member_ips.items():
                _member_ips[ethertype] = [tuple(addr) for addr in addrs]
            firewall.update_security_group_members(
                remote_sg_id, _member_ips)

        for port in ports:
            del firewall.reports[:]
            reports = result_map[port.id]['firewall']
            try:
                if net.is_port_trusted(port):
                    firewall.process_trusted_ports([port.id])
                else:
                    port_dict = devices.get(port.id)
                    if port_dict:
                        firewall.prepare_port_filter(port_dict)
                    else:
                        reports.add('Cannot find device with port_id %s',
                                    port.id)
                reports.extend(list(set(firewall.reports)))
            except Exception as err:
                if logging.is_debug_enabled(cfg.CONF):
                    LOG.exception('firewall error')
                reports.add('firewall error: %s', err)

    def _do_ovs_check(self, context, result_map, ports):
        ports_need_check = []
        for port in ports:
            # port tag
            reports = result_map[port.id]['openvswitch']
            port_name = utils.get_port_name_by_id(self.br_int, port.id)
            if not port_name:
                reports.add('port %s not found on bridge %s',
                            port.id, self.br_int.br_name)
                continue
            port_tag = self.br_int.get_port_tag_by_name(port_name)
            if port_tag <= 0 or port_tag >= 4095:
                reports.add('port %s has invalid tag %s', port.id, port_tag)
                continue

            port_external_ids = self.br_int.get_port_external_ids(port_name)
            port_id = port_mac = None
            if isinstance(port_external_ids, dict):
                port_id = port_external_ids.get('iface-id')
                port_mac = port_external_ids.get('attached-mac')
            if port.id != port_id:
                reports.add('port %s device %s has wrong port_id %s.',
                            port.id, port_name, port_id)
                continue
            if port.mac_address != port_mac:
                reports.add('Ovsdb port mac %s is not equal to port db mac %s',
                            port_mac, port.mac_address)
                continue
            ports_need_check.append(port)
        return ports_need_check

    def _do_private_floating_network_check(self, context, result_map, ports):
        br_int = ovs_lib.OVSBridge(cfg.CONF.ovs_integration_bridge)
        for port in ports:
            if not port.device_owner.startswith(
                    lib_const.DEVICE_OWNER_COMPUTE_PREFIX):
                return
            reports = result_map[port.id]['private_floating']
            port_name = utils.get_port_name_by_id(br_int, port.id)
            if not port_name:
                reports.add('port %s not found on bridge %s',
                            port.id, br_int.br_name)
                continue
            port_ofport = br_int.get_port_ofport(port_name)
            port_tag = br_int.get_port_tag_by_name(port_name)

            host_info = privatefloating.HostInfos(cfg.CONF)
            pfn_port_id = host_info.privatefloating_port['id']
            pfn_port_name = utils.get_port_name_by_id(br_int, pfn_port_id)
            if not pfn_port_name:
                reports.add('pfn port %s not found on bridge %s',
                            pfn_port_id, br_int.br_name)
                continue
            pfn_port_ofport = br_int.get_port_ofport(pfn_port_name)
            pfn_port_tag = br_int.get_port_tag_by_name(pfn_port_name)
            p = {'port_tag': port_tag,
                 'port_ofport': port_ofport}
            pfn_p = {'port_tag': pfn_port_tag,
                     'port_ofport': pfn_port_ofport}
            privatefloating.check_private_floating_flows(
                host_info, port, br_int, p, pfn_p, reports)

    def flow_trace(self, context, port, **kwargs):
        return self.flow_tracer.flow_trace(context, port, **kwargs)

    def handle_port(self, context, port):
        # L2AgentExtension.handle_port is abstractmethod.
        # handle_port is not used by this extensions.
        pass

    def delete_port(self, context, port):
        # L2AgentExtension.delete_port is abstractmethod.
        # delete_port is not used by this extensions.
        pass
