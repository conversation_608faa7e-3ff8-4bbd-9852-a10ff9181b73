#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib import constants
from neutron_lib.plugins import utils as p_utils
from oslo_config import cfg

from neutron.agent.common import utils
from neutron.agent.l2.extensions.nat import metadata_path
from neutron.agent.l2.extensions.port_check.flow_checker import base
from neutron.api.rpc.callbacks import resources
from neutron.common import constants as comm_consts
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const


class MetadataPathFlowCheck(base.ExtensionFlowCheckBase,
                            metadata_path.MetadataPathAgentExtension):
    def __init__(self, br_int, br_meta, agent_api, *args, **kwargs):
        super(MetadataPath<PERSON><PERSON><PERSON>heck, self).__init__(br_int)
        self.meta_br = br_meta
        self.set_path_br(self.meta_br)
        self.consume_api(agent_api)
        self.ext_api = metadata_path.MetadataPathExtensionPortInfoAPI(
            self.rcache_api)
        self.init_metadata_path()
        self.instance_infos = {}

    def consume_api(self, agent_api):
        self.agent_api = agent_api
        self.rcache_api = agent_api.plugin_rpc.remote_resource_cache

    @property
    def reports(self):
        return set(self.meta_br.reports + self.int_br.reports)

    def clear(self):
        del self.meta_br.reports[:]
        del self.int_br.reports[:]

    def init_metadata_path(self):
        bridge = self.agent_api.bridge_mappings.get('meta')
        port_name = p_utils.get_interface_name(
            bridge, prefix=p_const.PEER_INTEGRATION_PREFIX)
        self.ofport_int_to_snat = self.int_br.get_port_ofport(port_name)
        self.ofport_snat_to_int = self.agent_api.phys_ofports['meta']

        self.metadata_gateway_ip = str(netaddr.IPAddress(
            netaddr.IPNetwork(cfg.CONF.METADATA.meta_cidr).first + 1))

        self.meta_dev_vlan_id = cfg.CONF.METADATA.meta_dev_vlan_id
        self.meta_cidr = cfg.CONF.METADATA.meta_cidr

    def init_snat_br_networking_path(self):
        self.metadata_host_info = {
            "gateway_ip": self.metadata_gateway_ip,
            "provider_ip": self.metadata_gateway_ip,
            "mac_address": metadata_path.DEFAULT_META_GATEWAY_MAC,
            "service_protocol_port": cfg.CONF.METADATA.host_proxy_listen_port,
            "enable_metrics_proxy": cfg.CONF.METADATA.enable_metrics_proxy,
            "metrics_listen_port": cfg.CONF.METADATA.metrics_listen_port}
        self.metadata_ofport = self.meta_br.get_port_ofport("tap-meta")
        self.meta_br.network_path_defaults(
            pvid=self.meta_dev_vlan_id,
            to_int_ofport=self.ofport_snat_to_int,
            metadata_ofport=self.metadata_ofport,
            metadata_host_info=self.metadata_host_info)
        self.meta_br.install_goto(p_const.NP_EGRESS_NAT_CLASSIFY,
                                  table_id=p_const.NP_EGRESS_DEST_MAC_LEARN)

    def process_install_dhcp_arp_responder(self, port_info):
        filters = {'network_id': (port_info['network_id'], ),
                   'device_owner': (constants.DEVICE_OWNER_DHCP, )}
        dhcp_ports = self.rcache_api.get_resources(resources.PORT, filters)
        for p in dhcp_ports:
            for ip in p['fixed_ips']:
                ip_addr = netaddr.IPNetwork(str(ip.ip_address))
                if ip_addr.version != constants.IP_VERSION_4:
                    continue
                self.install_arp_responder(
                    bridge=self.int_br,
                    ip=str(ip.ip_address),
                    mac=str(p.mac_address),
                    table=p_const.ARP_SPOOF_TABLE,
                    in_port=port_info["ofport"])

    def prepare_flow(self, port_info):
        self.init_snat_br_networking_path()
        self.install_arp_responder(
            bridge=self.int_br,
            ip=comm_consts.METADATA_DEFAULT_IP,
            mac=comm_consts.METADATA_DEFAULT_MAC,
            table=p_const.TRANSIENT_TABLE)
        self.meta_br.setup_default_table()

        # handle port
        provider_ip = self.int_br.get_value_from_other_config(
            port_info['port_name'], 'provider_ip')
        provider_mac = self.int_br.get_value_from_other_config(
            port_info['port_name'], 'provider_mac')

        ins_info = self.ext_api.get_provider_ip_info(
            port_info['port_id'], provider_ip, provider_mac)
        if not ins_info:
            return
        self.instance_infos[port_info['port_id']] = ins_info
        port_info.update({"fixed_ips": self.verify_port_pfn_ip(port_info)})
        self.process_install_netwoking_path_flows(port_info)
        self.process_install_dhcp_arp_responder(port_info)

    def do_check(self, context, result_map, ports):
        for port in ports:
            if not port.device_owner.startswith(
                    constants.DEVICE_OWNER_COMPUTE_PREFIX):
                return
            reports = result_map[port.id]['metadata_path']
            port_name = utils.get_port_name_by_id(self.int_br, port.id)
            if not port_name:
                reports.add('port %s not found on bridge %s',
                            port.id, self.int_br.br_name)
                continue
            vlan = self.int_br.get_port_tag_by_name(port_name)
            port_ofport = self.int_br.get_port_ofport(port_name)
            port_info = {"port_id": port.id,
                         "device_owner": port.device_owner,
                         "port_name": port_name,
                         "vlan": vlan,
                         "mac_address": str(port.mac_address),
                         "fixed_ips": [{'ip_address': str(ip['ip_address']),
                                        'subnet_id': ip['subnet_id']}
                                       for ip in port.fixed_ips],
                         "network_id": port.network_id,
                         "ofport": port_ofport}
            self.clear()
            self.prepare_flow(port_info)
            reports.extend(list(self.reports))
