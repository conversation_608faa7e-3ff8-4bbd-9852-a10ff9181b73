#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import itertools
import struct

import netaddr
from neutron_lib.agent import l2_extension as l2_agent_extension
from neutron_lib import constants
from oslo_config import cfg
from oslo_log import log as logging
from oslo_service import loopingcall
import six

from ryu.base import app_manager
from ryu.controller import handler
from ryu.controller import ofp_event
from ryu.lib.packet import ether_types
from ryu.lib.packet import ethernet
from ryu.lib.packet import icmpv6
from ryu.lib.packet import in_proto
from ryu.lib.packet import ipv6
from ryu.lib.packet import packet
from ryu.ofproto import inet
from ryu.ofproto import ofproto_v1_3

from neutron.api.rpc.callbacks import resources
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_consts

LOG = logging.getLogger(__name__)

RA_BD_IPADDR = "ff02::1"
RA_BD_MAC = "33:33:00:00:00:01"


@icmpv6.nd_router_advert.register_nd_option_type
class nd_option_mtu(icmpv6.nd_option):
    """ICMPv6 sub encoder/decoder class for Neighbor discovery
    MTU Option. (RFC 4861)
    This is used with ryu.lib.packet.icmpv6.nd_router_advert.
    An instance has the following attributes at least.
    Most of them are same to the on-wire counterparts but in host byte order.
    __init__ takes the corresponding args in this order.
    .. tabularcolumns:: |l|p{35em}|
    ============== ====================
    Attribute      Description
    ============== ====================
    mtu            MTU.
    ============== ====================
    """

    _PACK_STR = '!BBHI'
    _MIN_LEN = struct.calcsize(_PACK_STR)
    _OPTION_LEN = _MIN_LEN // 8

    @classmethod
    def option_type(cls):
        return icmpv6.ND_OPTION_MTU

    def __init__(self, mtu=1500):
        super(nd_option_mtu, self).__init__(self.option_type(), 0)
        self.mtu = mtu

    @classmethod
    def parser(cls, buf, offset):
        (_x, _y, _z, mtu) = struct.unpack_from(cls._PACK_STR, buf, offset)
        msg = cls(mtu)
        return msg

    def serialize(self):
        buf = bytearray(struct.pack(
            self._PACK_STR, self.option_type(), self._OPTION_LEN, 0, self.mtu))
        if 0 == self.length:
            self.length = len(buf) // 8
        return six.binary_type(buf)


class NDPRaProcessor(app_manager.RyuApp):
    OFP_VERSIONS = [ofproto_v1_3.OFP_VERSION]

    def __init__(self, extension, *args, **kwargs):
        super(NDPRaProcessor, self).__init__(*args, **kwargs)
        self.extension = extension

    @handler.set_ev_cls(ofp_event.EventOFPPacketIn, handler.MAIN_DISPATCHER)
    def _packet_in_handler(self, ev):
        msg = ev.msg
        datapath = msg.datapath
        ofproto = datapath.ofproto

        if msg.reason == ofproto.OFPR_NO_MATCH:
            reason = 'NO MATCH'
        elif msg.reason == ofproto.OFPR_ACTION:
            reason = 'ACTION'
        elif msg.reason == ofproto.OFPR_INVALID_TTL:
            reason = 'INVALID TTL'
        else:
            reason = 'unknown'

        of_in_port = msg.match['in_port']
        LOG.info("NDPRaProcessor packet_in port: %s", of_in_port)
        pkt = packet.Packet(data=msg.data)

        LOG.debug('NDPRaProcessor OFPPacketIn received: '
                  'buffer_id=%x total_len=%d reason=%s '
                  'table_id=%d cookie=%d match=%s pkt=%s',
                  msg.buffer_id, msg.total_len, reason,
                  msg.table_id, msg.cookie, msg.match,
                  pkt)

        eth_pkt = pkt.get_protocol(ethernet.ethernet)
        try:
            ip6_pkt = pkt.get_protocol(ipv6.ipv6)
            icmpv6_pkt = pkt.get_protocol(icmpv6.icmpv6)
            if ip6_pkt and icmpv6_pkt:
                if icmpv6_pkt.type_ == icmpv6.ND_ROUTER_SOLICIT:
                    na = icmpv6_pkt.data
                    if (isinstance(na, icmpv6.nd_router_solicit) and
                            isinstance(na.option, icmpv6.nd_option_sla)):
                        self.extension.response_ip6_ra(of_in_port,
                                                       eth_pkt.src)
        except Exception as e:
            LOG.warning("NDPRaProcessor failed to process packet-in: %s", e)

    def get_subnet_ra_info(self, subnet):
        info = {'ipv6_ra_mode': subnet['ipv6_ra_mode'],
                'ipv6_address_mode': subnet['ipv6_address_mode']}
        if (subnet['ipv6_ra_mode'] == constants.IPV6_SLAAC or
                subnet['ipv6_ra_mode'] == constants.DHCPV6_STATELESS):
            info['auto_config_prefixes'] = subnet['cidr']
        if subnet['ipv6_ra_mode'] == constants.DHCPV6_STATEFUL:
            info['stateful_config_prefixes'] = subnet['cidr']
        if subnet['ipv6_ra_mode'] == constants.IPV6_SLAAC:
            info['dns_servers'] = list(itertools.chain(
                *[subnet.get('dns_nameservers', [])]))
        info['gateway_ip'] = subnet['gateway_ip']
        return info

    def get_ndp_packet(self, mtu, port_subnets, subnet, gw_ports):
        ra_config = self.get_subnet_ra_info(subnet)
        if not ra_config['gateway_ip']:
            return
        ret_pkt = packet.Packet()
        source_mac = cfg.CONF.base_mac
        for p in gw_ports or []:
            for ip in p['fixed_ips']:
                if (ip['subnet_id'] == subnet['id'] and
                        ip['subnet_id'] in port_subnets):
                    source_mac = str(p['mac_address'])
                    break
        ret_pkt.add_protocol(ethernet.ethernet(
            ethertype=ether_types.ETH_TYPE_IPV6,
            dst=RA_BD_MAC,
            src=source_mac))
        ipv6_hdr = ipv6.ipv6(
            src=str(netaddr.EUI(source_mac).ipv6_link_local()),
            dst=RA_BD_IPADDR,
            nxt=inet.IPPROTO_ICMPV6
        )
        ret_pkt.add_protocol(ipv6_hdr)

        options = []
        option_mtu = nd_option_mtu(mtu or 1500)
        options.append(option_mtu)

        prefix = ra_config.get("auto_config_prefixes")
        if prefix:
            net = netaddr.IPNetwork(prefix)
            # Flag: 0xc0, On-link flag(L)
            #     1... .... = On-link flag(L): Set
            #     .1.. .... = Autonomous address-configuration flag(A): Not set
            #     ..0. .... = Router address flag(R): Not set
            #     ...0 0000 = Reserved: 0
            res1 = 6   # 6 << 5 = 0xc0
            option_pi = icmpv6.nd_option_pi(
                pl=net.prefixlen,
                res1=res1,       # L,A,R\* Flags for Prefix Information.
                val_l=86400,     # Valid Lifetime.
                pre_l=14400,     # Preferred Lifetime.
                prefix=str(prefix).split('/')[0])
            options.append(option_pi)

        prefix = ra_config.get("stateful_config_prefixes")
        if prefix:
            net = netaddr.IPNetwork(prefix)
            # Flag: 0x80, On-link flag(L)
            #     1... .... = On-link flag(L): Set
            #     .0.. .... = Autonomous address-configuration flag(A): Not set
            #     ..0. .... = Router address flag(R): Not set
            #     ...0 0000 = Reserved: 0
            res1 = 4   # 4 << 5 = 0x80
            option_pi = icmpv6.nd_option_pi(
                pl=net.prefixlen,
                res1=res1,       # L,A,R\* Flags for Prefix Information.
                val_l=86400,     # Valid Lifetime.
                pre_l=14400,     # Preferred Lifetime.
                prefix=str(prefix).split('/')[0])
            options.append(option_pi)

        option_sla = icmpv6.nd_option_sla(hw_src=source_mac)
        options.append(option_sla)

        # Flags: 0x00, Managed address configuration,
        #              Prf (Default Router Preference): Medium
        #     0... .... = Managed address configuration: Set
        #     .0.. .... = Other configuration: Not set
        #     ..0. .... = Home Agent: Not set
        #     ...0 0... = Prf (Default Router Preference): Medium (0)
        #     .... .0.. = Proxy: Not set
        #     .... ..0. = Reserved: 0
        if constants.DHCPV6_STATELESS == ra_config.get('ipv6_ra_mode'):
            res = 1    # 1 << 6 0x40
        elif constants.DHCPV6_STATEFUL == ra_config.get('ipv6_ra_mode'):
            res = 2   # 1 << 6  0x80
        else:
            res = 0   # 0 << 6  0x00

        icmpv6_hdr = icmpv6.icmpv6(
            type_=icmpv6.ND_ROUTER_ADVERT,
            code=0,
            data=icmpv6.nd_router_advert(
                ch_l=64,     # Cur Hop Limit.
                res=res,     # M,O Flags for Router Advertisement.
                rou_l=300,   # TODO(liuyulong) Router Lifetime.
                rea_t=0,     # Reachable Time.
                ret_t=0,     # Retrans Timer.
                options=options)
        )
        ret_pkt.add_protocol(icmpv6_hdr)

        return ret_pkt

    def send_ra_output(self, datapath, port, mtu, v6_subnets, gw_ports):
        ofproto = datapath.ofproto
        parser = datapath.ofproto_parser
        port_ofport = port['vif_port'].ofport
        port_subnets = [ip['subnet_id'] for ip in port['fixed_ips']]
        for subnet in v6_subnets:
            pkt = self.get_ndp_packet(mtu, port_subnets, subnet, gw_ports)
            if not pkt:
                continue
            pkt.serialize()
            LOG.debug("Router Advertisement packet_out %s", (pkt,))
            data = pkt.data
            actions = [parser.OFPActionOutput(port_ofport, 0)]
            out = parser.OFPPacketOut(datapath=datapath,
                                      buffer_id=ofproto.OFP_NO_BUFFER,
                                      in_port=ofproto.OFPP_CONTROLLER,
                                      actions=actions,
                                      data=data)
            LOG.info("Router Advertisement with output to "
                     "port %s of_port %s.",
                     port['port_id'],
                     port_ofport)
            datapath.send_msg(out)


class RASpeakerAgentExtension(
        l2_agent_extension.L2AgentExtension):

    PORT_INFO_CACHE = {}
    OF_PORT = {}

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        self.int_br = self.agent_api.request_int_br()

        app_mgr = app_manager.AppManager.get_instance()

        try:
            # Idempotent for extension initialize:
            # Ryu app_manager will check if the app is already in the list.
            # So remove it first without error check.
            app_mgr.uninstantiate(NDPRaProcessor.__name__)
        except Exception as e:
            LOG.info("Ryu app_manager uninstantiate app %s failure: %s",
                     NDPRaProcessor.__name__, e)

        self.ra_processor = app_mgr.instantiate(NDPRaProcessor, self)
        self.ra_processor.start()

        self.ra_loop = loopingcall.FixedIntervalLoopingCall(
            self.send_ip6_ra_loop)
        self.ra_loop.start(interval=60,
                           initial_delay=60,
                           stop_on_exception=False)

    def consume_api(self, agent_api):
        """Allows an extension to gain access to resources internal to the
           neutron agent and otherwise unavailable to the extension.
        """
        self.agent_api = agent_api
        self.plugin_rpc = agent_api.plugin_rpc
        self.rcache_api = agent_api.plugin_rpc.remote_resource_cache

    def get_network_subnet_infos(self, port):
        subnet_ids = [p['subnet_id'] for p in port['fixed_ips']
                      if netaddr.IPNetwork(p['ip_address']).version == 6]
        network = self.rcache_api.get_resource_by_id(
            resources.NETWORK, port['network_id'])
        v6_subnets = [self.rcache_api.get_resource_by_id(
            resources.SUBNET, s_id) for s_id in subnet_ids]

        filters = {'network_id': (port['network_id'], ),
                   'device_owner': (constants.DEVICE_OWNER_ROUTER_INTF,
                                    constants.DEVICE_OWNER_HA_REPLICATED_INT,
                                    constants.DEVICE_OWNER_DVR_INTERFACE)}
        v6_gateway_ports = self.rcache_api.get_resources(
            resources.PORT, filters)

        return network.get('mtu', 1450), v6_subnets, v6_gateway_ports

    def send_ip6_ra_loop(self):
        for _port_id, port in self.PORT_INFO_CACHE.items():
            mtu, v6_subnets, gw_ports = self.get_network_subnet_infos(port)
            self.send_ip6_ra(port, mtu, v6_subnets, gw_ports)

    def response_ip6_ra(self, ofport, mac):
        dp, _ofp, _ofpp = self.int_br._get_dp()
        port_id = None
        for port_id, ofpt in self.OF_PORT.items():
            if ofpt == ofport:
                port = self.PORT_INFO_CACHE.get(port_id)
                if port and port['mac_address'] == mac:
                    mtu, subnets, gw_ports = (
                        self.get_network_subnet_infos(port))
                    self.ra_processor.send_ra_output(
                        dp, port, mtu, subnets, gw_ports)

    def send_ip6_ra(self, port, mtu, v6_subnets, gw_ports):
        dp, _ofp, _ofpp = self.int_br._get_dp()
        self.ra_processor.send_ra_output(
            dp, port, mtu, v6_subnets, gw_ports)

    def get_of_table(self):
        if cfg.CONF.AGENT.explicitly_egress_direct:
            return p_consts.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
        else:
            return p_consts.TRANSIENT_TABLE

    def add_ipv6_ns_flow(self, ofport, port_mac):
        (_dp, ofp, ofpp) = self.int_br._get_dp()
        match = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6,
                              ip_proto=in_proto.IPPROTO_ICMPV6,
                              in_port=ofport,
                              icmpv6_type=icmpv6.ND_ROUTER_SOLICIT,
                              eth_src=port_mac)
        actions = [
            ofpp.OFPActionOutput(ofp.OFPP_CONTROLLER, 0),
        ]
        instructions = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions),
        ]
        self.int_br.install_instructions(table_id=self.get_of_table(),
                                         priority=150,
                                         instructions=instructions,
                                         match=match)

    def remove_ipv6_ns_flow(self, ofport, port_mac):
        self.int_br.uninstall_flows(
            table_id=self.get_of_table(),
            eth_type=ether_types.ETH_TYPE_IPV6,
            ip_proto=in_proto.IPPROTO_ICMPV6,
            in_port=ofport,
            icmpv6_type=icmpv6.ND_ROUTER_SOLICIT,
            eth_src=port_mac)

    def handle_port(self, context, port_detail):
        device_owner = port_detail['device_owner']
        if not device_owner.startswith(constants.DEVICE_OWNER_COMPUTE_PREFIX):
            return

        vif_port = port_detail['vif_port']
        LOG.info("RA speaker extension add NDP related flows for port %s",
                 port_detail['port_id'])
        self.add_ipv6_ns_flow(vif_port.ofport, vif_port.vif_mac)

        self.PORT_INFO_CACHE[port_detail['port_id']] = port_detail
        self.OF_PORT[port_detail['port_id']] = vif_port.ofport

        # Send RA for it once
        mtu, v6_subnets, gw_ports = self.get_network_subnet_infos(port_detail)
        self.send_ip6_ra(port_detail, mtu, v6_subnets, gw_ports)

    def delete_port(self, context, port_detail):
        port = self.PORT_INFO_CACHE.pop(port_detail['port_id'], None)
        if not port:
            return
        self.OF_PORT.pop(port_detail['port_id'], None)
        vif_port = port['vif_port']
        self.remove_ipv6_ns_flow(vif_port.ofport, vif_port.vif_mac)
