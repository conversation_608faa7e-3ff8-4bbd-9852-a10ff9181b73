# Copyright (c) 2024 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import base64
import collections
import copy
import hashlib

import re

import netaddr

from neutron_lib.agent import l2_extension
from neutron_lib import constants
from neutron_lib import context as _context
from neutron_lib.plugins import utils as p_utils
from oslo_concurrency import lockutils
from oslo_config import cfg
from oslo_log import log as logging
from oslo_serialization import jsonutils
from oslo_service import loopingcall
from osprofiler import profiler

from neutron.agent.common import ovs_lib
from neutron.agent.linux import interface as interface_driver
from neutron.agent.linux import ip_lib
from neutron.common import rpc as n_rpc
from neutron.common import utils as neutron_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common import (constants as
                                                                  ovs_consts)
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.ovs_ofctl \
    import br_tun

from neutron.api.rpc.callbacks.consumer import registry
from neutron.api.rpc.callbacks import events
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc

LOG = logging.getLogger(__name__)

LOCAL_SWITCHING_TBL = ovs_consts.LOCAL_SWITCHING
TRANSIENT_TBL = ovs_consts.TRANSIENT_TABLE
TRAFFIC_MIRROR_TBL = ovs_consts.TRAFFIC_MIRROR
SELECT_TABLE = ovs_consts.BASE_EGRESS_TABLE
MIRROR_FILTER_TBL = ovs_consts.TRAFFIC_MIRROR_FILTER_TABLE
METER_BANDWIDTH_TBL = ovs_consts.TRAFFIC_MIRROR_METER_BANDWIDTH_TABLE
METER_PPS_TBL = ovs_consts.TRAFFIC_MIRROR_METER_PPS_TABLE
OUTPUT_TBL = ovs_consts.TRAFFIC_MIRROR_OUTPUT_TABLE
MIN_PRIORITY = 100
MAX_PRIORITY = 200
DEFAULT_PRIORITY = 1
UDP_VXLAN_HEADER_LENGTH = 50
TRAFFIC_LOCAL_SWITCHING = 0
DEFAULT_VXLAN_VERSION = 4
ARP_ALLOW_PRI = 230
ARP_FORBIDDEN_PRI = 220
MUL_FORBIDDEN_PRI = 220

# Match packets with an 802.1Q header, regardless of VLAN and priority values.
VLAN_TCI_MATCH_WITH_VLAN = "0x1000/0x1000"
# Match packets with no 802.1Q header or tagged with VLAN 0
VLAN_TCI_MATCH_WITHOUT_VLAN = "0x0000/0x0fff"

# traffic mirror vxlan port name
TRAFFIC_MIRROR_PORT = 'mirror'
VLAN_NET_TYPE = 'vlan'
VXLAN_NET_TYPE = 'vxlan'
OPENFLOW13 = "OpenFlow13"
ACCEPT_ACTION = 'accept'
DENY_ACTION = 'reject'
TRANSPORT_PROTOCOL = ['tcp', 'udp']
INGRESS = 'ingress'
EGRESS = 'egress'
IPv4 = 'IPv4'
IPv6 = 'IPv6'
IP_FLOW_TYPE = 'ip'
IP6_FLOW_TYPE = 'ip6'
ETH_PROTOCOL_TABLE = {IPv4: "0x0800", IPv6: "0x86dd"}
IP_PROTOCOL_TABLE = {
    constants.PROTO_NAME_TCP: constants.PROTO_NUM_TCP,
    constants.PROTO_NAME_ICMP: constants.PROTO_NUM_ICMP,
    constants.PROTO_NAME_IPV6_ICMP: constants.PROTO_NUM_IPV6_ICMP,
    constants.PROTO_NAME_UDP: constants.PROTO_NUM_UDP
}


class TrafficMirrorFilterMapping(object):
    def __init__(self):
        """
        filter_session_maps = {
            tm_filter_id_1: set(tm_session_1, tm_session_2),
            tm_filter_id_2: set(tm_session_3, tm_session_4)
        }
        """
        self.filter_session_maps = collections.defaultdict(set)

    def add_filter_session(self, filter_id, tm_session_id):
        self.filter_session_maps[filter_id].add(tm_session_id)

    def remove_filter_session(self, filter_id, tm_session_id):
        if filter_id in self.filter_session_maps.keys(
        ) and tm_session_id in self.filter_session_maps[filter_id]:
            self.filter_session_maps[filter_id].remove(tm_session_id)

    def remove_filter(self, filter_id):
        self.filter_session_maps.pop(filter_id, None)

    def get_tm_session_by_filter(self, filter_id):
        return self.filter_session_maps.get(filter_id)


@profiler.trace_cls("traffic_mirror_agent")
class TrafficMirrorAgent(object):
    '''Implement traffic mirror agent based on the OVS bridge'''
    @property
    def enable_traffic_mirror(self):
        return self.conf.enable_traffic_mirror

    @property
    def local_ip(self):
        if not self.conf.local_ip or not ip_lib.IPWrapper().get_device_by_ip(
                self.conf.local_ip):
            LOG.error(
                "TRAFFIC_MIRROR can't be enabled with invalid local_ip '%s',"
                "IP couldn't be found on this host's interfaces.",
                self.conf.local_ip)
            raise SystemExit(1)
        else:
            return self.conf.local_ip

    @property
    def carrier_network_type(self):
        return self.conf.carrier_network_type

    @property
    def traffic_bridge(self):
        return self.conf.traffic_bridge

    @property
    def zone(self):
        return cfg.CONF.AGENT.availability_zone

    @property
    def int_patch_port(self):
        return self.conf.int_peer_patch_port

    @property
    def traffic_patch_port(self):
        return self.conf.traffic_peer_patch_port

    @property
    def int_patch_ofport(self):
        ofport = self.br_int.get_port_ofport(self.conf.int_peer_patch_port)
        return ovs_lib.INVALID_OFPORT if ofport is None else ofport

    @property
    def traffic_patch_ofport(self):
        ofport = self.br_mirror.get_port_ofport(
            self.conf.traffic_peer_patch_port)
        return ovs_lib.INVALID_OFPORT if ofport is None else ofport

    @property
    def vxlan_udp_port(self):
        return cfg.CONF.AGENT.vxlan_udp_port

    @property
    def dont_fragment(self):
        return cfg.CONF.AGENT.dont_fragment

    @property
    def tunnel_csum(self):
        return cfg.CONF.AGENT.tunnel_csum

    @property
    def vxlan_mxl_offload(self):
        return cfg.CONF.AGENT.vxlan_mlx_offload

    @property
    def tos(self):
        return ('inherit' if cfg.CONF.AGENT.dscp_inherit else (
            int(cfg.CONF.AGENT.dscp) << 2 if cfg.CONF.AGENT.dscp else None))

    def get_pfn_subnets(self):
        self.pfn_net = self.agent_api.plugin_rpc.get_privatefloating_info(
            self.context, agent_id=self.agent_id, host=cfg.CONF.host)
        self.pfn_subnets = set()
        for subnet in self.pfn_net.get('privatefloating_network',
                                       {}).get('subnets_detail', []):
            self.pfn_subnets.add(subnet['id'])

    def is_pfn_subnet(self, subnet_id):
        if subnet_id and subnet_id in self.pfn_subnets:
            return True
        return False

    def dump_table_action_features(self):
        res = self.br_int.run_ofctl("dump-table-features", [])
        pattern = r'actions:.*$'
        result = re.search(pattern, res, re.MULTILINE)
        if result:
            return result.group(0)
        else:
            return ''

    def support_add_vxlan(self):
        return self._support_add_vxlan and self.conf.use_add_vxlan

    @property
    def firewall_driver(self):
        return cfg.CONF.SECURITYGROUP.firewall_driver

    @property
    def state_driver(self):
        return self.firewall_driver == 'openvswitch'

    @property
    def stateless_driver(self):
        return self.firewall_driver == 'openvswitch_stateless'

    def tunnel_port_name(self, remote_ip, key=0):
        hash_len = constants.DEVICE_NAME_MAX_LEN - len(TRAFFIC_MIRROR_PORT) - 1
        addr_num = str(int(netaddr.IPAddress(remote_ip)) + int(key))
        sha1 = hashlib.sha1(addr_num.encode())
        addr_value = base64.b32encode(sha1.digest())
        port_hash = addr_value[:hash_len].decode().lower()
        return '%s-%s' % (TRAFFIC_MIRROR_PORT, port_hash)

    def phy_patch_port_name(self, phy_bridge):
        if not phy_bridge:
            return None
        return p_utils.get_interface_name(
            phy_bridge, prefix=ovs_consts.PEER_INTEGRATION_PREFIX)

    def phy_patch_port(self, phy_bridge):
        if not phy_bridge:
            return None
        patch_port_name = self.phy_patch_port_name(phy_bridge)
        return self.br_int.get_port_ofport(patch_port_name)

    @property
    def tun_patch_port_name(self):
        return cfg.CONF.OVS.int_peer_patch_port

    @property
    def tun_patch_port(self):
        return self.br_int.get_port_ofport(self.tun_patch_port_name)

    @staticmethod
    def set_priority(priority=0):
        return min(MIN_PRIORITY + int(priority), MAX_PRIORITY)

    def _set_br_protocol(self):
        self.br_int.use_at_least_protocol(OPENFLOW13)
        self.br_mirror.use_at_least_protocol(OPENFLOW13)

    def create_tunnel_port(self, remote_ip, segmentation_id=None):
        """Set up traffic vxlan port in integration bridge to send or receive
        traffic flows.
        """
        # ovs-vsctl add-port br-int vxlan0 -- set interface vxlan0
        # type=vxlan options:remote ip=*************** options:key=99999
        try:
            port_name = self.tunnel_port_name(remote_ip, segmentation_id)
            existed = self.br_mirror.port_exists(port_name)
            if existed:
                return self.get_ofport_by_name(port_name)
            ofport = self.br_mirror.add_tunnel_port(port_name,
                                                    remote_ip,
                                                    self.local_ip,
                                                    VXLAN_NET_TYPE,
                                                    self.vxlan_udp_port,
                                                    self.dont_fragment,
                                                    self.tunnel_csum,
                                                    self.tos,
                                                    self.vxlan_mxl_offload,
                                                    key=str(segmentation_id))
            return ofport
        except Exception as ex:
            LOG.error("Failed to setup tunnel port, raise : %s", str(ex))
            return ovs_lib.INVALID_OFPORT

    def delete_tunnel_port(self, remote_ip, segmentation_id=None):
        port_name = self.tunnel_port_name(remote_ip, segmentation_id)
        if not self.br_mirror.port_exists(port_name):
            return
        else:
            self.br_mirror.delete_port(port_name)

    def get_ofport_by_name(self, port_name):
        ofport = self.br_mirror.get_port_ofport(port_name)
        return ovs_lib.INVALID_OFPORT if ofport is None else ofport

    def _write_proto(self, eth_type, protocol=None):
        if protocol == "arp":
            return "arp"
        proto_str = "eth_type=%s" % ETH_PROTOCOL_TABLE[eth_type]
        if protocol in IP_PROTOCOL_TABLE.keys():
            proto_num = IP_PROTOCOL_TABLE[protocol]
            if protocol == constants.PROTO_NAME_ICMP and eth_type == 'IPv6':
                proto_num = constants.PROTO_NUM_IPV6_ICMP
            proto_str += ",ip_proto=%s" % proto_num
        return proto_str

    def _add_flow(self, **kwargs):
        dl_type = kwargs.get('dl_type')
        if isinstance(dl_type, int):
            kwargs['dl_type'] = "0x{:04x}".format(dl_type)
        self.br_int.add_flow(**kwargs)

    def _delete_flow(self, **kwargs):
        self.br_int.delete_flows(**kwargs)

    def _add_traffic_flow(self, **kwargs):
        dl_type = kwargs.get('dl_type')
        if isinstance(dl_type, int):
            kwargs['dl_type'] = "0x{:04x}".format(dl_type)
        self.br_mirror.add_flow(**kwargs)

    def _delete_traffic_flow(self, **kwargs):
        self.br_mirror.delete_flows(**kwargs)

    def get_port_ranges(self, protocol, min_port_range, max_port_range):
        min_port_range = int(min_port_range)
        max_port_range = int(max_port_range)
        if protocol in TRANSPORT_PROTOCOL:
            if min_port_range != max_port_range:
                port_match = neutron_utils.port_rule_masking(
                    min_port_range, max_port_range)
            else:
                port_match = min_port_range
        else:
            port_match = None
        return port_match

    def install_source_port_flow(self,
                                 direction=INGRESS,
                                 in_port=None,
                                 dl_vlan=None,
                                 dl_src=None,
                                 dl_dst=None,
                                 nw_dst=None,
                                 ipv6_dst=None,
                                 port_security=True,
                                 is_phy_ofport=False,
                                 strip_vlan=False,
                                 priority=0):
        """Install traffic mirror flows to MIRROR_INGRESS_TBL"""
        kwargs = {
            "table": TRAFFIC_MIRROR_TBL,
            "priority": self.set_priority(priority),
        }
        if nw_dst:
            kwargs['priority'] += 10
            kwargs['proto'] = 'ip'
            kwargs['nw_dst'] = nw_dst
        if ipv6_dst:
            kwargs['priority'] += 10
            kwargs['proto'] = 'ip6'
            kwargs['ipv6_dst'] = ipv6_dst
        if in_port:
            kwargs['in_port'] = in_port
        if direction == INGRESS and dl_src:
            kwargs['dl_src'] = dl_src
        if dl_vlan:
            if self.stateless_driver:
                kwargs['dl_vlan'] = dl_vlan
            else:
                kwargs['reg6'] = dl_vlan

        if direction == EGRESS and dl_dst:
            kwargs['dl_dst'] = dl_dst
        if self.state_driver:
            if port_security and is_phy_ofport:
                actions = 'resubmit(,%d),' % ovs_consts.BASE_INGRESS_TABLE
            elif port_security and not is_phy_ofport:
                actions = 'resubmit(,%d),' % ovs_consts.BASE_EGRESS_TABLE
            elif not port_security and direction == INGRESS:
                actions = 'resubmit(,%d)' % ovs_consts.ACCEPT_OR_INGRESS_TABLE
            elif not port_security and is_phy_ofport:
                actions = 'resubmit(,%d),' % (
                    ovs_consts.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE)
            else:
                actions = 'resubmit(,%d),' % (ovs_consts.BASE_EGRESS_TABLE)
        else:
            actions = 'resubmit(,%d),' % SELECT_TABLE
        if dl_vlan and self.stateless_driver and not strip_vlan:
            actions += 'mod_vlan_vid:%s,resubmit(,%d)' % (dl_vlan,
                                                          MIRROR_FILTER_TBL)
        elif dl_vlan and self.stateless_driver and strip_vlan:
            actions += 'strip_vlan,resubmit(,%d)' % MIRROR_FILTER_TBL
        elif dl_vlan:
            actions += 'load:%d->NXM_NX_REG6[],resubmit(,%d)' % (
                dl_vlan, MIRROR_FILTER_TBL)
        else:
            actions += 'resubmit(,%d)' % MIRROR_FILTER_TBL
        kwargs['actions'] = actions
        self._add_flow(**kwargs)
        if (not port_security and self.state_driver and dl_vlan and
                is_phy_ofport):
            kwargs.pop('reg6', None)
            kwargs['dl_vlan'] = dl_vlan
            self._add_flow(**kwargs)
        if (not port_security and self.stateless_driver and dl_vlan and
                in_port):
            kwargs.pop('dl_vlan', None)
            kwargs['reg6'] = dl_vlan
            kwargs['priority'] -= 10
            actions = kwargs['actions'].replace('strip_vlan,', '')
            kwargs['actions'] = actions
            self._add_flow(**kwargs)

    def uninstall_source_port_flow(self,
                                   direction=INGRESS,
                                   in_port=None,
                                   dl_src=None,
                                   dl_dst=None,
                                   dl_vlan=None,
                                   port_security=True):
        kwargs = {
            "table": TRAFFIC_MIRROR_TBL,
        }
        if in_port:
            kwargs['in_port'] = in_port
        if direction == INGRESS and dl_src:
            kwargs['dl_src'] = dl_src

        if direction == EGRESS and dl_dst:
            kwargs['dl_dst'] = dl_dst
        if dl_vlan:
            if self.stateless_driver:
                kwargs['dl_vlan'] = dl_vlan
            else:
                kwargs['reg6'] = dl_vlan
        self._delete_flow(**kwargs)
        if not port_security and self.state_driver and dl_vlan:
            kwargs.pop('reg6', None)
            kwargs['dl_vlan'] = dl_vlan
            self._delete_flow(**kwargs)
            kwargs['reg6'] = dl_vlan
            self._delete_flow(**kwargs)
        if (not port_security and self.stateless_driver and dl_vlan and
                in_port):
            kwargs.pop('dl_vlan', None)
            kwargs['reg6'] = dl_vlan
            self._delete_flow(**kwargs)
            kwargs.pop('reg6', None)
            kwargs['dl_vlan'] = dl_vlan
            self._delete_flow(**kwargs)

    def set_meter_bandwidth_flows(self,
                                  in_port,
                                  meter_id,
                                  dl_src=None,
                                  dl_dst=None):
        """Set bandwidth limit to mirror traffic by meter flows
        """
        kwargs = {
            "table": METER_BANDWIDTH_TBL,
            "priority": self.set_priority(),
            "actions": "meter:%s,goto_table:%d" % (meter_id, METER_PPS_TBL)
        }
        if not in_port or not meter_id:
            return
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        if meter_id:
            kwargs['actions'] = "%s,goto_table:%d" % (meter_id, METER_PPS_TBL)
        else:
            kwargs['actions'] = 'goto_tables:%d' % METER_PPS_TBL
        self._add_flow(**kwargs)

    def unset_meter_bandwidth_flows(self,
                                    in_port,
                                    meter_id,
                                    dl_src=None,
                                    dl_dst=None):
        if not in_port or not meter_id:
            return
        kwargs = {
            "table": METER_BANDWIDTH_TBL,
            "in_port": in_port,
        }
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        self._delete_flow(**kwargs)

    def set_meter_pps_flows(self, in_port, meter_id, dl_src=None, dl_dst=None):
        """Set pps limit to mirror traffic by meter flows"""
        if not meter_id or not in_port:
            return
        kwargs = {
            'table': METER_PPS_TBL,
            'priority': self.set_priority(),
            'actions': 'meter:%s,goto_table:%d' % (meter_id, MIRROR_FILTER_TBL)
        }
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        self._add_flow(**kwargs)

    def unset_meter_pps_flows(self, in_port, meter_id, dl_src, dl_dst):
        if not meter_id or not in_port:
            return
        kwargs = {
            'table': METER_PPS_TBL,
        }
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        self._delete_flow(**kwargs)

    def install_filter_flows(self,
                             in_port=None,
                             ethertype=IPv4,
                             protocol='all',
                             direction=INGRESS,
                             mac_addr=None,
                             src_cidr=None,
                             dst_cidr=None,
                             src_port_range_min=None,
                             src_port_range_max=None,
                             dst_port_range_min=None,
                             dst_port_range_max=None,
                             vlan_tci=None,
                             dl_vlan=None,
                             priority=0,
                             action=ACCEPT_ACTION):
        """Install traffic mirror filter flows"""
        kwargs = {
            'table': MIRROR_FILTER_TBL,
            'priority': self.set_priority(priority),
        }
        if direction == EGRESS and in_port:
            kwargs['in_port'] = in_port

        if direction == INGRESS and mac_addr:
            kwargs['dl_dst'] = mac_addr
        if direction == EGRESS and mac_addr:
            kwargs['dl_src'] = mac_addr
        if direction == INGRESS and vlan_tci:
            kwargs['vlan_tci'] = vlan_tci
        if direction == INGRESS and dl_vlan:
            if self.state_driver:
                kwargs.pop('vlan_tci', None)
                kwargs['reg6'] = dl_vlan
            else:
                kwargs['dl_vlan'] = dl_vlan
        if ethertype == IPv4:
            if protocol == 'all':
                kwargs['proto'] = 'ip'
            else:
                kwargs['proto'] = self._write_proto(ethertype, protocol)
            if src_cidr:
                kwargs['nw_src'] = src_cidr
            if dst_cidr:
                kwargs['nw_dst'] = dst_cidr
        else:
            if protocol == 'all':
                kwargs['proto'] = 'ip6'
            else:
                kwargs['proto'] = self._write_proto(ethertype, protocol)
            if src_cidr:
                kwargs['ipv6_src'] = src_cidr
            if dst_cidr:
                kwargs['ipv6_dst'] = dst_cidr

        if action == ACCEPT_ACTION:
            kwargs['actions'] = 'goto_table:%d' % METER_BANDWIDTH_TBL
        else:
            kwargs['actions'] = 'drop'

        if protocol in TRANSPORT_PROTOCOL:
            if src_port_range_min != src_port_range_max:
                src_port_match = self.get_port_ranges(protocol,
                                                      src_port_range_min,
                                                      src_port_range_max)
            elif isinstance(src_port_range_min,
                            int) and src_port_range_min == src_port_range_max:
                src_port_match = [src_port_range_min]
            else:
                src_port_match = []
            if dst_port_range_min != dst_port_range_max:
                dst_port_match = self.get_port_ranges(protocol,
                                                      dst_port_range_min,
                                                      dst_port_range_max)
            elif isinstance(dst_port_range_min,
                            int) and dst_port_range_min == dst_port_range_max:
                dst_port_match = [dst_port_range_min]
            else:
                dst_port_match = []

            if src_port_match and dst_port_match:
                for src_port in src_port_match:
                    kwargs['%s_src' % protocol] = src_port
                    for dst_port in dst_port_match:
                        kwargs['%s_dst' % protocol] = dst_port
                        self._add_flow(**kwargs)

            elif src_port_match and not dst_port_match:
                for src_port in src_port_match:
                    kwargs['%s_src' % protocol] = src_port
                    self._add_flow(**kwargs)

            elif not src_port_match and dst_port_match:
                for dst_port in dst_port_match:
                    kwargs['%s_dst' % protocol] = dst_port
                    self._add_flow(**kwargs)

            else:
                self._add_flow(**kwargs)
        else:
            self._add_flow(**kwargs)

    def uninstall_filter_flows(self,
                               direction=INGRESS,
                               mac_addr=None,
                               in_port=None,
                               dl_vlan=None):
        kwargs = {
            'table': MIRROR_FILTER_TBL,
        }
        if direction == INGRESS and mac_addr:
            kwargs['dl_src'] = mac_addr
        elif direction == EGRESS and mac_addr:
            kwargs['dl_dst'] = mac_addr
        if in_port:
            kwargs['in_port'] = in_port
        if dl_vlan:
            kwargs['dl_vlan'] = dl_vlan
        self._delete_flow(**kwargs)

    def install_output_flow_from_br_int(self,
                                        out_port,
                                        same_host,
                                        carrier_network_type=VXLAN_NET_TYPE,
                                        in_port=None,
                                        dl_src=None,
                                        dl_dst=None,
                                        vlan_tci=None,
                                        dl_vlan=None,
                                        mod_vlan_id=None,
                                        mod_dl_src=None,
                                        mod_dl_dst=None,
                                        vxlan_ethertype=IPv4,
                                        vxlan_nw_src=None,
                                        vxlan_nw_dst=None,
                                        vxlan_dl_src=None,
                                        vxlan_dl_dst=None,
                                        vxlan_vni=None,
                                        packet_length=1500):
        """Add network service header and install output flows

        Scenario 1st: the source port and target port resides on the same
        HOST
        (1)Ingress flows:
            table=223,priority=300,ip,nw_dst=***********
                actions=output(port="tap2de740a5-79",max_len=100)
        (2)Egress flows:
            table=223,priority=300,ip,nw_src=***********
                actions=output(port="tap2de740a5-79",max_len=100)

        Scenario 2ed: the source port and target port resides on the different
        HOST, and output to physical device by VLAN packets.
        (1) Ingress flows:
            table=223,ip,nw_dst=***********
                actions=encap(nsh(md_type=2)),
                    encap(ethernet),
                    set_field:fa:16:3e:59:fb:a6->dl_dst,
                    set_field:fa:16:3e:bb:df:b1->dl_src,
                    mod_vlan_vid:5,
                    output(port=int-br-provider,max_len=100)
        (2) Egress flows:
            table=223,ip,nw_src=***********
                actions=encap(nsh(md_type=2)),
                    encap(ethernet),
                    set_field:fa:16:3e:59:fb:a6->dl_dst,
                    set_field:fa:16:3e:bb:df:b1->dl_src,
                    mod_vlan_vid:5,
                    output(port=int-br-provider,max_len=100)
        Scenario 3rd: the source port and target port resides on the different
        HOST, and output to physical device by VXLAN packets.
        (1) Ingress flows:
            table=223,ip,nw_dst=***********,
                actions=output(port=vxlan1,max_len=100)
        (2) Egress flows:
            table=223,ip,nw_src=***********,
                actions=output(port=vxlan1,max_len=100)
        """

        kwargs = {
            'table': OUTPUT_TBL,
            'priority': MIN_PRIORITY,
        }
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        if vlan_tci:
            kwargs['priority'] += 10
            kwargs['vlan_tci'] = vlan_tci
        if dl_vlan:
            kwargs['dl_vlan'] = dl_vlan
            kwargs['priority'] += 10

        actions = ''
        # Only VLAN carrier network process vlan modify actions
        if carrier_network_type == VLAN_NET_TYPE and same_host is False:
            if mod_dl_src:
                actions += 'set_field:%s->dl_src,' % mod_dl_src
            if mod_dl_dst:
                actions += 'set_field:%s->dl_dst,' % mod_dl_dst

        if mod_vlan_id and same_host is False:
            if not (self.state_driver and dl_vlan and dl_dst):
                actions += 'mod_vlan_vid:%s,' % mod_vlan_id

        packet_length = int(packet_length)
        vxlan_dl_type = ETH_PROTOCOL_TABLE.get(vxlan_ethertype)
        vxlan_cond = (vxlan_nw_src and vxlan_nw_dst and vxlan_dl_src and
                      vxlan_dl_dst and isinstance(vxlan_vni, int))
        if same_host and self.support_add_vxlan() and vxlan_cond:
            if dl_vlan:
                actions += 'strip_vlan,'
            actions += ('add_vxlan('
                        'eth(src={src},dst={dst},dl_type={dl_type}),'
                        '{ethertype}(src={nw_src},dst={nw_dst}),'
                        'vxlan(vni=0x{vni:x})),').format(
                            src=vxlan_dl_src,
                            dst=vxlan_dl_dst,
                            dl_type=vxlan_dl_type,
                            ethertype=vxlan_ethertype.lower(),
                            nw_src=vxlan_nw_src,
                            nw_dst=vxlan_nw_dst,
                            vni=int(vxlan_vni))
        if same_host and in_port == out_port:
            kwargs['priority'] += 30
            actions += 'IN_PORT'
        elif same_host:
            actions += 'output(port=%s,max_len=%s)' % (
                out_port, packet_length + UDP_VXLAN_HEADER_LENGTH)
        elif carrier_network_type == VLAN_NET_TYPE:
            if dl_dst and dl_dst == mod_dl_src:
                actions += 'IN_PORT'
            else:
                actions += 'NORMAL'
        else:
            actions += 'output=%s' % out_port
        kwargs['actions'] = actions
        self._add_flow(**kwargs)
        if self.state_driver and dl_vlan and dl_dst and mod_vlan_id:
            kwargs_copy = copy.deepcopy(kwargs)
            kwargs_copy.pop('dl_vlan', None)
            kwargs_copy['reg6'] = dl_vlan
            kwargs_copy['priority'] -= 10
            kwargs_copy['actions'] = ('mod_vlan_vid:%s,' % mod_vlan_id +
                                      kwargs_copy['actions'])
            self._add_flow(**kwargs_copy)

        if self.state_driver and same_host and dl_vlan:
            kwargs_copy = copy.deepcopy(kwargs)
            kwargs_copy.pop('dl_vlan', None)
            kwargs_copy['priority'] -= 10
            kwargs_copy['reg6'] = dl_vlan
            actions = kwargs_copy['actions'].replace('strip_vlan,', '')
            kwargs_copy['actions'] = actions
            self._add_flow(**kwargs_copy)

    def delete_output_flow_from_br_int(self,
                                       dl_src=None,
                                       dl_dst=None,
                                       in_port=None):
        kwargs = {
            'table': OUTPUT_TBL,
        }
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        try:
            self._delete_flow(**kwargs)
        except Exception as ex:
            LOG.warning(ex)

    def add_br_mirror_output_flow(self,
                                  out_port,
                                  output_type=VXLAN_NET_TYPE,
                                  same_host=False,
                                  protocol=None,
                                  direction=INGRESS,
                                  in_port=None,
                                  ip_addr=None,
                                  dl_src=None,
                                  dl_dst=None,
                                  dl_vlan=None,
                                  vxlan_ethertype=IPv4,
                                  vxlan_nw_src=None,
                                  vxlan_nw_dst=None,
                                  vxlan_dl_src=None,
                                  vxlan_dl_dst=None,
                                  vxlan_vni=None,
                                  packet_length=100):
        '''add output flows from br-mirror'''
        if (output_type != VXLAN_NET_TYPE or out_port ==
                ovs_lib.INVALID_OFPORT or same_host is True):
            return
        kwargs = {
            'table': TRAFFIC_LOCAL_SWITCHING,
            'priority': MAX_PRIORITY,
        }
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        if dl_vlan:
            kwargs['priority'] += 10
            kwargs['dl_vlan'] = dl_vlan
        if protocol in [IP_FLOW_TYPE, IP6_FLOW_TYPE]:
            kwargs['proto'] = protocol
        if direction == INGRESS and ip_addr:
            if protocol == IP_FLOW_TYPE:
                kwargs['nw_dst'] = ip_addr
            else:
                kwargs['ipv6_dst'] = ip_addr
        elif direction == EGRESS and ip_addr:
            if protocol == IP_FLOW_TYPE:
                kwargs['nw_src'] = ip_addr
            else:
                kwargs['ipv6_src'] = ip_addr
        actions = ''
        vxlan_dl_type = ETH_PROTOCOL_TABLE.get(vxlan_ethertype)
        if dl_vlan:
            actions += 'strip_vlan,'
        vxlan_cond = (vxlan_nw_src and vxlan_nw_dst and vxlan_dl_src and
                      vxlan_dl_dst and isinstance(vxlan_vni, int))
        if self.support_add_vxlan() and vxlan_cond:
            actions += ('add_vxlan('
                        'eth(src={src},dst={dst},dl_type={dl_type}),'
                        '{ethertype}(src={nw_src},dst={nw_dst}),'
                        'vxlan(vni=0x{vni:x})),').format(
                            src=vxlan_dl_src,
                            dst=vxlan_dl_dst,
                            dl_type=vxlan_dl_type,
                            ethertype=vxlan_ethertype.lower(),
                            nw_src=vxlan_nw_src,
                            nw_dst=vxlan_nw_dst,
                            vni=int(vxlan_vni))
        packet_length = int(packet_length)
        actions += 'output(port=%s,max_len=%s)' % (out_port, packet_length +
                                                   UDP_VXLAN_HEADER_LENGTH)
        kwargs['actions'] = actions
        try:
            self._add_traffic_flow(**kwargs)
        except Exception as ex:
            LOG.warning(ex)

    def delete_br_mirror_output_flow(self,
                                     protocol=IP_FLOW_TYPE,
                                     direction=INGRESS,
                                     in_port=None,
                                     ip_addr=None,
                                     dl_src=None,
                                     dl_dst=None):
        kwargs = {'table': TRAFFIC_LOCAL_SWITCHING}
        if protocol in [IP_FLOW_TYPE, IP6_FLOW_TYPE]:
            kwargs['proto'] = protocol
        if direction == INGRESS and ip_addr:
            if protocol == IP_FLOW_TYPE:
                kwargs['nw_dst'] = ip_addr
            else:
                kwargs['ipv6_dst'] = ip_addr
        elif direction == EGRESS and ip_addr:
            if protocol == IP_FLOW_TYPE:
                kwargs['nw_src'] = ip_addr
            else:
                kwargs['ipv6_src'] = ip_addr
        if in_port:
            kwargs['in_port'] = in_port
        if dl_src:
            kwargs['dl_src'] = dl_src
        if dl_dst:
            kwargs['dl_dst'] = dl_dst
        if protocol in [IP_FLOW_TYPE, IP6_FLOW_TYPE]:
            kwargs['proto'] = protocol
        if direction == INGRESS and ip_addr:
            if protocol == IP_FLOW_TYPE:
                kwargs['nw_dst'] = ip_addr
            else:
                kwargs['ipv6_dst'] = ip_addr
        elif direction == EGRESS and ip_addr:
            if protocol == IP_FLOW_TYPE:
                kwargs['nw_src'] = ip_addr
            else:
                kwargs['ipv6_src'] = ip_addr
        try:
            self._delete_traffic_flow(**kwargs)
        except Exception as ex:
            LOG.warning(ex)

    def install_accept_flow_from_br_mirror(self,
                                           in_port=None,
                                           direction=INGRESS,
                                           mac_addr=None,
                                           tun_id=None,
                                           mod_vlan_id=None,
                                           strip_vlan=False):
        kwargs = {'table': TRAFFIC_LOCAL_SWITCHING, 'priority': 10}
        if in_port:
            kwargs['in_port'] = in_port
        if direction == INGRESS and mac_addr:
            kwargs['dl_dst'] = mac_addr
        elif direction == EGRESS and mac_addr:
            kwargs['dl_src'] = mac_addr
        if tun_id:
            kwargs['tun_id'] = tun_id
        actions = ''
        if strip_vlan:
            kwargs['priority'] += 10
            kwargs['vlan_tci'] = '0x1000/0x1000'
            if not mod_vlan_id:
                actions += 'strip_vlan,'
        if mod_vlan_id:
            actions += 'mod_vlan_vid:%s,' % mod_vlan_id
        actions += 'output=%s' % self.int_patch_ofport
        kwargs['actions'] = actions
        self._add_traffic_flow(**kwargs)

    def uninstall_accept_flow_from_br_mirror(self, mac_addr=None, tun_id=None):
        if mac_addr:
            self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                                      dl_src=mac_addr)
            self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                                      dl_dst=mac_addr)
        if tun_id:
            self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                                      tun_id=tun_id)

    def install_accept_flow_to_target(self,
                                      in_port,
                                      target_port,
                                      direction=INGRESS,
                                      tun_id=None,
                                      mac_addr=None,
                                      dst_mac=None,
                                      dl_vlan=None):
        kwargs = {
            'table': TRAFFIC_MIRROR_TBL,
            'priority': MIN_PRIORITY,
        }
        if in_port:
            kwargs['in_port'] = in_port
        if tun_id:
            kwargs['tun_id'] = tun_id
        if dst_mac:
            kwargs['dl_dst'] = dst_mac
        actions = ''
        if dl_vlan:
            kwargs['priority'] += 10
            if self.state_driver:
                kwargs['reg6'] = dl_vlan
            else:
                kwargs['dl_vlan'] = dl_vlan
                actions += 'strip_vlan,'
        if direction == INGRESS and mac_addr:
            kwargs['dl_src'] = mac_addr
        elif direction == EGRESS and mac_addr:
            kwargs['dl_dst'] = mac_addr
        actions += 'output:%s' % target_port
        kwargs['actions'] = actions
        self._add_flow(**kwargs)

    def uninstall_accept_flow_to_target(self,
                                        in_port,
                                        direction=INGRESS,
                                        tun_id=None,
                                        dst_mac=None,
                                        mac_addr=None):
        kwargs = {'table': TRAFFIC_MIRROR_TBL, 'in_port': in_port}
        if in_port:
            kwargs['in_port'] = in_port
        if tun_id:
            kwargs['tun_id'] = tun_id
        if dst_mac:
            kwargs['dl_dst'] = dst_mac
        if direction == INGRESS and mac_addr:
            kwargs['dl_src'] = mac_addr
        elif direction == EGRESS and mac_addr:
            kwargs['dl_dst'] = mac_addr
        self._delete_flow(**kwargs)


class TrafficMirrorAgentExtension(l2_extension.L2AgentExtension,
                                  TrafficMirrorAgent):
    SUPPORTED_RESOURCE_TYPES = [resources.TRAFFIC_MIRROR_SESSION]
    FILTER_MAP = TrafficMirrorFilterMapping()

    def delete_port(self, rpc_context, port_detail):
        LOG.info("clean up traffic mirror flows of port :%s",
                 port_detail['port_id'])
        if not self.is_ovs_bridge_existed():
            return
        port_id = port_detail['port_id']
        port_vif = self.br_int.get_vif_port_by_id(port_id)
        if not port_vif:
            return
        _v4_addr, _v6_addr, mac_address = self.get_all_address_from_port(
            port_detail)
        for mac_addr in mac_address:
            mac_addr = str(mac_addr)
            self.uninstall_source_port_flow(direction=INGRESS,
                                            in_port=port_vif.ofport)
            self.uninstall_source_port_flow(direction=EGRESS, dl_dst=mac_addr)
            self._delete_flow(table=MIRROR_FILTER_TBL, dl_src=mac_addr)
            self._delete_flow(table=MIRROR_FILTER_TBL, dl_dst=mac_addr)
            self._delete_flow(table=MIRROR_FILTER_TBL, in_port=port_vif.ofport)
            self._delete_flow(table=OUTPUT_TBL, dl_src=mac_addr)
            self._delete_flow(table=OUTPUT_TBL, dl_dst=mac_addr)
            self._delete_traffic_flow(table=LOCAL_SWITCHING_TBL,
                                      dl_src=mac_addr)
            self._delete_traffic_flow(table=LOCAL_SWITCHING_TBL,
                                      dl_dst=mac_addr)

    def handle_port(self, rpc_context, port_detail):
        LOG.info("TRAFFIC MIRROR process to update port %s",
                 port_detail['port_id'])
        if not self.is_ovs_bridge_existed():
            return
        tm_sessions_all = self.cache_api.get_resources(
            resources.TRAFFIC_MIRROR_SESSION, dict())
        port_id = port_detail['port_id']
        port_vif = self.br_int.get_vif_port_by_id(port_id)
        local_vlan = self.get_port_local_tag(port_id)
        for tm in tm_sessions_all:
            tm_session_dict = tm.to_dict()
            port_bindings = tm_session_dict.get('traffic_mirror_sources', [])
            ports = [port['source_port_id'] for port in port_bindings]
            if port_id not in ports:
                continue
            self.uninstall_source_port_flow(direction=INGRESS,
                                            in_port=port_vif.ofport)
            self.uninstall_source_port_flow(direction=EGRESS,
                                            dl_vlan=local_vlan)
            self._delete_flow(table=MIRROR_FILTER_TBL, cookie="0/0")
            self._delete_flow(table=OUTPUT_TBL, cookie="0/0")
            self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                                      dl_vlan=local_vlan)
            self.delete_traffic_mirror_session(self.context, tm)
            self.create_traffic_mirror_session(self.context, tm)

    def consume_api(self, agent_api):
        self.agent_api = agent_api
        self.cache_api = agent_api.plugin_rpc.remote_resource_cache

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        self.conf = cfg.CONF.TRAFFIC_MIRROR
        if self.enable_traffic_mirror is False:
            LOG.info("disable traffic_mirror l2agent extension.")
            return
        self.agent_conf = cfg.CONF.AGENT
        self.host = cfg.CONF.host
        self.agent_id = 'ovs-agent-%s' % cfg.CONF.host
        self.context = _context.get_admin_context_without_session()
        self.host_tunnel_ip_dict = {}
        self.pfn_subnets = set()
        self.local_port_ip = None
        self.phy_bridges = self.agent_api.bridge_mappings
        self.resource_rpc = resources_rpc.ResourcesPullRpcApi()
        if connection:
            self._connection = connection
        else:
            self._connection = n_rpc.Connection()
        self._register_rpc_consumers()
        self.br_int = self.agent_api.request_int_br()
        self.init_task = loopingcall.FixedIntervalLoopingCall(
            self.init_extension)
        try:
            self.init_extension()
        except Exception as ex:
            LOG.warning("Init traffic mirror raise %s, run loop task.", ex)
            self.init_task.start(
                interval=cfg.CONF.AGENT.retrieve_pfn_interval,
                initial_delay=cfg.CONF.AGENT.retrieve_pfn_interval,
                stop_on_exception=False)
        LOG.info('TRAFFIC MIRROR extension loaded.')

    def init_extension(self):
        self.get_pfn_subnets()
        self.init_task.stop()
        self._init_traffic_bridge()
        self._set_br_protocol()
        self._support_add_vxlan = ('add_vxlan'
                                   in self.dump_table_action_features())
        self._init_base_flows()
        if self.conf.pull_tm_network_info:
            self._setup_traffic_mirror()
        self._fetch_sync_all_traffic_mirror_sessions()

    def is_ovs_bridge_existed(self):
        if not hasattr(self, 'br_int') or not self.br_int:
            LOG.warning("TRAFFIC MIRROR extension doest not find "
                        "ovs bridge br-int, skip to process.")
            return False
        if not hasattr(self, 'br_mirror') or not self.br_mirror:
            LOG.warning("TRAFFIC MIRROR extension doest not find "
                        "ovs bridge br-mirror, skip to process.")
            return False
        return True

    def get_port_by_id(self, port_id):
        port_obj = self.cache_api.get_resource_by_id(resources.PORT, port_id)
        if port_obj:
            return port_obj.to_dict()
        else:
            return None

    def get_net_by_id(self, network_id):
        network = self.agent_api.plugin_rpc.get_network_details(
            self.context, network_id, self.agent_id, self.host)
        if network:
            return network
        else:
            return None

    def get_port_local_tag(self, port_id):
        source_port_vif = self.br_int.get_vif_port_by_id(port_id)
        if source_port_vif:
            return self.br_int.get_port_tag_by_name(source_port_vif.port_name)
        return None

    def get_agent_config_by_host(self, host):
        agent_type = 'Open vSwitch agent'
        agent_lists = self.agent_api.plugin_rpc.get_agent_by_filter(
            self.context, host=host, agent_type=agent_type)
        if agent_lists:
            agent_dict = agent_lists[0]
            config_str = agent_dict['configurations']
            config_dict = jsonutils.loads(config_str)
            agent_dict['configurations'] = config_dict
            return agent_dict
        else:
            return {}

    def _record_host_tunnel_ip(self, host, ip):
        self.host_tunnel_ip_dict[host] = ip

    def get_host_tunnel_ip(self, host):
        remote_ip = self.host_tunnel_ip_dict.get(host)
        if remote_ip:
            return remote_ip

        l2_agent = self.get_agent_config_by_host(host)
        if l2_agent:
            traffic_conf = l2_agent['configurations'].get('traffic_mirror', {})
            remote_ip = traffic_conf.get('local_ip')
        else:
            remote_ip = None

        if remote_ip:
            self._record_host_tunnel_ip(host, remote_ip)
        return remote_ip

    def _init_traffic_bridge(self):
        bridge_name = self.conf.traffic_bridge
        self.br_mirror = br_tun.OVSTunnelBridge(bridge_name,
                                                cfg.CONF.OVS.datapath_type)
        self.br_mirror.create(secure_mode=True)
        self.br_mirror.setup_controllers(cfg.CONF)
        if (not self.br_int.port_exists(self.traffic_patch_port)):
            self.br_int.add_patch_port(self.traffic_patch_port,
                                       self.int_patch_port)
        if (not self.br_mirror.port_exists(self.int_patch_port)):
            self.br_mirror.add_patch_port(self.int_patch_port,
                                          self.traffic_patch_port)

    def _init_base_flows(self):
        LOG.debug("initialize traffic_mirror base flows.")
        if not self.is_ovs_bridge_existed():
            return
        self._delete_flow(table=MIRROR_FILTER_TBL, cookie="0/0")
        self._delete_flow(table=METER_BANDWIDTH_TBL, cookie="0/0")
        self._delete_flow(table=METER_PPS_TBL, cookie="0/0")
        self._delete_flow(table=OUTPUT_TBL, cookie="0/0")
        self._add_flow(table=MIRROR_FILTER_TBL,
                       priority=DEFAULT_PRIORITY,
                       actions='drop')
        self._add_flow(table=METER_BANDWIDTH_TBL,
                       priority=DEFAULT_PRIORITY,
                       actions='resubmit(,%d)' % METER_PPS_TBL)
        self._add_flow(table=METER_PPS_TBL,
                       priority=DEFAULT_PRIORITY,
                       actions='resubmit(,%d)' % OUTPUT_TBL)
        self._add_flow(table=OUTPUT_TBL,
                       priority=DEFAULT_PRIORITY,
                       actions='drop')
        self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING, cookie="0/0")
        self._add_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                               priority=0,
                               actions='drop')
        # Prevent arp normal request ERROR that openvswitch will add_vxlan
        # header to normal port VXLAN arp request to raise ERROR.
        self._add_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                               priority=ARP_FORBIDDEN_PRI,
                               proto='arp',
                               actions='drop')
        self._add_traffic_flow(table=TRAFFIC_MIRROR_TBL,
                               priority=MUL_FORBIDDEN_PRI,
                               dl_dst='01:00:00:00:00:00/01:00:00:00:00:00',
                               actions='drop')

    @lockutils.synchronized('traffic-mirror')
    def _fetch_sync_all_traffic_mirror_sessions(self):
        LOG.info("Fetch and sync all traffic mirror sessions.")
        tm_sessions_all = self.cache_api.get_resources(
            resources.TRAFFIC_MIRROR_SESSION, dict())
        for tm in tm_sessions_all:
            self.create_traffic_mirror_session(self.context, tm)
            self.FILTER_MAP.add_filter_session(tm.traffic_mirror_filter_id,
                                               tm.id)

    def _setup_traffic_mirror(self):
        if not self.enable_traffic_mirror:
            return
        self.tm_network = \
            self.agent_api.plugin_rpc.get_traffic_mirror_network_info(
                self.context, host=self.host, agent_id=self.agent_id,
                zone=self.zone)
        if not self.tm_network:
            LOG.warning(
                "[TRAFFIC_MIRROR] is enabled but its network is not exits!")
            return

        self.tm_host_port = self.tm_network.get('mirror_tunnel_port', {})
        if not self.tm_host_port:
            LOG.warning(
                "[TRAFFIC_MIRROR] is enabled but its port is not exits!")
            return

        port_id = self.tm_host_port.get('id')
        interface_obj = interface_driver.OVSInterfaceDriver(cfg.CONF)
        tap_name = "tm_tap%s" % port_id
        tap_name = tap_name[:14]
        mac_address = self.tm_host_port.get('mac_address')
        port_plugged = self.br_int.get_vif_port_by_id(port_id)
        if not port_plugged:
            interface_obj.plug(self.tm_network['mirror_tunnel_network_id'],
                               port_id,
                               tap_name,
                               mac_address,
                               mtu=1500,
                               bridge="br-int")
        else:
            interface_obj.set_link_status(tap_name, link_up=True)
        subnet_cidr = dict()
        for subnet in self.tm_network['mirror_tunnel_subnets']:
            subnet_cidr[subnet['id']] = subnet['cidr'].split("/")[1]
        ips = []
        for fixed_ip in self.tm_host_port['fixed_ips']:
            ip_address = fixed_ip['ip_address']
            cidr = subnet_cidr.get(fixed_ip['subnet_id'], None)
            if fixed_ip['visible'] and cidr:
                ips.append(ip_address + "/" + cidr)
                self.local_port_ip = ip_address
                break
        interface_obj.init_l3(tap_name, ips)
        tm_port = self.get_port_by_id(port_id)
        port_vlan_id = None
        for bind in tm_port['binding_levels']:
            if bind['driver'] == 'openvswitch' and bind['host'] == self.host:
                port_vlan_id = bind['segment']['segmentation_id']
                break
        if not port_vlan_id:
            return
        ovsdb = self.br_mirror.ovsdb
        with ovsdb.transaction() as txn:
            txn.add(ovsdb.db_set('Port', tap_name, ('tag', port_vlan_id)))
            txn.add(ovsdb.db_clear('Port', tap_name, 'vlan_mode'))
            txn.add(ovsdb.db_clear('Port', tap_name, 'trunks'))
        interface_obj.set_link_status(tap_name, link_up=True)

    def _register_rpc_consumers(self):
        registry.register(self._handle_notification,
                          resources.TRAFFIC_MIRROR_SESSION)
        registry.register(self._handle_filter_notification,
                          resources.TRAFFIC_MIRROR_FILTER)
        endpoints = [resources_rpc.ResourcesPushRpcCallback()]
        topic = resources_rpc.resource_type_versioned_topic(
            resources.TRAFFIC_MIRROR_SESSION)
        self._connection.create_consumer(topic, endpoints, fanout=True)
        topic_filter = resources_rpc.resource_type_versioned_topic(
            resources.TRAFFIC_MIRROR_FILTER)
        self._connection.create_consumer(topic_filter, endpoints, fanout=True)
        self._connection.consume_in_threads()

    def _handle_notification(self, context, resource_type, tm_sessions,
                             event_type):
        """This function used to process traffic mirror session events"""
        LOG.debug("TRAFFIC MIRROR handle notification resource:%s, event:%s",
                  resource_type, event_type)
        for tm_session in tm_sessions:
            self._process_traffic_mirror_event(context, tm_session, event_type)

    def _handle_filter_notification(self, context, resource_type, tms_filters,
                                    event_type):
        LOG.info(
            "TRAFFIC MIRROR handle filter notification resource:%s, event:%s",
            resource_type, event_type)
        tms_filters_updated = set()
        if event_type == events.CREATED:
            return
        for tm_filter in tms_filters:
            if self.FILTER_MAP.get_tm_session_by_filter(tm_filter.id):
                tms_filters_updated.add(tm_filter.id)
        if not tms_filters_updated:
            LOG.info("No traffic mirror filter found, update all.")
        filters = {'traffic_mirror_filter_id': tuple(tms_filters_updated)}
        tm_sessions_updated = self.cache_api.get_resources(
            resources.TRAFFIC_MIRROR_SESSION, filters)
        for tm_session in tm_sessions_updated:
            self.delete_tms_filter_rules(tm_session)
            self.add_tms_filter_rules(tm_session)

    @lockutils.synchronized('traffic-mirror')
    def _process_traffic_mirror_event(self, context, tm_session, event_type):
        if event_type == events.CREATED:
            self.create_traffic_mirror_session(context, tm_session)
        elif event_type == events.UPDATED:
            self.update_traffic_mirror_session(context, tm_session)
        elif event_type == events.DELETED:
            self.delete_traffic_mirror_session(context, tm_session)

    def get_port_info(self, port_id):
        port_obj = self.cache_api.get_resource_by_id(resources.PORT, port_id)
        return port_obj

    def get_all_address_from_port(self, port):
        '''Get all useful ip address and mac address from the port'''
        mac_address = set()
        v4_address = []
        v6_address = []
        if port.get('mac_address'):
            mac_address.add(port['mac_address'])
        allowed_address_pairs = port.get('allowed_address_pairs', [])
        for address in allowed_address_pairs:
            ip_addr = address.get('ip_address')
            mac_addr = address.get('mac_address')
            if mac_addr not in mac_address:
                mac_address.add(mac_addr)
            ip_version = netaddr.IPAddress(ip_addr).version
            if ip_version == 4:
                v4_address.append((ip_addr, mac_addr))
            else:
                v6_address.append((ip_addr, mac_addr))

        if not port.get('mac_address'):
            return v4_address, v6_address, mac_address

        for fixed_ip in port['fixed_ips']:
            if self.is_pfn_subnet(fixed_ip['subnet_id']):
                continue
            ip_addr = fixed_ip.get('ip_address')
            ip_version = netaddr.IPAddress(ip_addr).version
            if ip_version == 4:
                v4_address.append((ip_addr, port['mac_address']))
            else:
                v6_address.append((ip_addr, port['mac_address']))
        return v4_address, v6_address, mac_address

    def get_traffic_mirror_sessions(self, filter_id):
        kwargs = {'traffic_mirror_filter_id': tuple(filter_id)}
        traffic_mirror_sessions = self.cache_api.get_resources(
            resources.TRAFFIC_MIRROR_SESSION, filters=kwargs)
        if traffic_mirror_sessions:
            return traffic_mirror_sessions
        else:
            return []

    def get_filter_rules(self, filter_id):
        filter_obj = self.cache_api.get_resource_by_id(
            resources.TRAFFIC_MIRROR_FILTER, filter_id)
        if filter_obj:
            return filter_obj.to_dict()
        else:
            return None

    def create_traffic_mirror_session(self, context, tm_session):
        LOG.info("create traffic mirror session %s", tm_session.id)
        if not self.is_ovs_bridge_existed():
            return
        tm_session_dict = tm_session.to_dict()
        source_port_bindings = tm_session_dict.get('traffic_mirror_sources',
                                                   [])
        target_port_id = tm_session_dict.get('traffic_mirror_target_port_id')
        if not source_port_bindings and not target_port_id:
            LOG.info("Traffic mirror source ports and target port are empty, "
                     "skip to process.")
            return

        source_ports_hosted = list()
        target_ports_hosted = list()
        for port_binds in source_port_bindings:
            port_id = port_binds.get('source_port_id')
            port_vif = self.br_int.get_vif_port_by_id(port_id)
            if port_vif:
                source_ports_hosted.append((port_id, port_vif))

        if target_port_id:
            target_port_vif = self.br_int.get_vif_port_by_id(target_port_id)
            if target_port_vif:
                target_ports_hosted.append((target_port_id, target_port_vif))
        else:
            target_port_vif = None

        if len(source_ports_hosted) == 0 and len(target_ports_hosted) == 0:
            LOG.info("Traffic mirror port doest not exist, nothing needs to"
                     "do.")
            return

        # Implementation
        # 1st, process source port binding logic
        # (1)Import source port's flows from table 59 to table 220,
        #    and start to collect packets.
        # (2)Install all filter rules in table 220, and allow specified
        #    packets to send table 221

        # 2ed, process target port binding logic
        # (1) Judge whether output type, if it's vxlan and target port
        #     dost not exist the same HOST with source port, create new vxlan
        #     output and send packets to it.
        # (2) Judge whether output type, if it's vlan and target port dost
        #     exits the same HOST witch source port, modify packets' dst mac
        #     and vlan id and output bond_virt
        # (3) judge whether source port and target port exists in the
        #     same HOST, if it's true, direct output packets to dst port.

        enabled = tm_session_dict.get('enabled')
        filter_id = tm_session_dict.get('traffic_mirror_filter_id')
        packet_length = tm_session_dict.get('packet_length')
        segmentation_id = tm_session_dict.get('segmentation_id')
        vxlan_vni = tm_session_dict.get('virtual_network_id')
        priority = tm_session_dict.get('priority', 0)
        target_port = self.get_port_by_id(target_port_id)
        target_port_host = target_port['bindings'][0]['host']
        filter_rules = self.get_filter_rules(filter_id)
        for port_id, source_port_vif in source_ports_hosted:
            source_port = self.get_port_by_id(port_id)
            source_port_host = source_port['bindings'][0]['host']
            source_local_vlan = self.get_port_local_tag(port_id)
            port_security = source_port['security']['port_security_enabled']
            if (source_port_vif and target_port_vif and
                    source_port_host == target_port_host):
                same_host = True
            else:
                same_host = False
            # Step-1: export source port flows
            self.process_source_port_actions(source_port, source_port_vif,
                                             source_local_vlan, port_security,
                                             enabled, priority)
            # Step-2: install traffic mirror filter rules
            self.process_source_port_filter_actions(source_port,
                                                    source_port_vif,
                                                    source_local_vlan,
                                                    filter_rules)

            # Step-3: install output flows from br_int
            tunnel_port = self.process_output_from_br_int_actions(
                source_port, source_port_host, source_port_vif,
                source_local_vlan, target_port, target_port_host,
                target_port_vif, segmentation_id, vxlan_vni, packet_length)

            # Step-4: install output flows from br_mirror
            self.process_output_from_br_mirror_actions(same_host, source_port,
                                                       source_local_vlan,
                                                       target_port,
                                                       tunnel_port, vxlan_vni,
                                                       packet_length)

        # Step-5: install accepting flows to target in destination HOST
        if target_ports_hosted:
            self.process_target_port_accept_actions(source_port_bindings,
                                                    target_port_id,
                                                    segmentation_id)

    def process_target_port_accept_actions(self, source_port_bindings,
                                           target_port_id, segmentation_id):
        target_local_vlan = self.get_port_local_tag(target_port_id)
        target_port = self.get_port_by_id(target_port_id)
        target_port_vif = self.br_int.get_vif_port_by_id(target_port_id)
        if not target_port or not target_port.get('bindings'):
            target_host = None
        else:
            target_host = target_port['bindings'][0]['host']
        if self.carrier_network_type == VXLAN_NET_TYPE:
            in_port = self.traffic_patch_ofport
        else:
            phy_patch_ofport = self.get_phy_patch_port(target_port)
            in_port = phy_patch_ofport if phy_patch_ofport else None

        for port_binding in source_port_bindings:
            port_id = port_binding.get('source_port_id')
            source_port = self.get_port_by_id(port_id)
            if not source_port or not source_port['bindings']:
                source_host = None
            else:
                source_host = source_port['bindings'][0]['host']
            if target_host is not None and target_host == source_host:
                continue
            remote_ip = self.get_host_tunnel_ip(source_host)
            tunnel_port = self.create_tunnel_port(remote_ip, segmentation_id)
            if tunnel_port != ovs_lib.INVALID_OFPORT and segmentation_id:
                self.install_accept_flow_from_br_mirror(
                    tun_id=segmentation_id, mod_vlan_id=target_local_vlan)
            if in_port and target_port_vif:
                self.install_accept_flow_to_target(
                    in_port,
                    target_port=target_port_vif.ofport,
                    dl_vlan=target_local_vlan,
                    dst_mac=target_port['mac_address'])

    def process_output_from_br_mirror_actions(self, same_host, source_port,
                                              source_local_vlan, target_port,
                                              tunnel_port, vxlan_vni,
                                              packet_length):
        if not tunnel_port or tunnel_port == ovs_lib.INVALID_OFPORT:
            return
        vxlan_ethertype, vxlan_nw_dst, vxlan_nw_src = self.get_vxlan_info(
            source_port, target_port)
        _v4_addr, _v6_addr, mac_address = self.get_all_address_from_port(
            source_port)
        for mac_addr in mac_address:
            self.add_br_mirror_output_flow(
                tunnel_port,
                self.carrier_network_type,
                same_host=same_host,
                direction=INGRESS,
                dl_vlan=source_local_vlan,
                dl_src=mac_addr,
                vxlan_ethertype=vxlan_ethertype,
                vxlan_nw_src=vxlan_nw_src,
                vxlan_nw_dst=vxlan_nw_dst,
                vxlan_dl_src=source_port['mac_address'],
                vxlan_dl_dst=target_port['mac_address'],
                vxlan_vni=vxlan_vni,
                packet_length=packet_length)
            self.add_br_mirror_output_flow(
                tunnel_port,
                self.carrier_network_type,
                same_host=same_host,
                direction=EGRESS,
                dl_dst=mac_addr,
                dl_vlan=source_local_vlan,
                vxlan_ethertype=vxlan_ethertype,
                vxlan_nw_src=vxlan_nw_src,
                vxlan_nw_dst=vxlan_nw_dst,
                vxlan_dl_src=source_port['mac_address'],
                vxlan_dl_dst=target_port['mac_address'],
                vxlan_vni=vxlan_vni,
                packet_length=packet_length)

    def process_output_from_br_int_actions(self, source_port, source_port_host,
                                           source_port_vif, source_local_vlan,
                                           target_port, target_port_host,
                                           target_port_vif, segmentation_id,
                                           vxlan_vni, packet_length):
        same_host = False
        tunnel_port = ovs_lib.INVALID_OFPORT
        if (source_port_vif and target_port_vif and
                source_port_host == target_port_host):
            out_port = target_port_vif.ofport
            same_host = True
        elif self.carrier_network_type == VXLAN_NET_TYPE:
            remote_ip = self.get_host_tunnel_ip(target_port_host)
            tunnel_port = self.create_tunnel_port(remote_ip, segmentation_id)
            out_port = self.traffic_patch_ofport
        else:
            out_port = self.get_phy_patch_port(source_port)
        vxlan_ethertype, vxlan_nw_dst, vxlan_nw_src = self.get_vxlan_info(
            source_port, target_port)
        _v4_addr, _v6_addr, mac_address = self.get_all_address_from_port(
            source_port)
        for mac_addr in mac_address:
            self.install_output_flow_from_br_int(
                out_port,
                same_host,
                self.carrier_network_type,
                in_port=source_port_vif.ofport,
                dl_src=mac_addr,
                dl_vlan=source_local_vlan,
                mod_dl_dst=target_port['mac_address'],
                vxlan_ethertype=vxlan_ethertype,
                vxlan_nw_src=vxlan_nw_src,
                vxlan_nw_dst=vxlan_nw_dst,
                vxlan_dl_src=source_port['mac_address'],
                vxlan_dl_dst=target_port['mac_address'],
                vxlan_vni=vxlan_vni,
                packet_length=packet_length)
            self.install_output_flow_from_br_int(
                out_port,
                same_host,
                self.carrier_network_type,
                in_port=source_port_vif.ofport,
                dl_src=mac_addr,
                mod_vlan_id=source_local_vlan,
                mod_dl_dst=target_port['mac_address'],
                vxlan_ethertype=vxlan_ethertype,
                vxlan_nw_src=vxlan_nw_src,
                vxlan_nw_dst=vxlan_nw_dst,
                vxlan_dl_src=source_port['mac_address'],
                vxlan_dl_dst=target_port['mac_address'],
                vxlan_vni=vxlan_vni,
                packet_length=packet_length)
            self.install_output_flow_from_br_int(
                out_port,
                same_host,
                self.carrier_network_type,
                dl_vlan=source_local_vlan,
                dl_dst=mac_addr,
                mod_vlan_id=source_local_vlan,
                mod_dl_src=mac_addr,
                mod_dl_dst=target_port['mac_address'],
                vxlan_ethertype=vxlan_ethertype,
                vxlan_nw_src=vxlan_nw_src,
                vxlan_nw_dst=vxlan_nw_dst,
                vxlan_dl_src=source_port['mac_address'],
                vxlan_dl_dst=target_port['mac_address'],
                vxlan_vni=vxlan_vni,
                packet_length=packet_length)
            if same_host and target_port_vif:
                self.install_output_flow_from_br_int(
                    out_port,
                    same_host,
                    self.carrier_network_type,
                    in_port=target_port_vif.ofport,
                    dl_dst=mac_addr,
                    dl_vlan=source_local_vlan,
                    vxlan_ethertype=vxlan_ethertype,
                    vxlan_nw_src=vxlan_nw_src,
                    vxlan_nw_dst=vxlan_nw_dst,
                    vxlan_dl_src=source_port['mac_address'],
                    vxlan_dl_dst=target_port['mac_address'],
                    vxlan_vni=vxlan_vni,
                    packet_length=packet_length)
        return tunnel_port

    def process_source_port_filter_actions(self, port, port_vif, local_vlan,
                                           filter_rules):
        _v4_addr, _v6_addr, mac_address = self.get_all_address_from_port(port)
        for rule in filter_rules.get('rules'):
            direction = rule.get('direction')
            ethertype = rule.get('ethertype')
            protocol = rule.get('protocol', 'all')
            src_cidr = rule.get('src_cidr')
            dst_cidr = rule.get('dst_cidr')
            src_port_range_min = rule.get('src_port_range_min')
            src_port_range_max = rule.get('src_port_range_max')
            dst_port_range_min = rule.get('dst_port_range_min')
            dst_port_range_max = rule.get('dst_port_range_max')
            action = rule.get('action', 'accept')
            priority = rule.get('priority', 0)
            for mac_addr in mac_address:
                self.install_filter_flows(
                    ethertype=ethertype,
                    protocol=protocol,
                    direction=direction,
                    in_port=port_vif.ofport,
                    dl_vlan=local_vlan,
                    mac_addr=mac_addr,
                    src_cidr=src_cidr,
                    dst_cidr=dst_cidr,
                    src_port_range_min=src_port_range_min,
                    src_port_range_max=src_port_range_max,
                    dst_port_range_min=dst_port_range_min,
                    dst_port_range_max=dst_port_range_max,
                    priority=priority,
                    action=action)

    def process_source_port_actions(self, port, port_vif, local_vlan,
                                    port_security, enable, priority):
        v4_address, _v6_addr, mac_address = self.get_all_address_from_port(
            port)
        # delete all port arp flows in the openvswitch state driver scenario
        if self.state_driver:
            self._delete_flow(table=TRAFFIC_MIRROR_TBL,
                              proto='arp',
                              reg6=local_vlan,
                              in_port=port_vif.ofport)
            for address in v4_address:
                self._delete_flow(table=TRAFFIC_MIRROR_TBL,
                                  proto='arp',
                                  reg6=local_vlan,
                                  arp_tpa=address[0])
        # install arp flows to disable port security scenario
        if self.state_driver and not port_security and enable:
            self._add_flow(priority=200,
                           table=TRAFFIC_MIRROR_TBL,
                           proto='arp',
                           reg6=local_vlan,
                           in_port=port_vif.ofport,
                           actions='resubmit(,%d)' %
                           (ovs_consts.ACCEPT_OR_INGRESS_TABLE))
            for address in v4_address:
                self._add_flow(priority=200,
                               table=TRAFFIC_MIRROR_TBL,
                               proto='arp',
                               reg6=local_vlan,
                               arp_tpa=address[0],
                               actions='resubmit(,%d)' %
                               (ovs_consts.BASE_EGRESS_TABLE))

        for mac_addr in mac_address:
            if enable:
                if self.state_driver:
                    self.install_source_port_flow(direction=INGRESS,
                                                  in_port=port_vif.ofport,
                                                  dl_src=mac_addr,
                                                  dl_vlan=local_vlan,
                                                  port_security=port_security,
                                                  priority=priority)
                else:
                    self.install_source_port_flow(direction=INGRESS,
                                                  in_port=port_vif.ofport,
                                                  dl_src=mac_addr,
                                                  dl_vlan=local_vlan,
                                                  port_security=port_security,
                                                  strip_vlan=True,
                                                  priority=priority)
                self.install_source_port_flow(direction=EGRESS,
                                              dl_vlan=local_vlan,
                                              dl_dst=mac_addr,
                                              port_security=port_security,
                                              priority=priority)
                phy_patch_port = self.get_phy_patch_port(port)
                if phy_patch_port:
                    self.install_source_port_flow(direction=EGRESS,
                                                  in_port=phy_patch_port,
                                                  dl_vlan=local_vlan,
                                                  dl_dst=mac_addr,
                                                  port_security=port_security,
                                                  is_phy_ofport=True,
                                                  priority=priority + 10)
            else:
                self.uninstall_source_port_flow(direction=INGRESS,
                                                in_port=port_vif.ofport,
                                                dl_src=mac_addr)
                self.uninstall_source_port_flow(direction=EGRESS,
                                                dl_vlan=local_vlan,
                                                dl_dst=mac_addr)

    def get_port_binding_network(self, port):
        '''return (network_type, physical_network)'''
        for bind in port.get('binding_levels', []):
            if (bind.get('host') == self.host and
                    bind.get('driver') == 'openvswitch'):
                return (bind.get('segment', {}).get('network_type'),
                        bind.get('segment', {}).get('physical_network',
                                                    'default'))
        return None, None

    def get_phy_patch_port(self, port):
        '''return port destination physical patch port, such as: patch-tun or
        int-br-provider or int-br-ex
        '''
        ovs_binding_network, physical_network = self.get_port_binding_network(
            port)
        phy_bridge = self.phy_bridges.get(physical_network, None)
        if ovs_binding_network == VXLAN_NET_TYPE:
            return self.tun_patch_port
        elif ovs_binding_network == VLAN_NET_TYPE:
            return self.phy_patch_port(phy_bridge)
        return None

    def get_vxlan_info(self, source_port, target_port):
        '''get source port and target port ip address pairs that is suitable
        for VXLAN header.
        '''
        vxlan_ips = []
        for source_fixed_ip in source_port['fixed_ips']:
            if self.is_pfn_subnet(source_fixed_ip['subnet_id']):
                continue
            source_ip = source_fixed_ip.get('ip_address')
            sip_version = netaddr.IPAddress(source_ip).version
            for target_fixed_ip in target_port['fixed_ips']:
                if self.is_pfn_subnet(target_fixed_ip['subnet_id']):
                    continue
                target_ip = target_fixed_ip.get('ip_address')
                tip_version = netaddr.IPAddress(target_ip).version
                if sip_version == tip_version:
                    vxlan_version = 4 if sip_version == 4 else 6
                    vxlan_ips.append((source_ip, target_ip, vxlan_version))
        vxlan_nw_src, vxlan_nw_dst, vxlan_version = vxlan_ips[0]
        for i, vxlan_ip in enumerate(vxlan_ips):
            if (vxlan_ip[2] == DEFAULT_VXLAN_VERSION and
                    vxlan_version != DEFAULT_VXLAN_VERSION):
                vxlan_nw_src, vxlan_nw_dst, vxlan_version = vxlan_ip
                break
        if vxlan_version == 4:
            vxlan_ethertype = IPv4
        else:
            vxlan_ethertype = IPv6
        return vxlan_ethertype, vxlan_nw_dst, vxlan_nw_src

    def update_traffic_mirror_session(self, context, tm_session):
        LOG.info("update traffic mirror session, %s", tm_session.id)
        self.delete_traffic_mirror_session(context, tm_session)
        self.create_traffic_mirror_session(context, tm_session)

    def delete_traffic_mirror_session(self, context, tm_session):
        LOG.info("delete traffic mirror session, %s", tm_session.id)
        if not self.is_ovs_bridge_existed():
            return
        tm_session_dict = tm_session.to_dict()
        source_port_bindings = tm_session_dict.get('traffic_mirror_sources',
                                                   [])
        target_port_id = tm_session_dict.get('traffic_mirror_target_port_id')
        if not source_port_bindings and not target_port_id:
            LOG.info(
                "Traffic mirror source ports and target port id is empty, "
                "skip to process.")
            return

        source_ports_hosted = list()
        target_ports_hosted = list()
        for port_binds in source_port_bindings:
            port_id = port_binds.get('source_port_id')
            source_port_vif = self.br_int.get_vif_port_by_id(port_id)
            if source_port_vif:
                source_ports_hosted.append((port_id, source_port_vif))

        if target_port_id:
            target_port_vif = self.br_int.get_vif_port_by_id(target_port_id)
            if target_port_vif:
                target_ports_hosted.append((target_port_id, target_port_vif))

        if len(source_ports_hosted) == 0 and len(target_ports_hosted) == 0:
            LOG.info("Traffic mirror port doest not exist, nothing needs to"
                     "do.")
            return

        target_port = self.get_port_by_id(target_port_id)
        if not target_port.get('bindings'):
            target_host = None
        else:
            target_host = target_port['bindings'][0]['host']
        segmentation_id = tm_session_dict.get('segmentation_id')
        for port_id, source_port_vif in source_ports_hosted:
            source_port = self.get_port_by_id(port_id)
            source_local_vlan = self.get_port_local_tag(port_id)
            if not source_port.get('bindings'):
                source_host = None
            else:
                source_host = source_port['bindings'][0]['host']
            if source_host == target_host:
                same_host = True
            else:
                same_host = False
            if (self.carrier_network_type == VXLAN_NET_TYPE and not same_host):
                remote_ip = self.get_host_tunnel_ip(target_host)
                self.delete_tunnel_port(remote_ip, segmentation_id)

            # Step-1: uninstall source port flows
            _v4_addr, _v6_addr, mac_address = self.get_all_address_from_port(
                source_port)
            for mac_addr in mac_address:
                self.uninstall_source_port_flow(direction=INGRESS,
                                                in_port=source_port_vif.ofport,
                                                dl_src=mac_addr)
                self.uninstall_source_port_flow(direction=EGRESS,
                                                dl_dst=mac_addr)

                # Step-2: uninstall traffic mirror filter rules
                self.uninstall_filter_flows(direction=INGRESS,
                                            in_port=source_port_vif.ofport,
                                            mac_addr=mac_addr)
                self.uninstall_filter_flows(direction=EGRESS,
                                            dl_vlan=source_local_vlan,
                                            mac_addr=mac_addr)

                # Step-3: delete output flow from br-int
                self.delete_output_flow_from_br_int(
                    dl_src=mac_addr, in_port=source_port_vif.ofport)
                self.delete_output_flow_from_br_int(dl_dst=mac_addr)
                self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                                          dl_src=mac_addr)
                self._delete_traffic_flow(table=TRAFFIC_LOCAL_SWITCHING,
                                          dl_dst=mac_addr)

        # Step-4: uninstall target port accepting flows and delete tunnel port
        if not target_ports_hosted:
            return
        if self.carrier_network_type == VXLAN_NET_TYPE:
            in_port = self.traffic_patch_ofport
        else:
            phy_patch_ofport = self.get_phy_patch_port(target_port)
            in_port = phy_patch_ofport if phy_patch_ofport else None

        for port_binding in source_port_bindings:
            port_id = port_binding.get('source_port_id')
            source_port = self.get_port_by_id(port_id)
            if not source_port.get('bindings'):
                source_host = None
            else:
                source_host = source_port['bindings'][0]['host']
            if target_host and source_host and source_host == target_host:
                same_host = True
            else:
                same_host = False
            if not same_host and segmentation_id:
                self.uninstall_accept_flow_from_br_mirror(
                    tun_id=segmentation_id)
            if not same_host and in_port:
                self.uninstall_accept_flow_to_target(
                    in_port, dst_mac=target_port['mac_address'])
            if self.carrier_network_type == VXLAN_NET_TYPE and not same_host:
                remote_ip = self.get_host_tunnel_ip(source_host)
                self.delete_tunnel_port(remote_ip, segmentation_id)

    @lockutils.synchronized('traffic-mirror')
    def delete_tms_filter_rules(self, tm_session):
        LOG.info("delete traffic mirror session %s filter rules",
                 tm_session.id)
        tm_session_dict = tm_session.to_dict()
        source_port_bindings = tm_session_dict.get('traffic_mirror_sources',
                                                   [])
        source_ports_hosted = list()
        for port_binds in source_port_bindings:
            port_id = port_binds.get('source_port_id')
            port_vif = self.br_int.get_vif_port_by_id(port_id)
            if port_vif:
                source_ports_hosted.append((port_id, port_vif))
        filter_id = tm_session_dict.get('traffic_mirror_filter_id')
        if filter_id:
            self.FILTER_MAP.remove_filter_session(filter_id, tm_session.id)
        for port_id, port_vif in source_ports_hosted:
            port = self.get_port_by_id(port_id)
            _v4_addr, _v6_addr, mac_address = self.get_all_address_from_port(
                port)
            local_vlan = self.get_port_local_tag(port_id)
            for mac_addr in mac_address:
                self._delete_flow(table=MIRROR_FILTER_TBL,
                                  in_port=port_vif.ofport,
                                  dl_src=mac_addr)
                self._delete_flow(table=MIRROR_FILTER_TBL,
                                  dl_vlan=local_vlan,
                                  dl_dst=mac_addr)

    @lockutils.synchronized('traffic-mirror')
    def add_tms_filter_rules(self, tm_session):
        LOG.info("add traffic mirror session %s filter rules", tm_session.id)
        tm_session_dict = tm_session.to_dict()
        source_port_bindings = tm_session_dict.get('traffic_mirror_sources',
                                                   [])
        source_ports_hosted = list()
        for port_binds in source_port_bindings:
            port_id = port_binds.get('source_port_id')
            port_vif = self.br_int.get_vif_port_by_id(port_id)
            if port_vif:
                source_ports_hosted.append((port_id, port_vif))
        filter_id = tm_session_dict.get('traffic_mirror_filter_id')
        if filter_id:
            self.FILTER_MAP.add_filter_session(filter_id, tm_session.id)
        filter_rules = self.get_filter_rules(filter_id)
        if not filter_rules:
            return
        for port_id, port_vif in source_ports_hosted:
            port = self.get_port_by_id(port_id)
            local_vlan = self.get_port_local_tag(port_id)
            self.process_source_port_filter_actions(port, port_vif, local_vlan,
                                                    filter_rules)
