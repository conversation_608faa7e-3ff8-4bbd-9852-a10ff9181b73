# Copyright (c) 2022 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import random

from neutron_lib.agent import l3_extension
from neutron_lib import constants
from neutron_lib.services.qos import constants as qos_consts
from oslo_concurrency import lockutils
from oslo_config import cfg
from oslo_log import log as logging

from neutron.agent.common import ovs_lib
from neutron.agent.l3.extensions.qos import base as qos_base
from neutron.agent.l3.extensions.qos import fip as qos_fip
from neutron.agent.linux import interface as ovs_interface
from neutron.api.rpc.callbacks import events
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc
from neutron.common import constants as n_constants
from neutron.common import coordination

LOG = logging.getLogger(__name__)

QOS_PRIORITY = 50
OPENFLOW13 = "OpenFlow13"
RULE_TYPE_PACKET_RATE_LIMIT = n_constants.RULE_TYPE_PACKET_RATE_LIMIT
RULE_TYPE_BANDWIDTH_LIMIT = qos_consts.RULE_TYPE_BANDWIDTH_LIMIT
DEFAULT_TYPE = RULE_TYPE_BANDWIDTH_LIMIT


def gen_name(prefix, uuid):
    return (prefix + uuid)[:ovs_interface.OVSInterfaceDriver.DEV_NAME_LEN]


class BridgeFipRateLimitMaps(qos_fip.RouterFipRateLimitMaps):
    LOCK_NAME = "fip-bridge-qos-cache"

    def __init__(self, br_int):
        """
        The rate limits dict will be:
             xxx_ratelimits = {
                fip_1: meter_id
                fip_2: 1
            }
        """
        self.ingress_meters = {}
        self.egress_meters = {}

        """
         The router_used_meter_ids will be:
            router_used_meter_ids = {
                router_id_1: set("meter_id_1", "meter_id_2"),
                router_id_2: set("meter_id_2", "meter_id_3")}
            }
        """
        self.router_used_meter_ids = {}
        self.max_meter = 0
        self.br_int = br_int
        self.br_int.use_at_least_protocol(OPENFLOW13)
        self._init_max_meter_id()
        super(BridgeFipRateLimitMaps, self).__init__()

    def _init_max_meter_id(self):
        features = self.br_int.list_meter_features()
        br_max_meter = int(features[0])
        if br_max_meter > 0:
            self.max_meter = br_max_meter

    @coordination.synchronized('meter-id-lock-{router_id}')
    def generate_meter_id(self, router_id):
        used_meter_id = self.router_used_meter_ids.get(router_id, set())
        mid = None
        times = 0
        while not mid or mid in used_meter_id:
            mid = random.randint(1, self.max_meter)
            times += 1
            if times >= self.max_meter:
                return
        used_meter_id.add(mid)
        self.router_used_meter_ids[router_id] = used_meter_id
        return mid

    def _remove_meter_id(self, router_id, meter_id):
        used_meter_ids = self.router_used_meter_ids.get(router_id, set())
        try:
            used_meter_ids.remove(meter_id)
        except KeyError:
            pass
        self.router_used_meter_ids[router_id] = used_meter_ids

    def remove_meter_id(self, router_id, meter_id):
        self._remove_meter_id(router_id, meter_id)

    def set_fip_meter_cache(self, direction, fip, meter):

        @lockutils.synchronized(self.lock_name)
        def _set_fip_meter_cache():
            rate_limits_direction = direction + "_meters"
            rate_limits = getattr(self, rate_limits_direction, {})
            rate_limits[fip] = meter

        _set_fip_meter_cache()

    def get_fip_meter_cache(self, direction, fip):

        @lockutils.synchronized(self.lock_name)
        def _get_fip_meter_cache():
            rate_limits_direction = direction + "_meters"
            rate_limits = getattr(self, rate_limits_direction, {})
            meter = rate_limits.get(fip)
            return meter

        return _get_fip_meter_cache()

    def remove_fip_meter_cache(self, direction, fip):

        @lockutils.synchronized(self.lock_name)
        def _remove_fip_meter_cache():
            rate_limits_direction = direction + "_meters"
            rate_limits = getattr(self, rate_limits_direction, {})
            rate_limits.pop(fip, None)

        _remove_fip_meter_cache()

    def remove_fip_all_cache(self, fip):
        for direction in constants.VALID_DIRECTIONS:
            self.remove_fip_meter_cache(direction, fip)
        self.clean_by_resource(fip)

    def clean_router_all_fip_cache(self, router_id):
        floating_ips = self.router_floating_ips.pop(
            router_id, [])
        for fip in floating_ips:
            self.remove_fip_all_cache(fip)
        self.router_used_meter_ids.pop(router_id, None)


class BridgeFipQosAgentExtension(qos_base.L3QosAgentExtensionBase,
                                 l3_extension.L3AgentExtension):

    def __init__(self):
        self.int_br = ovs_lib.OVSBridge("br-int")
        self.dp_type = self.int_br.db_get_val(
            'Bridge', self.int_br.br_name,
            'datapath_type')
        self.meter_support = None

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        self.resource_rpc = resources_rpc.ResourcesPullRpcApi()
        self.fip_qos_map = BridgeFipRateLimitMaps(self.int_br)
        self._register_rpc_consumers()

    def _handle_notification(self, context, resource_type,
                             qos_policies, event_type):
        if event_type == events.UPDATED:
            for qos_policy in qos_policies:
                self._process_update_policy(qos_policy)

    def check_meter_features(self):
        features = self.int_br.list_meter_features()
        if (features[0] != 0 and features[1] != 0 and
                features[2] != 0 and features[3] != 0):
            return True
        return False

    def support_meter(self):
        if self.meter_support is None:
            self.meter_support = self.check_meter_features()
        return self.meter_support

    def get_dvr_bridge(self, router_id):
        bridge_name = gen_name("dvr-", router_id)
        br = ovs_lib.OVSBridge(bridge_name, self.dp_type)
        if not br.bridge_exists(bridge_name):
            LOG.debug("Router %s does not have a dvr bridge "
                      "skipping.", router_id)
            return
        br.use_at_least_protocol(OPENFLOW13)
        return br

    def _process_update_policy(self, qos_policy):
        old_qos_policy = self.fip_qos_map.get_policy(qos_policy.id)
        if old_qos_policy:
            if self._policy_rules_modified(old_qos_policy, qos_policy):
                for fip in self.fip_qos_map.get_resources(qos_policy):
                    router_id = self.fip_qos_map.find_fip_router_id(fip)
                    br = self.get_dvr_bridge(router_id)
                    if not br:
                        continue
                    rates = self.get_policy_rates(qos_policy)
                    self.process_ip_rates(fip, br, rates, router_id)

            self.fip_qos_map.update_policy(qos_policy)

    def process_floating_ip_addresses(self, context, router_info):
        is_distributed_router = router_info.router.get('distributed')
        agent_mode = router_info.agent_conf.agent_mode
        LOG.debug("Start processing floating IP QoS for "
                  "router %(router_id)s, router "
                  "distributed: %(distributed)s, "
                  "agent mode: %(agent_mode)s",
                  {"router_id": router_info.router_id,
                   "distributed": is_distributed_router,
                   "agent_mode": agent_mode})
        if not is_distributed_router or agent_mode != \
                n_constants.L3_AGENT_MODE_DVR_BRIDGE:
            # bridge_qos can only apply on dvr_bridge mode.
            return
        router_id = router_info.router_id

        br = self.get_dvr_bridge(router_id)
        if not br:
            return
        if not self.support_meter:
            LOG.debug("Meter feature was not support by ovs %s bridge",
                      br.br_name)
            return
        LOG.debug("Process router %s floating IPs.", router_id)
        if not self.fip_qos_map.router_used_meter_ids.get(router_id):
            br.cleanup_meters()
        floating_ips = router_info.get_floating_ips()
        current_fips = self.fip_qos_map.router_floating_ips.get(
            router_id, set())
        new_fips = set()
        for fip in floating_ips:
            fip_addr = fip['floating_ip_address']
            new_fips.add(fip_addr)
            rates = self.get_fip_qos_rates(context,
                                           fip_addr,
                                           fip.get(qos_consts.QOS_POLICY_ID))
            self.process_ip_rates(fip_addr, br, rates, router_id)
        self.fip_qos_map.router_floating_ips[router_id] = new_fips
        fips_removed = current_fips - new_fips
        for removed_fip in fips_removed:
            for direction in constants.VALID_DIRECTIONS:
                mid = self.fip_qos_map.get_fip_meter_cache(
                    direction, removed_fip)
                if mid:
                    self.remove_meter_from_fip(br, direction, removed_fip)
                    br.delete_meter(mid)
                    self.fip_qos_map.remove_fip_meter_cache(
                        direction, removed_fip)
                    self.fip_qos_map.remove_meter_id(router_id, mid)
            self.fip_qos_map.clean_by_resource(removed_fip)

    @coordination.synchronized('qos-floating-ip-rates-{fip}')
    def process_ip_rates(self, fip, br, rates, router_id):
        if not self.support_meter:
            LOG.debug("Meter feature was not support by ovs %s bridge",
                      br.br_name)
            return
        for direction in constants.VALID_DIRECTIONS:
            rate = rates.get(direction)
            mid = self.fip_qos_map.get_fip_meter_cache(direction, fip)
            if mid:
                if not rate:
                    self.remove_meter_from_fip(br, direction, fip)
                    br.delete_meter(mid)
                    self.fip_qos_map.remove_fip_meter_cache(direction, fip)
                    self.fip_qos_map.remove_meter_id(router_id, mid)
                else:
                    br.update_meter(mid, rate['rate'],
                                    rate['burst'], rate['type'])
            else:
                if not rate:
                    continue
                mid = self.fip_qos_map.generate_meter_id(router_id)
                br.add_meter(mid, rate['rate'],
                             rate['burst'], rate['type'])
                self.apply_meter_on_fip(br, mid, direction, fip)
                self.fip_qos_map.set_fip_meter_cache(direction, fip, mid)

    def remove_meter_from_fip(self, br, direction, fip_address):
        br.remove_meter_from_ip(n_constants.DVR_BRIDGE_FIP_QOS,
                                direction,
                                fip_address)

    def apply_meter_on_fip(self, br, mid, direction, fip_address):
        br.apply_meter_to_ip(n_constants.DVR_BRIDGE_FIP_QOS,
                             QOS_PRIORITY,
                             mid, direction,
                             fip_address, n_constants.DVR_BRIDGE_FIP_NAT)

    def get_fip_qos_rates(self, context, fip, policy_id):
        if policy_id is None:
            self.fip_qos_map.clean_by_resource(fip)
            return {}
        policy = self.resource_rpc.pull(
            context, resources.QOS_POLICY, policy_id)
        self.fip_qos_map.set_resource_policy(fip, policy)
        return self.get_policy_rates(policy)

    def get_policy_rates(self, policy):
        rates = {}
        for rule in policy.rules:
            if rule.rule_type == RULE_TYPE_PACKET_RATE_LIMIT and \
                    cfg.CONF.enable_fip_meter_packet_limit:
                if rule.direction not in rates:
                    rates[rule.direction] = {
                        "rate": rule.max_kpps * 1000,
                        "burst": rule.max_burst_kpps * 1000,
                        "type": n_constants.METER_FLAG_PPS}
            elif rule.rule_type == RULE_TYPE_BANDWIDTH_LIMIT:
                if rule.direction not in rates:
                    rates[rule.direction] = {
                        "rate": rule.max_kbps,
                        "burst": rule.max_burst_kbps,
                        "type": n_constants.METER_FLAG_BPS}

        for direction in constants.VALID_DIRECTIONS:
            if direction not in rates:
                LOG.debug("Policy %(id)s does not have '%(direction)s' "
                          "bandwidth_limit rule, use none instead.",
                          {"id": policy.id,
                           "direction": direction})
                rates[direction] = {}
        return rates

    def add_router(self, context, data):
        router_info = self._get_router_info(data['id'])
        if router_info:
            self.process_floating_ip_addresses(context, router_info)

    def update_router(self, context, data):
        router_info = self._get_router_info(data['id'])
        if router_info:
            self.process_floating_ip_addresses(context, router_info)

    def delete_router(self, context, data):
        self.fip_qos_map.clean_router_all_fip_cache(data['id'])

    def ha_state_change(self, context, data):
        pass
