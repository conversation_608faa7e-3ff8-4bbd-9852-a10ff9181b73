# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib import constants as lib_const
from oslo_log import log as logging

from neutron.agent.common import utils as common_utils
from neutron.agent.l3 import backend
from neutron.agent.l3 import namespace_manager
from neutron.agent.l3 import namespaces as l3_namespaces
from neutron.agent.linux import external_process
from neutron.agent.linux import pd
from neutron.agent.metadata import driver as metadata_driver

LOG = logging.getLogger(__name__)


class LinuxBackend(backend.Backend):

    def __init__(self, agent, use_ipv6=False):
        super(LinuxBackend, self).__init__(agent, use_ipv6)
        self.interface_driver = common_utils.load_interface_driver(agent.conf)
        self.metadata_driver = None
        if agent.conf.enable_metadata_proxy:
            self.metadata_driver = metadata_driver.MetadataDriver(agent)
        self.namespaces_manager = namespace_manager.NamespaceManager(
            agent.conf,
            self.interface_driver,
            self.metadata_driver)
        self.process_monitor = external_process.ProcessMonitor(
            config=agent.conf,
            resource_type='router')
        self.pd = pd.PrefixDelegation(
            agent.context,
            self.process_monitor,
            self.interface_driver,
            agent.plugin_rpc.process_prefix_update,
            agent.create_pd_router_update,
            agent.conf)

    def process_prefix_update(self):
        self.pd.process_prefix_update()

    def after_start(self):
        self.pd.after_start()

    def process_ha_state(self, router_id, state):
        self.pd.process_ha_state(router_id, state)

    def get_router_pd(self, router_id):
        return self.pd.routers.get(router_id)

    def set_router_pd(self, router_id, router):
        self.pd.routers[router_id] = router

    def delete_router_pd(self, router_id):
        router = self.get_router_pd(router_id)
        self.pd.delete_router_pd(router)
        del self.pd.routers[router_id]['subnets']
        del self.pd.routers[router_id]

    def sync_router_pd(self, router_id):
        self.pd.sync_router(router_id)

    def spawn_monitored_metadata_proxy(self, router_info):
        proxy = self.metadata_driver
        if not proxy:
            return
        for c, r in proxy.metadata_filter_rules(proxy.metadata_port,
                                                proxy.metadata_access_mark):
            router_info.iptables_manager.ipv4['filter'].add_rule(c, r)
        for c, r in proxy.metadata_nat_rules(proxy.metadata_port):
            router_info.iptables_manager.ipv4['nat'].add_rule(c, r)
        if self.agent.conf.enable_metrics_proxy and \
                self.agent.conf.metrics_proxy_socket:
            for c, r in proxy.metrics_proxy_filter_rules(
                    proxy.metadata_port + 1, proxy.metadata_access_mark):
                router_info.iptables_manager.ipv4['filter'].add_rule(c, r)
            for c, r in proxy.metrics_proxy_nat_rules(proxy.metadata_port + 1):
                router_info.iptables_manager.ipv4['nat'].add_rule(c, r)
        router_info.iptables_manager.apply()

        self.metadata_driver.spawn_monitored_metadata_proxy(
            self.process_monitor, router_info.ns_name,
            self.agent.conf.metadata_port, self.agent.conf,
            router_id=router_info.router_id)

    def destroy_monitored_metadata_proxy(self, router_info):
        self.metadata_driver.destroy_monitored_metadata_proxy(
            self.process_monitor, router_info.router_id,
            self.agent.conf, router_info.ns_name)

    def update_metadata_rules(self, router_info):
        proxy = self.metadata_driver

        for c, r in proxy.metadata_filter_rules(proxy.metadata_port,
                                                proxy.metadata_access_mark):
            router_info.iptables_manager.ipv4['filter'].add_rule(c, r)
        for c, r in proxy.metadata_nat_rules(proxy.metadata_port):
            router_info.iptables_manager.ipv4['nat'].add_rule(c, r)
        router_info.iptables_manager.apply()

    def ensure_router_cleanup(self, router_id):
        self.namespaces_manager.ensure_router_cleanup(router_id)

    @property
    def routers_manager(self):
        return self.namespaces_manager

    def check_ha_router_status(self, context):
        """Check HA router VRRP process status in network node.

        Check if the HA router HA routers VRRP (keepalived) process count
        and state change python monitor process count meet the expected
        quantity. If so, l3-agent will not call neutron to set all related
        HA port to down state, this can prevent some unexpected VRRP
        re-election. If not, a physical host may have down and just
        restarted, set HA network port status to DOWN.
        """
        if (self.agent.conf.agent_mode not in [
            lib_const.L3_AGENT_MODE_DVR_SNAT,
            lib_const.L3_AGENT_MODE_LEGACY
        ]):
            return

        if self.agent.ha_router_count <= 0:
            return

        # Only set HA ports down when host was rebooted so no net
        # namespaces were still created.
        if any(ns.startswith(l3_namespaces.NS_PREFIX) for ns in
               self.namespaces_manager.list_all()):
            LOG.debug("Network configuration already done. Skipping "
                      "set HA port to DOWN state.")
            return

        LOG.debug("Call neutron server to set HA port to DOWN state.")
        try:
            # We set HA network port status to DOWN to let l2 agent
            # update it to ACTIVE after wiring. This allows us to spawn
            # keepalived only when l2 agent finished wiring the port.
            self.agent.plugin_rpc.update_all_ha_network_port_statuses(context)
        except Exception:
            LOG.exception('update_all_ha_network_port_statuses failed')
