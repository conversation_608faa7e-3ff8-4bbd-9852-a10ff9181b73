# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import random

from neutron_lib.agent import topics
from neutron_lib.api import extensions
from neutron_lib import constants
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_log import log as logging
import oslo_messaging

from neutron.common import rpc as n_rpc

LOG = logging.getLogger(__name__)


class MeteringAgentNotifyAPI(object):
    """API for plugin to notify L3 metering agent."""

    def __init__(self, topic=topics.METERING_AGENT):
        self.topic = topic
        target = oslo_messaging.Target(topic=topic, version='1.0')
        self.client = n_rpc.get_client(target)

    def _agent_notification(self, context, method, routers):
        """Notify l3 metering agents hosted by l3 agent hosts."""
        adminContext = context if context.is_admin else context.elevated()
        plugin = directory.get_plugin(plugin_constants.L3)

        for router in routers:
            hosts = plugin.get_hosts_to_notify(adminContext, router['id'])
            random.shuffle(hosts)
            for host in hosts:
                LOG.debug('Notify metering agent at %(topic)s.%(host)s '
                          'the message %(method)s',
                          {'topic': self.topic,
                           'host': host,
                           'method': method})
                cctxt = self.client.prepare(server=host)
                cctxt.cast(context, method, routers=routers)

    def _notification_metering(self, context, method, router):
        admin_context = context if context.is_admin else context.elevated()
        plugin = directory.get_plugin(plugin_constants.L3)
        router_id = router.get('router_id')
        hosts = plugin.get_hosts_to_notify(admin_context, router_id)
        for host in hosts:
            cctxt = self.client.prepare(server=host)
            cctxt.cast(context, method, router=router)

    def _notification_fanout(self, context, method, router_id):
        LOG.debug('Fanout notify metering agent at %(topic)s the message '
                  '%(method)s on router %(router_id)s',
                  {'topic': self.topic,
                   'method': method,
                   'router_id': router_id})
        cctxt = self.client.prepare(fanout=True)
        cctxt.cast(context, method, router_id=router_id)

    def _notification_host(self, context, method, host, **kwargs):
        """Notify the agent that is hosting the router."""
        LOG.debug('Notify agent at %(host)s the message '
                  '%(method)s', {'host': host,
                                 'method': method})
        cctxt = self.client.prepare(server=host)
        cctxt.cast(context, method, **kwargs)

    def _notification(self, context, method, routers):
        """Notify all the agents that are hosting the routers."""
        plugin = directory.get_plugin(plugin_constants.L3)
        if extensions.is_extension_supported(
            plugin, constants.L3_AGENT_SCHEDULER_EXT_ALIAS):
            self._agent_notification(context, method, routers)
        else:
            cctxt = self.client.prepare(fanout=True)
            cctxt.cast(context, method, routers=routers)

    def router_deleted(self, context, router_id):
        self._notification_fanout(context, 'router_deleted', router_id)

    def update_metering(self, context, router):
        """
        @param router: it is dictionary about metering information, must
                       include following attribute, such as:
                       router_id, floatingip_id, label_id, label_type.
        """
        self._notification_metering(context, 'update_metering', router)

    def routers_updated(self, context, routers):
        if routers:
            self._notification(context, 'routers_updated', routers)

    def update_metering_label_rules(self, context, routers):
        self._notification(context, 'update_metering_label_rules', routers)

    def add_metering_label_rule(self, context, routers):
        self._notification(context, 'add_metering_label_rule', routers)

    def remove_metering_label_rule(self, context, routers):
        self._notification(context, 'remove_metering_label_rule', routers)

    def clean_metering_router_iptable(self, context, meter_routers):
        routers = []
        clean_info = meter_routers.split('+')
        floatingip_id = clean_info[-1]
        host = clean_info[-2]
        router_ids = clean_info[0]
        ver = clean_info[-3]
        ids = router_ids.split(',')
        for id in ids:
            router = {'id': id, 'ver': ver, 'host': host,
                      'floatingip_id': floatingip_id}
            routers.append(router)

        if host == 'ha-host':
            if len(routers) == 0:
                LOG.error("Must specif router id.")
                return
            self._notification(
                context, 'clean_metering_router_iptable', routers)
        elif host == 'all':
            if len(ids) != 1:
                LOG.error("Do not support multi router ids.")
                return
            router_id = ids
            self._notification_fanout(
                context, 'clean_metering_one_router_iptable', router_id)
        else:
            self._notification_host(
                context,
                'clean_metering_router_iptable',
                host,
                routers=routers)

    def add_metering_label(self, context, routers):
        self._notification(context, 'add_metering_label', routers)

    def remove_metering_label(self, context, routers):
        self._notification(context, 'remove_metering_label', routers)

    def routers_updated_on_host(self, context, router_ids, host):
        """Notify router updates to specific hosts hosting DVR routers."""
        self._notification_host(context, 'routers_updated', host,
                                routers=router_ids)

    def update_metering_vpc(self, context, metering_vpc):
        """Notify agent to add metering vpc"""
        cctxt = self.client.prepare(fanout=True)
        cctxt.cast(context, 'update_metering_vpc', metering_vpc=metering_vpc)

    def add_metering_vpc(self, context, metering_vpc):
        """Notify agent to add metering vpc"""
        cctxt = self.client.prepare(fanout=True)
        cctxt.cast(context, 'add_metering_vpc', metering_vpc=metering_vpc)

    def remove_metering_vpc(self, context, metering_vpc):
        """Notify agent to add metering vpc"""
        cctxt = self.client.prepare(fanout=True)
        cctxt.cast(context, 'remove_metering_vpc', metering_vpc=metering_vpc)
