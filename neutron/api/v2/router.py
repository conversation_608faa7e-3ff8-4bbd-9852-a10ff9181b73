# Copyright (c) 2012 OpenStack Foundation.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.


from neutron.pecan_wsgi import app as pecan_app


def APIRouter(**local_config):
    return pecan_app.v2_factory(None, **local_config)


def _factory(global_config, **local_config):
    return pecan_app.v2_factory(global_config, **local_config)


setattr(APIRouter, 'factory', _factory)
