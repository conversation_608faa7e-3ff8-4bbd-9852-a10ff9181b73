#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import ctypes
from ctypes import util
import logging
import time

from neutron.common import exceptions
from neutron.privileged.agent.linux import netlink_constants as nl_constants

nfct = ctypes.CDLL(util.find_library('netfilter_conntrack'))
NFCT_CALLBACK = ctypes.CFUNCTYPE(ctypes.c_int, ctypes.c_int,
                                 ctypes.c_void_p, ctypes.c_void_p)

NFCT_Q_DUMP_FILTER = 8

ATTR_ORIG_L4PROTO = 17
ATTR_TCP_STATE = 19

# global var define
tcp = 0
udp = 0
icmp = 0
established = 0
total = 0

LOG = logging.getLogger(__name__)


class ConntrackManager(object):
    """
    Use below to display buffer:
    nfct.nfct_snprintf(raw_entry, nl_constants.BUFFER,
                     conntrack, type_,
                        nl_constants.NFCT_O_PLAIN,
                        nl_constants.NFCT_OF_TIME)
    print "%s" % raw_entry.value.decode('utf-8')
    """
    def show(self):
        begin = time.time()
        output = '\ntcp={} est={} udp={} icmp={} all={}'.format(
            tcp, established, udp, icmp, total)

        @NFCT_CALLBACK
        def callback_func(type_, conntrack, data):
            global tcp, udp, icmp, established, total
            if conntrack is None:
                print('conntrack is None')
                print(output)
                return nl_constants.NFCT_CB_CONTINUE

            total += 1
            nfct.nfct_get_attr_u8.argtypes = (ctypes.c_ulong,)
            nfct.nfct_get_attr_u8.restype = ctypes.c_ulong
            pro = nfct.nfct_get_attr_u8(conntrack, ATTR_ORIG_L4PROTO)
            if pro == 6:
                tcp += 1
                nfct.nfct_get_attr_u8.argtypes = (ctypes.c_ulong,)
                nfct.nfct_get_attr_u8.restype = ctypes.c_ulong
                est = nfct.nfct_get_attr_u8(conntrack, ATTR_TCP_STATE)
                if est == 3:
                    established += 1

            elif pro == 17:
                udp += 1
            elif pro == 1:
                icmp += 1
            else:
                pass

            return nl_constants.NFCT_CB_CONTINUE

        nfct.nfct_callback_register.argtypes = (ctypes.c_ulong,)
        nfct.nfct_callback_register.restype = ctypes.c_ulong
        nfct.nfct_callback_register(self.conntrack_handler,
                                    nl_constants.NFCT_T_ALL, callback_func,
                                    None)

        qd = ctypes.byref(ctypes.c_int(nl_constants.IPVERSION_SOCKET[4]))
        nfct.nfct_query.argtypes = (ctypes.c_ulong,)
        nfct.nfct_query.restype = ctypes.c_ulong
        result = nfct.nfct_query(self.conntrack_handler,
                                 nl_constants.NFCT_Q_DUMP,
                                 qd)
        if result == nl_constants.NFCT_CB_FAILURE:
            print(output)
            print("Netlink query failed")

        end = time.time()

        print('\ntcp={} est={} udp={} icmp={} all={} escaped={}'.format(
            tcp, established, udp, icmp, total, end - begin))

        return

    def __enter__(self):
        nfct.nfct_open.argtypes = (ctypes.c_ulong, ctypes.c_uint)
        nfct.nfct_open.restype = ctypes.c_ulong
        self.conntrack_handler = nfct.nfct_open(
            nl_constants.CONNTRACK,
            nl_constants.NFNL_SUBSYS_CTNETLINK)
        if not self.conntrack_handler:
            raise exceptions.CTZoneExhaustedError()
        return self

    def __exit__(self, *args):
        nfct.nfct_close.argtypes = (ctypes.c_ulong,)
        nfct.nfct_close.restype = ctypes.c_ulong
        nfct.nfct_close(self.conntrack_handler)


def check_conntrack_main():
    with ConntrackManager() as cm:
        cm.show()


if __name__ == '__main__':
    check_conntrack_main()
