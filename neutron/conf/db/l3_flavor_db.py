#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.


from oslo_config import cfg

from neutron._i18n import _


L3_FLAVOR_OPTS = [
    cfg.StrOpt('l3_default_flavor',
               default='ha',
               choices=['dvr', 'dvrha', 'ha', 'legacy',
                        'vpp', 'huawei', 'h3c'],
               help=_('The default flavor when creating a router.'
                      'This parameter is valid only when openstack network '
                      'flavor is created.'
                      'SDN architecture support: vpp, huawei, h3c, ha, legacy.'
                      'Non SDN architecture support: dvr, dvrha, ha, legacy.'))
]


def register_db_l3_flavor_opts(conf=cfg.CONF):
    conf.register_opts(L3_FLAVOR_OPTS)
