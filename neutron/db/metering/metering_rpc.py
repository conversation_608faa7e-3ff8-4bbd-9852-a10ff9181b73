# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

from neutron_lib.api import extensions
from neutron_lib import constants as consts
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_log import log as logging
import oslo_messaging

LOG = logging.getLogger(__name__)


class MeteringRpcCallbacks(object):

    target = oslo_messaging.Target(version='1.0')

    def __init__(self, meter_plugin):
        self.meter_plugin = meter_plugin

    def metering_router_vpc_callback(self, context, host, flag='all'):
        metering_router_vpc = {'router': [], 'vpc2vpc': []}
        if flag == 'router':
            metering_router_vpc['router'] = \
                self.meter_plugin.metering_router_callback(context, host)
            return metering_router_vpc['router']
        elif flag == 'vpc2vpc':
            metering_router_vpc['vpc2vpc'] = \
                self.get_plugin_metering_vpcs(context, host)
            return metering_router_vpc['vpc2vpc']
        else:
            metering_router_vpc['router'] = \
                self.meter_plugin.metering_router_callback(context, host)
            metering_router_vpc['vpc2vpc'] = self.get_plugin_metering_vpcs(
                context, host)
            return metering_router_vpc

    def get_plugin_metering_vpcs(self, context, host):
        metering_vpcs_db = self.meter_plugin.get_metering_vpcs(context)
        dhcp_networks_db = self.meter_plugin.get_host_dhcp_networks(
            context, host)
        metering_vpcs = []
        for vpc in metering_vpcs_db:
            network_list = vpc['network_id']
            if not network_list[0]:
                network_list[0] = self.meter_plugin.set_vpc_default_network(
                    context, vpc['id'], vpc['router_id'])
            if set(network_list).intersection(dhcp_networks_db):
                metering_vpcs.append(vpc)
        return metering_vpcs

    def sync_metering_vpc_routers(self, context, host, sync_vpcs):
        self.meter_plugin.sync_metering_vpc_routers(context, host, sync_vpcs)

    def get_sync_data_metering(self, context, **kwargs):
        l3_plugin = directory.get_plugin(plugin_constants.L3)
        if not l3_plugin:
            return

        host = kwargs.get('host')
        if not extensions.is_extension_supported(
            l3_plugin, consts.L3_AGENT_SCHEDULER_EXT_ALIAS) or not host:
            LOG.error('Unable to find host %s or unsupport L3.', host)
            return
        else:
            agents = l3_plugin.get_l3_agents(context, filters={
                'host': [host]})
            if not agents:
                LOG.error('Unable to find agent on host %s.', host)
                return

            metering_data = \
                self.meter_plugin.get_sync_full_data_metering_by_agents(
                    context, l3_plugin, agents)

            return metering_data

    def sync_router_metering(self, context, router):
        routers = self.meter_plugin.sync_router_metering(
                context, router)
        return routers

    def get_metering_fip(self, context, floatingip_id):
        l3_plugin = directory.get_plugin(plugin_constants.L3)
        if not l3_plugin:
            return
        floatingip = l3_plugin.get_floatingip(
            context, floatingip_id)
        return floatingip
