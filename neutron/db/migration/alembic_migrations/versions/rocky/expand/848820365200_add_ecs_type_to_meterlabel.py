# Copyright 2021 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add_ecs_type_to_meterlabel

Revision ID: 848820365200
Revises: 75de5ad18d3d
Create Date: 2021-04-12 15:35:27.867548

"""

from alembic import op
import sqlalchemy as sa

from neutron.common import constants as n_const
from neutron.db import migration

# revision identifiers, used by Alembic.
revision = '848820365200'
down_revision = '75de5ad18d3d'


new_type_enum = sa.Enum(n_const.METERING_TYPE_EIP,
                        n_const.METERING_TYPE_PF_EIP,
                        n_const.METERING_TYPE_SNAT_EIP,
                        n_const.METERING_TYPE_ECS_EIP)


def upgrade():
    table_name = 'meteringlabels'
    existColumn = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        if column['name'] == 'type':
            existColumn = True

    if existColumn:
        migration.alter_enum_add_value(
            table_name, 'type', new_type_enum, nullable=False,
            server_default=n_const.METERING_TYPE_EIP)
    else:
        op.add_column(
            table_name,
            sa.Column('type', sa.Enum(n_const.METERING_TYPE_EIP,
                                      n_const.METERING_TYPE_PF_EIP,
                                      n_const.METERING_TYPE_SNAT_EIP,
                                      n_const.METERING_TYPE_ECS_EIP),
                      nullable=False,
                      server_default=n_const.METERING_TYPE_EIP))
