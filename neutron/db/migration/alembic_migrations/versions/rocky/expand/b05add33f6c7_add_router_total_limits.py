# Copyright 2021 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""Add router attributes

Revision ID: b05add33f6c7
Revises: d01a93c3da16
Create Date: 2021-11-05 16:49:56.817855

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'b05add33f6c7'
down_revision = 'd01a93c3da16'


def upgrade():
    exist_router_total_limits = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'router_total_limits':
            exist_router_total_limits = True
            break

    if not exist_router_total_limits:
        op.create_table(
            'router_total_limits',
            sa.Column('router_id', sa.String(length=36), nullable=False),
            sa.Column('total_bandwidth', sa.Integer()),
            sa.Column('total_packet_rate', sa.Integer()),
            sa.Column('total_connection', sa.Integer()),
            sa.ForeignKeyConstraint(
                ['router_id'], ['routers.id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('router_id')
        )
