# Copyright 2019 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""subnet force network id

Revision ID: c613d0b82681
Revises: d72db3e25539
Create Date: 2019-08-19 11:15:14.443244

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'c613d0b82681'
down_revision = 'd72db3e25539'


def upgrade():
    op.drop_constraint(constraint_name="subnets_ibfk_1", table_name="subnets",
                       type_="foreignkey")
    op.alter_column('subnets', 'network_id', nullable=False,
                    existing_type=sa.String(36))
    op.create_foreign_key(
        constraint_name="subnets_ibfk_1",
        source_table="subnets",
        referent_table="networks",
        local_cols=["network_id"],
        remote_cols=["id"])


def expand_drop_exceptions():
    """Drop and recreate the foreignkey network_id for table subnets
    """

    return {
        sa.Constraint: [
            # mysql foreign keys
            "subnets_ibfk_1",
            # postgresql foreign keys
            "subnets_network_id_fkey",
        ]
    }
