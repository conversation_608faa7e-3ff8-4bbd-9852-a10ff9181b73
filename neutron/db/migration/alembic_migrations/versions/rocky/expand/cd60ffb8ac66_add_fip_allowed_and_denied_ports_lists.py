# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add fip allowed and denied ports lists

Revision ID: cd60ffb8ac66
Revises: cae680a5b17d
Create Date: 2020-12-08 16:08:48.810036

"""

from alembic import op
import sqlalchemy as sa

from neutron_lib.db import constants

# revision identifiers, used by Alembic.
revision = 'cd60ffb8ac66'
down_revision = 'cae680a5b17d'


def upgrade():
    exist_wo_fip_ad_ports_lists = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'wo_fip_ad_ports_lists':
            exist_wo_fip_ad_ports_lists = True
            break

    if not exist_wo_fip_ad_ports_lists:
        op.create_table(
            'wo_fip_ad_ports_lists', sa.Column(
                'floatingip_id', sa.String(
                    length=constants.UUID_FIELD_SIZE), nullable=False),
            sa.Column('allowed_port_numbers', sa.String(length=4095)),
            sa.Column('denied_port_numbers', sa.String(length=4095)),
            sa.ForeignKeyConstraint(
                ['floatingip_id'], ['floatingips.id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('floatingip_id')
        )
