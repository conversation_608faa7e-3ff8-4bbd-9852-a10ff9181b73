# Copyright 2022 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add index to agents host

Revision ID: cd9ef14ccf87
Revises: b05add33f6c7
Create Date: 2022-01-07 15:45:33.319170

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'cd9ef14ccf87'
down_revision = 'b05add33f6c7'


TABLE = 'agents'
COLUMN = 'host'
INDEX_NAME = 'ix_' + TABLE + '_' + COLUMN


def upgrade():
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    if INDEX_NAME not in [idx['name'] for idx in
                          insp.get_indexes(TABLE)]:
        op.create_index(op.f(INDEX_NAME), TABLE, [COLUMN])
