# Copyright (c) 2023 China Unicom Cloud Data Co.,Ltd.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add_router_type_to_router

Revision ID: d274b97e3160
Revises: e9976f8d8e78
Create Date: 2023-03-03 10:55:58.497789

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'd274b97e3160'
down_revision = 'e9976f8d8e78'


def upgrade():
    table_name = 'router_extra_attributes'
    exist_column = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        if column['name'] == 'custom_type':
            exist_column = True
    if not exist_column:
        custom_type_col = sa.Column('custom_type', sa.String(255))
        op.add_column(table_name, custom_type_col)
