# Copyright 2023 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add_indexes

Revision ID: e241a5f7285e
Revises: d274b97e3160
Create Date: 2023-07-11 14:21:00.971010

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'e241a5f7285e'
down_revision = 'd274b97e3160'


def _add_index(table, column):
    index_name = 'ix_' + table + '_' + column
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    if index_name not in [idx['name'] for idx in
                          insp.get_indexes(table)]:
        op.create_index(op.f(index_name), table, [column])


def upgrade():
    idx_entries = [('ports', 'device_owner'),
                   ('ml2_port_bindings', 'host'),
                   ('ml2_port_binding_levels', 'host')]
    for table, colume in idx_entries:
        _add_index(table, colume)
