#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""elastic_snat

Revision ID: e9f568f45240
Revises: 1bb3393de75d
Create Date: 2018-08-20 17:16:10.323756

"""

from alembic import op
import sqlalchemy as sa

from neutron_lib.db import constants

# revision identifiers, used by Alembic.
revision = 'e9f568f45240'
down_revision = '1bb3393de75d'


def upgrade():
    op.create_table(
        'elastic_snats',
        sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('name', sa.String(length=255), nullable=True),
        sa.Column('floatingip_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('router_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('gateway_port_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('project_id',
                  sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                  nullable=True,
                  index=True),
        sa.ForeignKeyConstraint(['floatingip_id'], ['floatingips.id']),
        sa.ForeignKeyConstraint(['router_id'], ['routers.id']),
        sa.ForeignKeyConstraint(['gateway_port_id'], ['ports.id']),
        sa.PrimaryKeyConstraint('id'),

        sa.UniqueConstraint('floatingip_id',
                            name='uniq_elastic_snats0floatingip_id'),
    )

    op.create_table(
        'elastic_snat_rules',
        sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('elastic_snat_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('router_id', sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('floatingip_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('subnet_id', sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=True),
        sa.Column('internal_cidr', sa.String(length=64)),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['elastic_snat_id'], ['elastic_snats.id'],
                                ondelete='CASCADE'),

        sa.UniqueConstraint('router_id', 'subnet_id',
                            name='uniq_elastic_snat_rules0'
                                 'router_id0subnet_id'),
        sa.UniqueConstraint('router_id', 'internal_cidr',
                            name='uniq_elastic_snat_rules0router_id0'
                                 'internal_cidr'),
    )
