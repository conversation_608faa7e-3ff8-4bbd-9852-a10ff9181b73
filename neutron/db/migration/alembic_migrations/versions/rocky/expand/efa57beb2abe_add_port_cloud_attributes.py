# Copyright (c) 2023 China Unicom Cloud Data Co.,Ltd.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add_port_cloud_attributes

Revision ID: efa57beb2abe
Revises: e241a5f7285e
Create Date: 2024-04-15 11:37:34.834006

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'efa57beb2abe'
down_revision = 'e241a5f7285e'


def upgrade():
    exist_port_cloud_attributes = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'portcloudattributes':
            exist_port_cloud_attributes = True
            break
    if not exist_port_cloud_attributes:
        op.create_table(
            'portcloudattributes',
            sa.Column('id', sa.String(length=36), nullable=False),
            sa.Column('cloud_attrs', sa.String(4095)),
            sa.ForeignKeyConstraint(
                ['id'], ['ports.id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('id')
        )
