# Copyright 2013 VMware, Inc.  All rights reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api.definitions import port as port_def
from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources

from neutron_lib.plugins import directory
from oslo_log import log as logging
from oslo_serialization import jsonutils

from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.extensions import _port_cloud_attributes_lib as cloud_attrs
from neutron.extensions import port_cloud_attributes as port_attr_ext
from neutron.objects import ports as ports_obj

LOG = logging.getLogger(__name__)


@resource_extend.has_resource_extenders
class PortCloudAttributesDbMixin(object):
    """Mixin class to extend port's cloud attributes."""

    @staticmethod
    @resource_extend.extends([port_def.COLLECTION_NAME])
    def _extend_port_cloud_attributes(port_res, port):
        attrs = port.cloud_attributes
        if attrs:
            try:
                port_res[cloud_attrs.COLLECTION_NAME] = \
                    jsonutils.loads(attrs.cloud_attrs)
            except Exception:
                LOG.error("Serialized profile DB value '%(value)s' for "
                          "port %(port)s is invalid.",
                          {'value': attrs.cloud_attrs,
                           'port': port.id})
        else:
            port_res[cloud_attrs.COLLECTION_NAME] = {}
        return port_res

    def _process_port_create_port_cloud_attrs(
            self, context, port, attrs):
        if not attrs:
            return port
        with db_api.context_manager.writer.using(context):
            port_cloud_obj = ports_obj.PortCloudAttribute(
                context, id=port['id'],
                cloud_attrs=attrs)
            port_cloud_obj.create()
        port[cloud_attrs.COLLECTION_NAME] = jsonutils.dumps(
            attrs)
        extra_attrs_obj = self._get_port_cloud_attrs(context, port['id'])
        port[cloud_attrs.COLLECTION_NAME] = extra_attrs_obj.cloud_attrs
        return port[cloud_attrs.COLLECTION_NAME]

    def _get_port_cloud_attrs(self, context, port_id):
        port_extra_obj = ports_obj.PortCloudAttribute.get_object(
            context, id=port_id)
        if port_extra_obj is None:
            raise port_attr_ext.PortCloudAttributeNotFound(port_id=port_id)
        return port_extra_obj

    def _process_port_update_port_cloud_attrs(
            self, context, id, port, updated_port):
        # update port cloud attribute for one key
        new_attrs = port['port'].get(cloud_attrs.COLLECTION_NAME)
        if not new_attrs:
            return bool(new_attrs)
        if not updated_port.get(cloud_attrs.COLLECTION_NAME):
            self._process_port_create_port_cloud_attrs(
                context, updated_port, new_attrs)
            LOG.info("Create port %s cloud attributes.", id)
        else:
            port_attrs_dict = updated_port.get(
                cloud_attrs.COLLECTION_NAME)
            for k, v in new_attrs.items():
                port_attrs_dict[k] = v
                LOG.info("Update port %(port_id)s cloud attribute "
                         "%(key)s's value, the current cloud "
                         "attribute is %(key)s:%(value)s.",
                         {'port_id': id,
                          'key': k,
                          'value': port_attrs_dict[k]})
            with db_api.context_manager.writer.using(context):
                port_attrs_obj = self._get_port_cloud_attrs(context, id)
                port_attrs_obj.update_fields({'cloud_attrs': port_attrs_dict})
                port_attrs_obj.update()
        LOG.info("Finish update port %s cloud attributes.", id)
        return bool(new_attrs)

    @db_api.retry_if_session_inactive()
    def get_port_cloud_attributes(self, context, port_id):
        obj = self._get_port_cloud_attrs(context, port_id)
        attrs = obj.cloud_attrs
        result = {cloud_attrs.COLLECTION_NAME: attrs} if attrs else None
        return result

    @db_api.retry_if_session_inactive()
    def extend_port_cloud_attribute(self, context, id, extend_attrs):
        # extend port cloud attribute for one key
        plugin = directory.get_plugin()
        port = plugin.get_port(context, id)
        port_attrs_dict = port.get(cloud_attrs.COLLECTION_NAME)
        new_attrs = extend_attrs.get(cloud_attrs.COLLECTION_NAME)
        original_port = port
        if not port_attrs_dict:
            port_attrs_dict = self._process_port_create_port_cloud_attrs(
                context, port, new_attrs)
        else:
            for k, v in new_attrs.items():
                value_cache = []
                if k not in port_attrs_dict.keys():
                    port_attrs_dict[k] = v
                else:
                    old_value = port_attrs_dict[k]
                    if not isinstance(old_value, list):
                        LOG.warning("Original attributes %s value type of "
                                    "port to extend should be list. ",
                                    old_value)
                        continue
                    for i in old_value:
                        value_cache.append(i)
                    if isinstance(v, list):
                        for i in v:
                            if i in value_cache:
                                continue
                            value_cache.append(i)
                    else:
                        if v in value_cache:
                            continue
                        value_cache.append(v)
                    port_attrs_dict[k] = value_cache
                LOG.info("Update port %(port_id)s cloud attribute "
                         "%(key)s's value, the current cloud "
                         "attribute is %(dict)s.",
                         {'port_id': id,
                          'key': k,
                          'dict': port_attrs_dict})

            with db_api.context_manager.writer.using(context):
                port_attrs_obj = self._get_port_cloud_attrs(context, id)
                port_attrs_obj.update_fields({'cloud_attrs': port_attrs_dict})
                port_attrs_obj.update()
        kwargs = {
            'context': context,
            'port': port,
            'original_port': original_port,
        }
        registry.notify(resources.PORT, events.AFTER_UPDATE, self,
                        **kwargs)
        LOG.info("Finish update port %s cloud attributes.", id)
        return {cloud_attrs.COLLECTION_NAME: port_attrs_dict}

    @db_api.retry_if_session_inactive()
    def remove_port_cloud_attribute(self, context, id, key):
        plugin = directory.get_plugin()
        port = plugin.get_port(context, id)
        if key not in port[cloud_attrs.COLLECTION_NAME].keys():
            raise port_attr_ext.AttributeKeyNotFound(key=key, port_id=id)
        self.delete_port_cloud_attribute(context, port, key)

    @db_api.retry_if_session_inactive()
    def remove_port_cloud_attributes(self, context, id, remove_attrs):
        plugin = directory.get_plugin()
        port = plugin.get_port(context, id)
        attributes = remove_attrs[cloud_attrs.COLLECTION_NAME]
        # Delete port cloud attributes based keys
        del_key = []
        if isinstance(attributes, list):
            for key in attributes:
                if key not in port[cloud_attrs.COLLECTION_NAME].keys():
                    raise port_attr_ext.AttributeKeyNotFound(key=key,
                                                             port_id=id)
                del_key.append(key)
            for key in del_key:
                self.delete_port_cloud_attribute(context, port, key)
            return

        if not isinstance(attributes, dict):
            LOG.warning("To remove a single cloud attribute property, "
                        "the value type needs to be dict.")
            return
        original_attr = port[cloud_attrs.COLLECTION_NAME]

        del_attrs = {}
        for k, v in attributes.items():
            if k not in original_attr.keys():
                raise port_attr_ext.AttributeKeyNotFound(
                    key=k, port_id=id)
            if not isinstance(original_attr[k], list):
                if v != original_attr[k]:
                    raise port_attr_ext.AttributeValueNotFound(key=k)
                del_attrs[k] = original_attr[k]
                continue
            if isinstance(v, list):
                for item in v:
                    if item not in original_attr[k]:
                        raise port_attr_ext.AttributeValueNotFound(key=k)
            else:
                if v not in original_attr[k]:
                    raise port_attr_ext.AttributeValueNotFound(key=k)
            del_attrs[k] = v

        for k, v in del_attrs.items():
            if not isinstance(original_attr[k], list):
                self.delete_port_cloud_attribute(context, port, k)
                continue
            if isinstance(v, list):
                for item in v:
                    original_attr[k].remove(item)
            else:
                original_attr[k].remove(v)
            self.delete_port_cloud_attribute(context, port, k,
                                             original_attr[k])

    def delete_port_cloud_attribute(self, context, port, key, value=None):
        port_id = port.get('id')
        try:
            original_port = port
            if value:
                port[cloud_attrs.COLLECTION_NAME][key] = value
            else:
                del port[cloud_attrs.COLLECTION_NAME][key]

            with db_api.context_manager.writer.using(context):
                port_attrs_obj = self._get_port_cloud_attrs(context, port_id)
                port_attrs_obj.cloud_attrs = port.get(
                    cloud_attrs.COLLECTION_NAME)
                port_attrs_obj.update()
                LOG.info("Finish remove port %(port_id)s cloud attributes of "
                         "%(key)s.", {'port_id': port_id, 'key': key})
                if not port[cloud_attrs.COLLECTION_NAME]:
                    port_attrs_obj.delete()
                    LOG.info("Finish remove port %s all cloud attributes.",
                             port_id)
            kwargs = {
                'context': context,
                'port': port,
                'original_port': original_port,
            }
            registry.notify(resources.PORT, events.AFTER_UPDATE, self,
                            **kwargs)
        except Exception:
            LOG.error("Failed to remove port %(port)s cloud attributes.",
                      {'port': port_id})
