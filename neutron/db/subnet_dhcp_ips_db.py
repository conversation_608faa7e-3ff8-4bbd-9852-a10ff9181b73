# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.api.definitions import subnet as subnet_def
from neutron_lib import context

from neutron.db import _resource_extend as resource_extend
from neutron.objects import subnet as subnet_obj


@resource_extend.has_resource_extenders
class SubnetDhcpIPsMixin(object):
    """Mixin class of subnet dhcp ips."""

    @staticmethod
    @resource_extend.extends([subnet_def.COLLECTION_NAME])
    def _add_subnet_dhcp_ips_to_response(subnet_res, subnet_db):
        subnet_res['dhcp_ips'] = []
        ctx = context.get_admin_context()
        for ip in subnet_db.get('dhcp_ips', []):
            ip_address = ip['ip_address']
            allocated = subnet_obj.SubnetDhcpIP.is_dhcp_ip_allocated(
                ctx, subnet_db['id'], ip_address)
            subnet_res['dhcp_ips'].append({'ip_address': ip_address,
                                           'allocated': allocated})
