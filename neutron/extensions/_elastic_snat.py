#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api import converters
from neutron_lib.api.definitions import l3
from neutron_lib.api.definitions import l3_ext_gw_mode
from neutron_lib.db import constants as db_const

# The name of the extension.
NAME = 'Elastic SNAT'
ELASTIC_SNAT = "ELASTIC_SNAT"

# The alias of the extension.
ALIAS = 'router-elastic-snat'
# The description of the extension.
DESCRIPTION = "Extension to control router SNAT more granularly"

# A timestamp of when the extension was introduced.
UPDATED_TIMESTAMP = "2021-01-16T10:08:08-08:08"

# The name of the resource.
RESOURCE_NAME = 'elastic_snat'

# The plural for the resource.
COLLECTION_NAME = 'elastic_snats'

# IP address or CIDR format
IP_REGEX = (r'([0-9]{1,3}\.){3}[0-9]{1,3}($|/([0-32]))')

RESOURCE_ATTRIBUTE_MAP = {
    COLLECTION_NAME: {
        'id': {'allow_post': False,
               'allow_put': False,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_sort_key': True,
               'primary_key': True},
        'name': {'allow_post': True, 'allow_put': True,
                 'validate': {'type:string': db_const.NAME_FIELD_SIZE},
                 'is_sort_key': True,
                 'is_visible': True,
                 'default': ''},
        'floatingip_id': {'allow_post': True,
                          'allow_put': False,
                          'validate': {'type:uuid': None},
                          'default': None,
                          'is_sort_key': True,
                          'is_visible': True},
        'router_id': {'allow_post': True,
                      'allow_put': False,
                      'validate': {'type:uuid': None},
                      'is_sort_key': True,
                      'is_visible': True},
        'project_id': {'allow_post': True,
                       'allow_put': False,
                       'validate': {
                           'type:string': db_const.PROJECT_ID_FIELD_SIZE},
                       'required_by_policy': True,
                       'is_visible': True},
        'subnets': {
            'allow_post': True, 'allow_put': True,
            'validate': {'type:uuid_list': None},
            'convert_to': converters.convert_none_to_empty_list,
            'is_visible': True,
            'default': []},
        'internal_cidrs': {
            'allow_post': True, 'allow_put': True,
            'default': [],
            'validate': {'type:list_of_regex_or_none': IP_REGEX},
            'convert_to': converters.convert_none_to_empty_list,
            'is_visible': True}
    }
}

# The list of required extensions.
REQUIRED_EXTENSIONS = [l3.ALIAS, l3_ext_gw_mode.ALIAS]

# Whether or not this extension is simply signaling behavior to the user
# or it actively modifies the attribute map.
IS_SHIM_EXTENSION = False

# Whether the extension is marking the adoption of standardattr model for
# legacy resources, or introducing new standardattr attributes. False or
# None if the standardattr model is adopted since the introduction of
# resource extension.
# If this is True, the alias for the extension should be prefixed with
# 'standard-attr-'.
IS_STANDARD_ATTR_EXTENSION = False

# The subresource attribute map for the extension. It adds child resources
# to main extension's resource. The subresource map must have a parent and
# a parameters entry. If an extension does not need such a map, None can
# be specified (mandatory).
SUB_RESOURCE_ATTRIBUTE_MAP = {}

# The action map: it associates verbs with methods to be performed on
# the API resource.
ACTION_MAP = {}

# The list of required extensions.
REQUIRED_EXTENSIONS = []

# The list of optional extensions.
OPTIONAL_EXTENSIONS = []

# The action status.
ACTION_STATUS = {}
