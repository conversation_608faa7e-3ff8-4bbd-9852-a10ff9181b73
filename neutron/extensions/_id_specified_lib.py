#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api.definitions import network
from neutron_lib.api.definitions import port
from neutron_lib.api.definitions import qos
from neutron_lib.api.definitions import subnet
from neutron_lib import constants

from neutron.extensions import _qos_pps_rule_lib as apidef


ALIAS = 'id-specified'
IS_SHIM_EXTENSION = False
IS_STANDARD_ATTR_EXTENSION = False
NAME = 'Id Specified Extension'
API_PREFIX = ''
DESCRIPTION = 'Id Specified For Network, Subnet, Port, Qos Policy and Rule'
UPDATED_TIMESTAMP = '2024-12-09T10:00:00-00:00'


RESOURCE_ATTRIBUTE_MAP = {
    network.COLLECTION_NAME: {
        'id': {'allow_post': True, 'allow_put': False,
               'default': constants.ATTR_NOT_SPECIFIED,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_filter': True,
               'is_sort_key': True,
               'primary_key': True},
    },
    subnet.COLLECTION_NAME: {
        'id': {'allow_post': True, 'allow_put': False,
               'default': constants.ATTR_NOT_SPECIFIED,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_filter': True,
               'is_sort_key': True,
               'primary_key': True},
    },
    port.COLLECTION_NAME: {
        'id': {'allow_post': True, 'allow_put': False,
               'default': constants.ATTR_NOT_SPECIFIED,
               'validate': {'type:uuid': None},
               'is_filter': True,
               'is_sort_key': True,
               'is_visible': True,
               'primary_key': True},
    },
    qos.POLICIES: {
        'id': {'allow_post': True, 'allow_put': False,
               'default': constants.ATTR_NOT_SPECIFIED,
               'validate': {'type:uuid': None},
               'is_filter': True, 'is_sort_key': True,
               'is_visible': True, 'primary_key': True},
    },
}

qos._QOS_RULE_COMMON_FIELDS = {
    'id': {
        'allow_post': True, 'allow_put': False,
        'default': constants.ATTR_NOT_SPECIFIED,
        'validate': {'type:uuid': None},
        'is_visible': True,
        'is_filter': True,
        'is_sort_key': True,
        'primary_key': True},
}

SUB_RESOURCE_ATTRIBUTE_MAP = {
    qos.BANDWIDTH_LIMIT_RULES: {
        'parameters': dict(
            qos._QOS_RULE_COMMON_FIELDS)
    },
    apidef.PACKET_RATE_LIMIT_RULES: {
        'parameters': dict(
            qos._QOS_RULE_COMMON_FIELDS)
    }
}

ACTION_MAP = {}
REQUIRED_EXTENSIONS = []
OPTIONAL_EXTENSIONS = []
ACTION_STATUS = {}
