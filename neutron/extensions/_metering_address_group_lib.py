# Copyright (c) 2021 China Unicom Cloud Data Co.,Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

METERING_LABEL_RULES = 'metering_label_rules'

ALIAS = "metering-address-group"
IS_SHIM_EXTENSION = False
IS_STANDARD_ATTR_EXTENSION = False
NAME = 'Address group id field for metering label rules'
DESCRIPTION = 'Add new field of address group id in metering label rules'
UPDATED_TIMESTAMP = '2021-06-19T10:00:00-00:00'

RESOURCE_ATTRIBUTE_MAP = {
    METERING_LABEL_RULES: {
        'address_group_id': {
            'allow_post': True, 'allow_put': False,
            'validate': {'type:uuid_or_none': None},
            'default': None, 'is_visible': True,
            'is_sort_key': True, 'is_filter': True},
    }
}

SUB_RESOURCE_ATTRIBUTE_MAP = {
}

ACTION_MAP = {
}

ACTION_STATUS = {
}

REQUIRED_EXTENSIONS = [
    'metering'
]

OPTIONAL_EXTENSIONS = [
]
