#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron.common import constants as com_const
from neutron.extensions import securitygroup


ALIAS = 'security-group-rule-priority-and-action'
IS_SHIM_EXTENSION = False
IS_STANDARD_ATTR_EXTENSION = False
NAME = 'Security group rule priority and action Extension'
DESCRIPTION = 'Add security group rule priority and action'
UPDATED_TIMESTAMP = '2023-01-13T10:00:00-00:00'
RESOURCE_ATTRIBUTE_MAP = {
    securitygroup.SECURITYGROUPRULES: {
        'action': {
            'allow_post': True,
            'allow_put': True,
            'is_visible': True,
            'is_filter': True,
            'default': 'allow',
            'validate': {
                'type:values': com_const.SECURITY_RULE_ACTIONS,
            }
        },
        'priority': {
            'allow_post': True,
            'allow_put': True,
            'is_visible': True,
            'is_filter': True,
            'default': 1,
            'validate': {
                'type:range': com_const.ALLOW_SECURITY_PRIORITY_VALUES_RANGE,
            },
        },
    }
}

SUB_RESOURCE_ATTRIBUTE_MAP = {}
ACTION_MAP = {}
REQUIRED_EXTENSIONS = []
OPTIONAL_EXTENSIONS = []
ACTION_STATUS = {}
