#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib import constants

from neutron.extensions import securitygroup


ALIAS = 'securitygroup-and-rule-id-support-post'
IS_SHIM_EXTENSION = False
IS_STANDARD_ATTR_EXTENSION = False
NAME = ('Security Group and Security Group Rule '
        'Id Support POST Extension')
DESCRIPTION = ('Id Support POST For Security Group '
               'and Security Group Rule Extension')
UPDATED_TIMESTAMP = '2024-12-16T10:00:00-00:00'
RESOURCE_ATTRIBUTE_MAP = {
    securitygroup.SECURITYGROUPS: {
        'id': {'allow_post': True,
               'allow_put': False,
               'default': constants.ATTR_NOT_SPECIFIED,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_filter': True,
               'primary_key': True},
    },
    securitygroup.SECURITYGROUPRULES: {
        'id': {'allow_post': True,
               'allow_put': False,
               'default': constants.ATTR_NOT_SPECIFIED,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_filter': True,
               'primary_key': True},
    }
}
SUB_RESOURCE_ATTRIBUTE_MAP = {}
ACTION_MAP = {}
REQUIRED_EXTENSIONS = [securitygroup.Securitygroup.get_alias()]
OPTIONAL_EXTENSIONS = []
ACTION_STATUS = {}
