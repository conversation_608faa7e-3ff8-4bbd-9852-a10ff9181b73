#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc

from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib import exceptions
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
import six
import webob.exc

from neutron._i18n import _
from neutron.api import extensions
from neutron.api.v2 import resource as api_resource
from neutron.common import utils


CLOUD_ATTRIBUTE = 'cloud_attribute'
CLOUD_ATTRIBUTES = CLOUD_ATTRIBUTE + 's'
MAX_CLOUD_ATTRIBUTE_LEN = 4096
CLOUD_ATTRIBUTE_PLUGIN_TYPE = 'CLOUD_ATTRIBUTE'

CLOUD_ATTRIBUTE_SUPPORTED_RESOURCES = {
    'routers': 'router',
    'networks': 'network',
    'subnets': 'subnet',
    'agents': 'agent'
}
CLOUD_ATTRIBUTE_ATTRIBUTE_MAP = {
    CLOUD_ATTRIBUTES: {
        'allow_post': True,
        'allow_put': True,
        'is_visible': True,
        'is_filter': True,
        'default': {}}
}


def validate_cloud_attributes(cloud_attributes):
    if not isinstance(cloud_attributes, dict):
        msg = (_("The input cloud_attributes %s is not of type dict") %
               cloud_attributes)
        raise exceptions.InvalidInput(error_message=msg)
    ct_string = utils.filter_to_json_str(cloud_attributes)
    if len(ct_string) > MAX_CLOUD_ATTRIBUTE_LEN:
        msg = _("Too long cloud_attributes specified")
        raise exceptions.InvalidInput(error_message=msg)


class CloudAttributeResourceNotFound(exceptions.NotFound):
    message = _("Resource %(resource)s %(resource_id)s could not be found.")


class CloudAttributeNotFound(exceptions.NotFound):
    message = _("Tag %(tag)s could not be found.")


class CloudAttributeController(object):
    def __init__(self):
        self.plugin = directory.get_plugin(CLOUD_ATTRIBUTE_PLUGIN_TYPE)
        self.supported_resources = CLOUD_ATTRIBUTE_SUPPORTED_RESOURCES

    def _get_parent_resource_and_id(self, kwargs):
        for key in kwargs:
            for resource in self.supported_resources:
                if key == self.supported_resources[resource] + '_id':
                    return resource, kwargs[key]
        return None, None

    def index(self, request, **kwargs):
        # GET /v2.0/resources/{resource_id}/cloud_attributes
        parent, parent_id = self._get_parent_resource_and_id(kwargs)
        return self.plugin.get_cloud_attributes(request.context,
                                                parent, parent_id)

    def show(self, request, id, **kwargs):
        # GET /v2.0/resources/{resource_id}/cloud_attributes/{key}
        # id == key
        parent, parent_id = self._get_parent_resource_and_id(kwargs)
        return self.plugin.get_cloud_attribute(request.context,
                                               parent, parent_id, id)

    def create(self, request, **kwargs):
        # not supported
        # POST /v2.0/resources/{resource_id}/cloud_attributes
        raise webob.exc.HTTPNotFound("not supported")

    def update(self, request, id, **kwargs):
        # not supported
        # PUT /v2.0/resources/{resource_id}/cloud_attributes/{key}
        raise webob.exc.HTTPNotFound("not supported")

    def update_attrs(self, request, body, **kwargs):
        # PUT /v2.0/resources/{resource_id}/cloud_attributes
        # body: {"cloud_attributes": {"key1": "value1"}} or
        # body: {"cloud_attributes": {"key1": ["value1", "value2"]}}
        parent, parent_id = self._get_parent_resource_and_id(kwargs)
        result = self.plugin.extend_cloud_attributes(request.context, parent,
                                         parent_id, body)
        return result

    def delete(self, request, id, **kwargs):
        # DELETE /v2.0/resources/{resource_id}/cloud_attributes/{key}
        # id == key
        parent, parent_id = self._get_parent_resource_and_id(kwargs)
        self.plugin.remove_cloud_attribute(request.context,
                                           parent, parent_id, id)

    def delete_attrs(self, request, **kwargs):
        # DELETE /v2.0/resources/{resource_id}/cloud_attributes
        # body: {"cloud_attributes": ["key1", "key2"]]} or
        # body: {"cloud_attributes": {"key1": "value1"}} or
        # body: {"cloud_attributes": {"key1": ["value1", "value2"]}}
        parent, parent_id = self._get_parent_resource_and_id(kwargs)
        body = kwargs.get('body', None)
        if body is None:
            self.plugin.remove_cloud_attributes_all(request.context,
                                                    parent, parent_id)
        else:
            self.plugin.remove_cloud_attributes(request.context,
                                                parent, parent_id, body)


class Cloud_attribute(api_extensions.ExtensionDescriptor):
    """Extension class supporting cloud_attributes."""

    @classmethod
    def get_name(cls):
        return ("Cloud_attribute support for resources: %s"
                % ', '.join(CLOUD_ATTRIBUTE_SUPPORTED_RESOURCES.values()))

    @classmethod
    def get_alias(cls):
        return "cloud-attribute"

    @classmethod
    def get_description(cls):
        return "Enables to set cloud attribute on resources."

    @classmethod
    def get_updated(cls):
        return "2024-10-25T00:00:00-00:00"

    def get_required_extensions(self):
        return []

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        exts = []
        action_status = {'index': 200, 'show': 200, 'update_attrs': 200,
                         'delete': 204, 'delete_attrs': 204}
        controller = api_resource.Resource(CloudAttributeController(),
                                           faults.FAULT_MAP,
                                           action_status=action_status)
        collection_methods = {"update_attrs": "PUT",
                              "delete_attrs": "DELETE"}
        exts = []
        for collection, member in CLOUD_ATTRIBUTE_SUPPORTED_RESOURCES.items():
            parent = {'member_name': member,
                      'collection_name': collection}
            exts.append(extensions.ResourceExtension(
                CLOUD_ATTRIBUTES, controller, parent,
                collection_methods=collection_methods))
        return exts

    def get_extended_resources(self, version):
        if version != "2.0":
            return {}
        EXTENDED_ATTRIBUTES_2_0 = {}
        for name in CLOUD_ATTRIBUTE_SUPPORTED_RESOURCES:
            EXTENDED_ATTRIBUTES_2_0[name] = CLOUD_ATTRIBUTE_ATTRIBUTE_MAP
        return EXTENDED_ATTRIBUTES_2_0


@six.add_metaclass(abc.ABCMeta)
class CloudAttributePluginBase(service_base.ServicePluginBase):
    """REST API to operate the Tag."""

    def get_plugin_description(self):
        return "Tag support"

    @classmethod
    def get_plugin_type(cls):
        return CLOUD_ATTRIBUTE_PLUGIN_TYPE

    @abc.abstractmethod
    def get_cloud_attributes(self, context, resource, resource_id):
        pass

    @abc.abstractmethod
    def get_cloud_attribute(self, context, resource, resource_id, key):
        pass

    @abc.abstractmethod
    def extend_cloud_attributes(self, context, resource, resource_id, body):
        pass

    @abc.abstractmethod
    def remove_cloud_attribute(self, context, resource, resource_id, key):
        pass

    @abc.abstractmethod
    def remove_cloud_attributes(self, context, resource, resource_id):
        pass
