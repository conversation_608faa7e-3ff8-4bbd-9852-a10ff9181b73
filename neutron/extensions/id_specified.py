#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api import extensions as api_extensions

from neutron.extensions import _id_specified_lib as apidef


class Id_specified(api_extensions.APIExtensionDescriptor):
    """Id specified of Service Extension."""
    api_definition = apidef

    @classmethod
    def get_name(cls):
        return apidef.NAME

    @classmethod
    def get_alias(cls):
        return apidef.ALIAS

    @classmethod
    def get_description(cls):
        return apidef.DESCRIPTION

    @classmethod
    def get_updated(cls):
        return apidef.UPDATED_TIMESTAMP
