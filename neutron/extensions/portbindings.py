# Copyright (c) 2012 OpenStack Foundation.
# All rights reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api.definitions import portbindings
from neutron_lib.api import extensions

from neutron.common import constants as c_const


# Originally comes from:
# https://review.opendev.org/c/openstack/neutron-lib/+/624660
# Add new vnic_type: smart-nic
portbindings.VNIC_TYPES.append(c_const.VNIC_SMARTNIC)
VNIC_VHOST_VDPA = 'vdpa'
portbindings.VNIC_TYPES.append(VNIC_VHOST_VDPA)
VNIC_BAREMETAL_SUB = 'baremetal-sub'
portbindings.VNIC_TYPES.append(VNIC_BAREMETAL_SUB)
VNIC_SIMULATE = 'simulate'
portbindings.VNIC_TYPES.append(VNIC_SIMULATE)


class Portbindings(extensions.APIExtensionDescriptor):
    """Extension class supporting port bindings.

    This class is used by neutron's extension framework to make
    metadata about the port bindings available to external applications.

    With admin rights one will be able to update and read the values.
    """

    api_definition = portbindings
