# Copyright 2013 VMware, Inc.  All rights reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib.plugins import directory

from neutron.api import extensions
from neutron.api.v2 import resource
from neutron.extensions import _provisioning_blocks as apidef
from neutron import wsgi


class ProvisioningBlocksController(wsgi.Controller):
    def provisioning_blocks(self, request, port_id):
        plugin = directory.get_plugin()
        result = plugin.get_provisioning_blocks(request.context,
                                                port_id)
        return {'provisioning_blocks': result}


class Provisioning_blocks(
        api_extensions.APIExtensionDescriptor):
    """Extension class supporting provisioning_blocks."""

    api_definition = apidef

    @classmethod
    def get_resources(cls):
        controller = resource.Resource(ProvisioningBlocksController(),
                                       faults.FAULT_MAP)
        collection_methods = {"provisioning_blocks": "GET"}
        parent = dict(member_name="port",
                      collection_name="ports")
        ext = extensions.ResourceExtension(
            apidef.PROVISIONINGBLOCKS, controller, parent,
            collection_methods=collection_methods)
        return [ext]
