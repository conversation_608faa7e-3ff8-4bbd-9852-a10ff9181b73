# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.api import converters
from neutron_lib.api.definitions import l3
from neutron_lib.api import extensions

from neutron.conf.db import l3_dvr_db

l3_dvr_db.register_db_l3_dvr_opts()

TOTAL_LIMITS_ALIAS = 'router-total-limits'

EXTENDED_ATTRIBUTES_2_0 = {
    l3.ROUTERS: {
        'total_bandwidth': {
            'allow_post': True,
            'allow_put': True,
            'convert_to': converters.convert_to_int,
            'default': 0,
            'is_sort_key': True,
            'is_visible': True,
            'validate': {'type:range': [0, 10000000000]},
        },
        'total_packet_rate': {
            'allow_post': True,
            'allow_put': True,
            'convert_to': converters.convert_to_int,
            'default': 0,
            'is_sort_key': True,
            'is_visible': True,
            'validate': {'type:range': [0, 100000000]},
        },
        'total_connection': {
            'allow_post': True,
            'allow_put': True,
            'convert_to': converters.convert_to_int,
            'default': 0,
            'is_sort_key': True,
            'is_visible': True,
            'validate': {'type:range': [0, 100000000]},
        },
    }
}


class Router_total_limits(extensions.ExtensionDescriptor):
    """Extension class supporting total limits in all router."""

    @classmethod
    def get_name(cls):
        return "Total limitation of Router"

    @classmethod
    def get_alias(cls):
        return TOTAL_LIMITS_ALIAS

    @classmethod
    def get_description(cls):
        return "The total router limitation of Service extension"

    @classmethod
    def get_updated(cls):
        return "2021-11-09T00:00:00-00:00"

    def get_requrired_extensions(self):
        return ["router"]

    def get_extended_resources(self, version):
        if version == "2.0":
            return EXTENDED_ATTRIBUTES_2_0
        else:
            return {}
