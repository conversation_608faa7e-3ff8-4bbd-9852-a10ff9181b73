# Translations template for neutron.
# Copyright (C) 2015 ORGANIZATION
# This file is distributed under the same license as the neutron project.
#
# Translators:
# <AUTHOR> <EMAIL>, 2015
# <PERSON><PERSON>ift<PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2015. #zanata
# <PERSON> <<EMAIL>>, 2016. #zanata
msgid ""
msgstr ""
"Project-Id-Version: neutron VERSION\n"
"Report-Msgid-Bugs-To: https://bugs.launchpad.net/openstack-i18n/\n"
"POT-Creation-Date: 2018-11-30 03:49+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2016-04-12 05:55+0000\n"
"Last-Translator: Copied by <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: tr_TR\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.0\n"
"X-Generator: Zanata 4.3.3\n"
"Language-Team: Turkish (Turkey)\n"

#, python-format
msgid ""
"\n"
"Command: %(cmd)s\n"
"Exit code: %(code)s\n"
"Stdin: %(stdin)s\n"
"Stdout: %(stdout)s\n"
"Stderr: %(stderr)s"
msgstr ""
"\n"
"Komut: %(cmd)s\n"
"Çıkış kodu: %(code)s\n"
"Stdin: %(stdin)s\n"
"Stdout: %(stdout)s\n"
"Stderr: %(stderr)s"

#, python-format
msgid ""
"%(invalid_dirs)s is invalid value for sort_dirs, valid value is '%(asc)s' "
"and '%(desc)s'"
msgstr ""
"%(invalid_dirs)s sort_dirs için geçersiz değer, geçerli değer '%(asc)s' ve "
"'%(desc)s'"

#, python-format
msgid "%(key)s prohibited for %(tunnel)s provider network"
msgstr "%(key)s %(tunnel)s sağlayıcı ağı için yasaklanmış"

#, python-format
msgid "%(name)s '%(addr)s' does not match the ip_version '%(ip_version)s'"
msgstr "%(name)s '%(addr)s' ip_version '%(ip_version)s' ile eşleşmiyor"

#, python-format
msgid "%s cannot be called while in offline mode"
msgstr "%s çevrim dışı kipte çağrılamaz"

#, python-format
msgid "%s is invalid attribute for sort_keys"
msgstr "%s sort_keys için geçersiz öznitelik"

#, python-format
msgid "%s must implement get_port_from_device or get_ports_from_devices."
msgstr "%s get_port_from_device veya get_ports_from_devices uygulamalıdır."

#, python-format
msgid "%s prohibited for VLAN provider network"
msgstr "%s VLAN sağlayıcı ağı için yasaklanmış"

#, python-format
msgid "%s prohibited for flat provider network"
msgstr "%s düz sağlayıcı ağı için yasaklanmış"

#, python-format
msgid "%s prohibited for local provider network"
msgstr "%s yerel sağlayıcı ağı için yasaklanmış"

msgid "0 is not allowed as CIDR prefix length"
msgstr "0 CIDR önek uzunluğuna izin verilmez"

msgid "A cidr must be specified in the absence of a subnet pool"
msgstr "Alt ağ havuzu olmadığında bir cidr belirtilmelidir"

msgid "A metering driver must be specified"
msgstr "Bir ölçme sürücüsü belirtilmeli"

msgid "Access to this resource was denied."
msgstr "Bu kaynağa erişime izin verilmiyor."

msgid "Action to be executed when a child process dies"
msgstr "Alt süreç öldüğünde çalıştırılacak eylem"

msgid "Address not present on interface"
msgstr "Adres arayüzde mevcut değil"

msgid "Adds test attributes to core resources."
msgstr "Çekirdek kaynaklara test özniteliklerini ekler."

#, python-format
msgid "Agent %(id)s is not a L3 Agent or has been disabled"
msgstr "Ajan %(id)s bir L3 Ajanı değil ya da kapalı"

#, python-format
msgid "Agent updated: %(payload)s"
msgstr "Ajan güncellendi: %(payload)s"

msgid "Allow auto scheduling networks to DHCP agent."
msgstr "Ağların DHCP ajanlarına otomatik zamanlanmasına izin ver."

msgid "Allow auto scheduling of routers to L3 agent."
msgstr "Yönlendiricilerin L3 ajanına otomatik zamanlanmasına izin ver."

msgid "Allow running metadata proxy."
msgstr "Metadata vekili çalıştırmaya izin ver."

msgid "Allow sending resource operation notification to DHCP agent"
msgstr "DHCP ajanına kaynak işlem bildirimi göndermeye izin ver"

msgid "Allow the usage of the bulk API"
msgstr "Toplu API'nin kullanımına izin ver"

msgid "Allow to perform insecure SSL (https) requests to nova metadata"
msgstr "Nova metadata'ya güvensiz SSL (https) istekleri yapmaya izin ver"

msgid ""
"An ordered list of networking mechanism driver entrypoints to be loaded from "
"the neutron.ml2.mechanism_drivers namespace."
msgstr ""
"neutron.ml2.mechanism_drivers isim uzayından yüklenecek ağ mekanizması "
"sürücü giriş noktalarının sıralı listesi."

msgid "An unknown error has occurred. Please try your request again."
msgstr "Bilinmeyen bir hata oluştu. Lütfen tekrar deneyin."

msgid "Automatically remove networks from offline DHCP agents."
msgstr "Ağları çevrimdışı DHCP ajanlarından otomatik olarak çıkar."

msgid ""
"Automatically reschedule routers from offline L3 agents to online L3 agents."
msgstr ""
"Yönlendiricileri çevrimdışı L3 ajanlarından çevrimiçi L3 ajanlarına otomatik "
"olarak yeniden zamanla."

msgid "Available commands"
msgstr "Kullanılabilir komutlar"

#, python-format
msgid "Base MAC: %s"
msgstr "Taban MAC: %s"

msgid "Body contains invalid data"
msgstr "Gövde geçersiz veri içeriyor"

#, python-format
msgid "Bridge %(bridge)s does not exist."
msgstr "Köprü %(bridge)s mevcut değil."

msgid "Bulk operation not supported"
msgstr "Toplu işlem desteklenmiyor"

msgid "CIDR to monitor"
msgstr "İzlenecek CIDR"

#, python-format
msgid "Cannot allocate IPv%(req_ver)s subnet from IPv%(pool_ver)s subnet pool"
msgstr "IPv%(pool_ver)s alt ağ havuzundan IPv%(req_ver)s alt ağı ayrılamaz"

msgid "Cannot allocate requested subnet from the available set of prefixes"
msgstr "İstenen alt ağ kullanılabilir önek kümesinden ayrılamıyor"

msgid "Cannot disable enable_dhcp with ipv6 attributes set"
msgstr "ipv6 öznitelikleri ayarlıyken enable_dhcp kapatılamaz"

#, python-format
msgid "Cannot handle subnet of type %(subnet_type)s"
msgstr "%(subnet_type)s türünde alt ağ işlenemiyor"

msgid "Cannot have multiple IPv4 subnets on router port"
msgstr "Yönlendirici bağlantı noktasında birden fazla IPv4 alt ağı olamaz"

#, python-format
msgid ""
"Cannot have multiple router ports with the same network id if both contain "
"IPv6 subnets. Existing port %(p)s has IPv6 subnet(s) and network id %(nid)s"
msgstr ""
"İkisi de IPv6 alt ağı içeriyorsa aynı ağ id'si ile birden fazla yönlendirici "
"bağlantı noktası olamaz. Mevcut bağlantı noktası %(p)s IPv6 alt ağ(lar)ına "
"ve %(nid)s ağ kimliğine sahip"

msgid "Cannot specify both subnet-id and port-id"
msgstr "Hem subnet-id hem port-id belirtilemez"

msgid "Cannot understand JSON"
msgstr "JSON anlaşılamıyor"

#, python-format
msgid "Cannot update read-only attribute %s"
msgstr "Yalnızca okunabilir öznitelik %s güncellenemez"

msgid "Certificate Authority public key (CA cert) file for ssl"
msgstr "Ssl için Sertifika Yetkilisi açık anahtarı (CA cert)"

msgid "Check ebtables installation"
msgstr "Ebtables kurulumunu kontrol et"

msgid "Check for ARP header match support"
msgstr "ARP başlık eşleştirme desteğini kontrol et"

msgid "Check for ARP responder support"
msgstr "ARP yanıtlayıcısı desteğini kontrol et"

msgid "Check for OVS vxlan support"
msgstr "OVS vxlan desteğini kontrol et"

msgid "Check for VF management support"
msgstr "VF yönetim desteğini kontrol et"

msgid "Check for iproute2 vxlan support"
msgstr "Iproute2 vxlan desteğini kontrol et"

msgid "Check for nova notification support"
msgstr "Nova bildirim desteğini kontrol et"

msgid "Check for patch port support"
msgstr "Yama bağlantı noktası desteğini kontrol et"

msgid "Check minimal dnsmasq version"
msgstr "Asgari dnsmasq sürümünü kontrol et"

msgid "Check netns permission settings"
msgstr "Netns izin ayarlarını kontrol et"

msgid "Check ovsdb native interface support"
msgstr "Ovsdb doğal arayüz desteğini kontrol et"

#, python-format
msgid ""
"Cidr %(subnet_cidr)s of subnet %(subnet_id)s overlaps with cidr %(cidr)s of "
"subnet %(sub_id)s"
msgstr ""
"%(subnet_id)s alt ağının %(subnet_cidr)s cidr'i %(sub_id)s alt ağının "
"%(cidr)s cidr'i ile çakışıyor"

msgid "Client certificate for nova metadata api server."
msgstr "Nova metadata api sunucusu için istemci sertifikası."

msgid ""
"Comma-separated list of <tun_min>:<tun_max> tuples enumerating ranges of GRE "
"tunnel IDs that are available for tenant network allocation"
msgstr ""
"Kiracı ağ ayırma için kullanılabilir GRE tünel kimliklerinin aralığını "
"numaralandıran <tun_min>:<tun_max> demetlerinin virgülle ayrılmış listesi"

msgid ""
"Comma-separated list of <vni_min>:<vni_max> tuples enumerating ranges of "
"VXLAN VNI IDs that are available for tenant network allocation"
msgstr ""
"Kiracı ağı ayırmaları için kullanılabilir VXLAN VNI ID'lerinin aralıklarını "
"numaralandıran <vni_min>:<vni_max> demetlerinin virgülle ayrılmış listesi"

msgid ""
"Comma-separated list of the DNS servers which will be used as forwarders."
msgstr ""
"Yönlendirici olarak kullanılacak DNS sunucularının virgülle ayrılmış listesi."

msgid "Command to execute"
msgstr "Çalıştırılacak komut"

msgid "Config file for interface driver (You may also use l3_agent.ini)"
msgstr ""
"Arayüz sürücüsü için yapılandırma dosyası (l3_agent.ini de kullanabilirsiniz)"

#, python-format
msgid "Conflicting value ethertype %(ethertype)s for CIDR %(cidr)s"
msgstr "CIDR %(cidr)s için çatışan değer ethertype %(ethertype)s"

msgid ""
"Controls whether the neutron security group API is enabled in the server. It "
"should be false when using no security groups or using the nova security "
"group API."
msgstr ""
"Neutron güvenlik grubu API'sinin sunucuda etkin olup olmadığını kontrol "
"eder. Güvenlik grubu kullanılmadığında veeya nova güvenlik grubu API'si "
"kullanıldığında false olmalıdır."

#, python-format
msgid "Could not bind to %(host)s:%(port)s after trying for %(time)d seconds"
msgstr "%(time)d  saniye denedikten sonra %(host)s:%(port)s'a bağlanamadı"

msgid "Could not deserialize data"
msgstr "Veri serisi çözülemedi"

#, python-format
msgid ""
"Current gateway ip %(ip_address)s already in use by port %(port_id)s. Unable "
"to update."
msgstr ""
"Mevcut geçit ip'si %(ip_address)s %(port_id)s bağlantı noktası tarafından "
"zaten kullanılıyor. Güncellenemiyor."

msgid ""
"DHCP lease duration (in seconds). Use -1 to tell dnsmasq to use infinite "
"lease times."
msgstr ""
"DHCP kira süresi (saniye olarak). Dnsmasq'a süresiz kira zamanları "
"kullanmasını söylemek için -1 kullanın."

msgid ""
"Default network type for external networks when no provider attributes are "
"specified. By default it is None, which means that if provider attributes "
"are not specified while creating external networks then they will have the "
"same type as tenant networks. Allowed values for external_network_type "
"config option depend on the network type values configured in type_drivers "
"config option."
msgstr ""
"Sağlayıcı öznitelikleri belirtilmediğinde harici ağlar için varsayılan ağ "
"türü. Varsayılan olarak None'dir, bunun anlamı harici ağ oluştururken "
"sağlayıcı öznitelikleri belirtilmemişse kiracı ağlarla aynı türe sahip "
"olacaklarıdır. external_network_type yapılandırma seçeneği için izin verilen "
"değerler type_drivers yapılandırma seçeneğinde yapılandırılan ağ türü "
"değerlerine bağlıdır."

msgid ""
"Default number of resource allowed per tenant. A negative value means "
"unlimited."
msgstr ""
"Kiracı başına izin verilecek varsayılan kaynak sayısı. Negatif değer "
"sınırsız anlamına gelir."

msgid "Default security group"
msgstr "Varsayılan güvenlik grubu"

msgid "Default security group already exists."
msgstr "Varsayılan güvenlik grubu zaten mevcut."

msgid ""
"Define the default value of enable_snat if not provided in "
"external_gateway_info."
msgstr ""
"external_gateway_info'da sağlanmamışsa enable_snat'ın varsayılan değerini "
"tanımla."

msgid ""
"Defines providers for advanced services using the format: <service_type>:"
"<name>:<driver>[:default]"
msgstr ""
"Şu biçimi kullanarak gleişmiş servisler için sağlayıcılar tanımlar: "
"<service_type>:<name>:<driver>[:default]"

msgid "Delete the namespace by removing all devices."
msgstr "İsim uzayını tüm aygıtları kaldırarak sil."

#, python-format
msgid "Deleting port %s"
msgstr "Bağlantı noktası %s siliniyor"

msgid "Destroy IPsets even if there is an iptables reference."
msgstr "Iptables referansı olsa bile IPset'leri sil."

msgid "Destroy all IPsets."
msgstr "Tüm IPset'leri sil."

#, python-format
msgid "Device %(dev_name)s in mapping: %(mapping)s not unique"
msgstr "%(mapping)s eşleştirmesindeki aygıt %(dev_name)s benzersiz"

#, python-format
msgid "Device name %(dev_name)s is missing from physical_device_mappings"
msgstr "Aygıt ismi %(dev_name)s physical_device_mappings'de eksik"

msgid "Device not found"
msgstr "Aygıt bulunamadı"

msgid "Domain to use for building the hostnames"
msgstr "Makine adlarını inşa için kullanılacak alan"

msgid "Downgrade no longer supported"
msgstr "Alçaltma artık desteklenmiyor"

#, python-format
msgid "Driver %s is not unique across providers"
msgstr "%s sürücüsü sağlayıcılar arasında benzersiz değil"

msgid "Driver for security groups firewall in the L2 agent"
msgstr "L2 ajanındaki güvenlik grubunun güvenlik duvarı için sürücü"

msgid "Driver to use for scheduling network to DHCP agent"
msgstr "Ağın DHCP ajanlarına zamanlanması için kullanılacak sürücü"

msgid "Driver to use for scheduling router to a default L3 agent"
msgstr "Yönlendiriciyi bir L3 ajanına zamanlamak için gerekli sürücü"

msgid "Duplicate Security Group Rule in POST."
msgstr "POST'da Kopya Güvenlik Grubu Kuralı."

#, python-format
msgid "ERROR: %s"
msgstr "HATA: %s"

msgid ""
"ERROR: Unable to find configuration file via the default search paths (~/."
"neutron/, ~/, /etc/neutron/, /etc/) and the '--config-file' option!"
msgstr ""
"HATA: Varsayılan arama yollarıyla ve (~/.neutron/, ~/, /etc/neutron/, /etc/) "
"ve '--config-file' seçeneğiyle yapılandırma dosyası bulunamadı!"

msgid ""
"Either one of parameter network_id or router_id must be passed to _get_ports "
"method."
msgstr ""
"_get_ports metoduna network_id veya router_id parametrelerinden biri "
"verilmelidir."

msgid "Either subnet_id or port_id must be specified"
msgstr "subnet_id veya port_id belirtilmeli"

msgid "Enable HA mode for virtual routers."
msgstr "Sanal yönlendiriciler için HA kipini etkinleştir."

msgid "Enable SSL on the API server"
msgstr "API sunucuda SSL etkinleştir"

msgid ""
"Enable VXLAN on the agent. Can be enabled when agent is managed by ml2 "
"plugin using linuxbridge mechanism driver"
msgstr ""
"Ajanda VXLAN etkinleştir. Ajan linuxbridge mekanizma sürücüsünü kullanan ml2 "
"eklentisi ile yönetildiğinde etkinleştirilebilir"

msgid ""
"Enable local ARP responder if it is supported. Requires OVS 2.1 and ML2 "
"l2population driver. Allows the switch (when supporting an overlay) to "
"respond to an ARP request locally without performing a costly ARP broadcast "
"into the overlay."
msgstr ""
"Destekleniyorsa yerel ARP yanıtlayıcıyı etkinleştir. OVS 2.1 ve ML2 "
"I2population sürücüsüne ihtiyaç duyar. Anahtarın (kaplama desteklediğinde) "
"bir ARP isteğine yerel olarak, kaplamaya maliyetli ARP yayını yapmadan yanıt "
"vermesini sağlar."

msgid ""
"Enable services on an agent with admin_state_up False. If this option is "
"False, when admin_state_up of an agent is turned False, services on it will "
"be disabled. Agents with admin_state_up False are not selected for automatic "
"scheduling regardless of this option. But manual scheduling to such agents "
"is available if this option is True."
msgstr ""
"admin_state_up False olan bir ajan üzerinde servisleri etkinleştir. Bu "
"seçenek False ise, bir ajanın admin_state_up'u False yapıldığında, "
"üzerindeki servisler kapatılacaktır. admin_state_up False olan ajanlar bu "
"seçeneğe bakılmaksızın otomatik zamanlama için seçilmezler. Ama bu seçenek "
"True ise bu tür ajanlara elle zamanlama yapılabilir."

#, python-format
msgid "Error %(reason)s while attempting the operation."
msgstr "İşlem denenirken %(reason)s hatası."

#, python-format
msgid "Error parsing dns address %s"
msgstr "%s dns adresinin ayrıştırılmasında hata"

#, python-format
msgid "Error while reading %s"
msgstr "%s okunurken hata"

#, python-format
msgid ""
"Exceeded %s second limit waiting for address to leave the tentative state."
msgstr ""
"Adresin belirsiz durumdan çıkması için %s saniye bekleme sınırı aşıldı."

msgid "Existing prefixes must be a subset of the new prefixes"
msgstr "Mevcut önekler yeni öneklerin alt kümesi olmalıdır"

msgid ""
"Extension to use alongside ml2 plugin's l2population mechanism driver. It "
"enables the plugin to populate VXLAN forwarding table."
msgstr ""
"ml2 eklentisinin l2population mekanizma sürücüsünün yanında kullanılacak "
"eklenti. Eklentiyi VXLAN iletim tablosunu doldurması için etkinleştirir."

#, python-format
msgid "Extension with alias %s does not exist"
msgstr "%s rumuzlu eklenti mevcut değil"

#, python-format
msgid "External IP %s is the same as the gateway IP"
msgstr "Harici IP %s geçit IP ile aynı"

#, python-format
msgid "Failed rescheduling router %(router_id)s: no eligible l3 agent found."
msgstr ""
"%(router_id)s yönlendiricisini yeniden zamanlama başarısız: seçilebilir l3 "
"ajanı bulunamadı."

#, python-format
msgid "Failed scheduling router %(router_id)s to the L3 Agent %(agent_id)s."
msgstr ""
"%(router_id)s yönlendiricisinin %(agent_id)s L3 Ajanına zamanlanması "
"başarısız."

#, python-format
msgid ""
"Failed to create port on network %(network_id)s, because fixed_ips included "
"invalid subnet %(subnet_id)s"
msgstr ""
"%(network_id)s ağı üzerinde bağlantı noktası oluşturma başarısız, çünkü "
"fixed_ips geçersiz %(subnet_id)s alt ağını içeriyor"

msgid "Failed to remove supplemental groups"
msgstr "Destekleyici gruplar kaldırılamadı"

#, python-format
msgid "Failed to set gid %s"
msgstr "Gid %s ayarlanamadı"

#, python-format
msgid "Failed to set uid %s"
msgstr "Uid %s ayarlanamadı"

#, python-format
msgid "Failed to set-up %(type)s tunnel port to %(ip)s"
msgstr "%(ip)s'ye %(type)s tünel bağlantı noktası kurulumu başarısız"

msgid "Failure applying iptables rules"
msgstr "Iptables kuralları uygulanırken başarısız olundu"

#, python-format
msgid "Failure waiting for address %(address)s to become ready: %(reason)s"
msgstr "%(address)s adresinin hazır olmasını bekleme başarısız: %(reason)s"

msgid "For TCP/UDP protocols, port_range_min must be <= port_range_max"
msgstr ""
"TCP/UDP iletişim kuralları için, port_range_min <= port_range_max olmalı"

msgid "Force ip_lib calls to use the root helper"
msgstr "ip_lib çağrılarını kök yardımcıyı kullanmaya zorla"

#, python-format
msgid ""
"Found overlapping allocation pools: %(pool_1)s %(pool_2)s for subnet "
"%(subnet_cidr)s."
msgstr ""
"%(subnet_cidr)s alt ağı için çakışan ayırma havuzları: %(pool_1)s %(pool_2)s "
"bulundu."

msgid "Gateway is not valid on subnet"
msgstr "Geçit alt ağda geçerli değil"

msgid ""
"Group (gid or name) running metadata proxy after its initialization (if "
"empty: agent effective group)."
msgstr ""
"İlklendirilmesinden sonra metadata vekilini çalıştıran grup (gid veya isim) "
"(boşsa: ajan etkin grup)."

msgid "Group (gid or name) running this process after its initialization"
msgstr "İlklendirilmesinden sonra bu süreci çalıştıran grup (gid veya isim)"

#, python-format
msgid ""
"ICMP code (port-range-max) %(value)s is provided but ICMP type (port-range-"
"min) is missing."
msgstr ""
"ICMP kodu (port-range-max) %(value)s sağlanmış ama ICMP türü (port-range-"
"min) eksik."

msgid "ID of network"
msgstr "Ağ kimliği"

msgid "ID of network to probe"
msgstr "Sorgulanacak ağ ID'si"

msgid "ID of probe port to delete"
msgstr "Silinecek deneme bağlantı noktasının kimliği"

msgid "ID of probe port to execute command"
msgstr "Komutun çalıştırılacağı deneme bağlantı noktası kimliği"

msgid "ID of the router"
msgstr "Yönetici kimliği"

#, python-format
msgid "IP address %(ip)s already allocated in subnet %(subnet_id)s"
msgstr "%(ip)s IP adresi %(subnet_id)s alt ağında zaten ayrılmış"

#, python-format
msgid "IP address %(ip)s does not belong to subnet %(subnet_id)s"
msgstr "%(ip)s IP adresi %(subnet_id)s alt ağına ait değil"

msgid "IP allocation requires subnet_id or ip_address"
msgstr "IP ayırma subnet_id veya ip_address gerektirir"

#, python-format
msgid ""
"IPTablesManager.apply failed to apply the following set of iptables rules:\n"
"%s"
msgstr ""
"IPTablesManager.apply aşağıdakı iptables bilgileri uygulanamadı\n"
"%s"

#, python-format
msgid ""
"IPv6 address %(ip)s cannot be directly assigned to a port on subnet "
"%(subnet_id)s as the subnet is configured for automatic addresses"
msgstr ""
"Alt ağ otomatik adres olarak yapıladırıldığı için %(ip)s IPv6 adresi "
"doğrudan %(subnet_id)s alt ağındaki bir bağlantı noktasına atanamaz."

#, python-format
msgid ""
"IPv6 subnet %s configured to receive RAs from an external router cannot be "
"added to Neutron Router."
msgstr ""
"Harici bir yönlendiriciden RA almak için yapılandırılmış %s IPv6 alt ağı "
"Neutron Yönlendiriciye eklenemez."

msgid ""
"If True, then allow plugins that support it to create VLAN transparent "
"networks."
msgstr ""
"True ise, destekleyen eklentilerin VLAN şeffaf ağlar oluşturmasına izin ver."

msgid "Illegal IP version number"
msgstr "Kuraldışı IP sürüm numarası"

#, python-format
msgid "Insufficient prefix space to allocate subnet size /%s"
msgstr "/%s boyutunda alt ağ ayırmak için yetersiz önek alanı"

msgid "Insufficient rights for removing default security group."
msgstr "Varsayılan güvenlik grubunu silmek için yeterli izin yok."

msgid "Interface to monitor"
msgstr "İzlenecek arayüz"

msgid ""
"Interval between checks of child process liveness (seconds), use 0 to disable"
msgstr ""
"Alt süreç canlılığı kontrolleri aralığı (saniye), kapatmak için 0 kullanın"

msgid "Interval between two metering measures"
msgstr "İki ölçüm arasındaki aralık"

msgid "Interval between two metering reports"
msgstr "İki ölçme raporu arasındaki aralık"

#, python-format
msgid "Invalid Device %(dev_name)s: %(reason)s"
msgstr "Geçersiz Aygıt %(dev_name)s: %(reason)s"

#, python-format
msgid ""
"Invalid authentication type: %(auth_type)s, valid types are: "
"%(valid_auth_types)s"
msgstr ""
"Geçersiz kimlik doğrulama türü: %(auth_type)s, geçerli türler: "
"%(valid_auth_types)s"

#, python-format
msgid "Invalid format: %s"
msgstr "Geçersiz biçim: %s"

#, python-format
msgid "Invalid instance state: %(state)s, valid states are: %(valid_states)s"
msgstr "Geçersiz sunucu durumu: %(state)s, geçerli durumlar: %(valid_states)s"

#, python-format
msgid "Invalid mapping: '%s'"
msgstr "Geçersiz eşleştirme: '%s'"

#, python-format
msgid "Invalid pci slot %(pci_slot)s"
msgstr "Geçersiz pci yuvası %(pci_slot)s"

#, python-format
msgid "Invalid provider format. Last part should be 'default' or empty: %s"
msgstr "Geçersiz sağlayıcı biçimi. Son kısım 'default' ya da boş olmalı: %s"

#, python-format
msgid "Invalid route: %s"
msgstr "Geçersiz rota: %s"

msgid "Invalid service provider format"
msgstr "Geçersiz servis sağlayıcı biçimi"

#, python-format
msgid ""
"Invalid value for ICMP %(field)s (%(attr)s) %(value)s. It must be 0 to 255."
msgstr ""
"ICMP %(field)s (%(attr)s) %(value)s için geçersiz değer. 0 dan 255'e kadar "
"olmalı."

#, python-format
msgid "Invalid value for port %(port)s"
msgstr "%(port)s bağlantı noktası için geçersiz değer"

msgid "Keepalived didn't respawn"
msgstr "Keepalived yeniden başlamadı"

msgid "Limit number of leases to prevent a denial-of-service."
msgstr "Servis engellemeyi önlemek için kiralama sayısını sınırla."

msgid ""
"List of <physical_network>:<vlan_min>:<vlan_max> or <physical_network> "
"specifying physical_network names usable for VLAN provider and tenant "
"networks, as well as ranges of VLAN tags on each available for allocation to "
"tenant networks."
msgstr ""
"VLAN sağlayıcı ve kiracı ağlar için kullanılabilir physical_network "
"isimlerini belirten <physical_network>:<vlan_min>:<vlan_max> veya "
"<physical_network> listesi, aynı zamanda her birinde kiracı ağlara ayırma "
"için VLAN etiketleri aralıkları."

msgid ""
"List of network type driver entrypoints to be loaded from the neutron.ml2."
"type_drivers namespace."
msgstr ""
"neutron.ml2.type_drivers isim uzayından yüklenecek ağ türü sürücü giriş "
"noktaları listesi."

msgid "Location for Metadata Proxy UNIX domain socket."
msgstr "Metadata Vekil UNIX alan soketi için konum."

msgid "Location of Metadata Proxy UNIX domain socket"
msgstr "Metadata Vekil UNIX alan soketi konumu"

msgid "Location to store IPv6 RA config files"
msgstr "IPv6 RA yapılandırma dosyalarının kaydedileceği konum"

msgid "Location to store child pid files"
msgstr "Alt süreç dosyalarının kaydedileceği konum"

msgid "Location to store keepalived/conntrackd config files"
msgstr "Keepalived/conntrackd yapılandırma dosyalarının tutulacağı konum"

msgid "Log agent heartbeats"
msgstr "Ajan kalp atışlarını kaydet"

msgid "MTU size of veth interfaces"
msgstr "veth arayüzlerinin MTU boyutu"

msgid "Make the l2 agent run in DVR mode."
msgstr "L2 ajanın DVR kipinde çalışmasını sağla."

msgid "Malformed request body"
msgstr "Kusurlu istek gövdesi"

msgid "Maximum number of allowed address pairs"
msgstr "İzin verilen adres çiftlerinin azami sayısı"

msgid "Maximum number of host routes per subnet"
msgstr "Alt ağ başına azami istemci sayısı"

msgid "Metering driver"
msgstr "Ölçme sürücüsü"

msgid "Minimize polling by monitoring ovsdb for interface changes."
msgstr ""
"Sorgulamayı ovsdb arayüzünü değişiklikler için izleyerek olabildiğince azalt."

#, python-format
msgid "Missing key in mapping: '%s'"
msgstr "Eşleştirmede anahtar eksik: '%s'"

#, python-format
msgid "Multiple default providers for service %s"
msgstr "%s servisi için birden fazla varsayılan sağlayıcı"

#, python-format
msgid "Multiple plugins for service %s were configured"
msgstr "%s servisi için birden fazla eklenti yapılandırılmış"

#, python-format
msgid "Multiple providers specified for service %s"
msgstr "%s servisi için birden fazla sağlayıcı belirtilmiş"

msgid "Multiple tenant_ids in bulk security group rule create not allowed"
msgstr ""
"Toplu güvenlik grubu kuralı oluşturmada birden çok tenant_id'ye izin "
"verilmiyor"

msgid "Must specify one or more actions on flow addition or modification"
msgstr "Akış ekleme ya da değiştirmede bir ya da fazla eylem belirtilmeli"

msgid "Name of Open vSwitch bridge to use"
msgstr "Kullanılacak Open vSwitch köprüsünün ismi"

msgid ""
"Name of nova region to use. Useful if keystone manages more than one region."
msgstr ""
"Kullanılacak nova gölgesinin ismi. Keystone birden fazla bölgeyi yönetiyorsa "
"kullanışlıdır."

msgid "Namespace of the router"
msgstr "Yönetici isim uzayı"

msgid "Native pagination depend on native sorting"
msgstr "Doğal sayfalama doğal sıralamaya bağlıdır"

msgid "Negative delta (downgrade) not supported"
msgstr "Negatif fark (alt sürüm) desteklenmiyor"

msgid "Negative relative revision (downgrade) not supported"
msgstr "Negatif ilişkili sürüm (alt sürüm) desteklenmiyor"

#, python-format
msgid "Network %s is not a valid external network"
msgstr "%s ağı geçerli bir harici ağ değil"

#, python-format
msgid "Network %s is not an external network"
msgstr "Ağ %s harici bir ağ değil"

#, python-format
msgid ""
"Network of size %(size)s, from IP range %(parent_range)s excluding IP ranges "
"%(excluded_ranges)s was not found."
msgstr ""
"%(excluded_ranges)s IP aralıkları hariç %(parent_range)s IP aralığından "
"%(size)s botyutunda ağ bulunamadı."

#, python-format
msgid "Network type value '%s' not supported"
msgstr "Ağ türü değeri '%s' desteklenmiyor"

msgid "Network type value needed by the ML2 plugin"
msgstr "ML2 eklentisi ağ türü değerine ihtiyaç duyuyor"

msgid "Neutron core_plugin not configured!"
msgstr "Neutron core_plugin yapılandırılmamış!"

#, python-format
msgid "No eligible l3 agent associated with external network %s found"
msgstr "%s harici ağıyla ilişkilendirilmiş uygun l3 ajanı bulunamadı"

#, python-format
msgid "No more IP addresses available for subnet %(subnet_id)s."
msgstr "%(subnet_id)s alt ağı için kullanılabilir başka IP adresi yok."

msgid ""
"Number of DHCP agents scheduled to host a tenant network. If this number is "
"greater than 1, the scheduler automatically assigns multiple DHCP agents for "
"a given tenant network, providing high availability for DHCP service."
msgstr ""
"Bir kiracı ağı sunmak için zamanlanan DHCP ajanları sayısı. Bu sayı 1'den "
"büyükse, zamanlayıcı verilen bir kiracı ağa otomatik olarak birden çok DHCP "
"ajanı atar, ve DHCP servisi için yüksek kullanılabilirlik sağlar."

msgid "Number of backlog requests to configure the metadata server socket with"
msgstr "Metadata sunucu soketinin yapılandırılacağı birikmiş isteklerin sayısı"

msgid "Number of backlog requests to configure the socket with"
msgstr "Soketin birlikte yapılandırılacağı backlog isteklerinin sayısı"

msgid ""
"Number of floating IPs allowed per tenant. A negative value means unlimited."
msgstr ""
"Kiracı başına izin verilen değişken IP sayısı. Negatif değer sınırsız "
"demektir."

msgid ""
"Number of networks allowed per tenant. A negative value means unlimited."
msgstr ""
"Kiracı başına izin verilen ağ sayısı. Negatif değer sınırsız anlamına gelir."

msgid "Number of ports allowed per tenant. A negative value means unlimited."
msgstr ""
"Kiracı başına izin verilen bağlantı noktası sayısı. Negatif değer sınırsız "
"anlamına gelir."

msgid "Number of routers allowed per tenant. A negative value means unlimited."
msgstr ""
"Kiracı başına izin verilen yönlendirici sayısı. Negatif değer sınırsız "
"anlamına gelir."

msgid ""
"Number of seconds between sending events to nova if there are any events to "
"send."
msgstr ""
"Gönderilecek olay varsa olayların nova'ya gönderilmesi arasında beklenecek "
"saniye sayısı."

msgid "Number of seconds to keep retrying to listen"
msgstr "Dinlemeye devam etmek için saniye sayısı"

msgid ""
"Number of security groups allowed per tenant. A negative value means "
"unlimited."
msgstr ""
"Kiracı başına izin verilen güvenlik grubu sayısı. Negatif değer sınırsız "
"anlamına gelir."

msgid ""
"Number of security rules allowed per tenant. A negative value means "
"unlimited."
msgstr ""
"Kiracı başına izin verilen güvenlik kuralı sayısı. Negatif bir değer "
"sınırsız demektir."

msgid ""
"Number of separate API worker processes for service. If not specified, the "
"default is equal to the number of CPUs available for best performance."
msgstr ""
"Servis için ayrı API işçi süreçlerinin sayısı. Belirtilmezse, varsayılan "
"olarak en iyi performans için CPU sayısına eşit değerdir."

msgid ""
"Number of separate worker processes for metadata server (defaults to half of "
"the number of CPUs)"
msgstr ""
"Metadata sunucu için ayrı işçi süreçleri sayısı (CPU sayısının yarısı "
"varsayılır)"

msgid "Number of subnets allowed per tenant, A negative value means unlimited."
msgstr ""
"Kiracı başına izin verilen alt ağ sayısı, negatif değer sınırsız anlamına "
"gelir."

msgid "OK"
msgstr "Tamam"

msgid "Only admin can view or configure quota"
msgstr "Yalnızca yönetici kotaları görüntüleyebilir ya da yapılandırabilir"

msgid "Only admin is authorized to access quotas for another tenant"
msgstr "Yalnızca yönetici başka bir kiracı için kotalara erişebilir"

msgid "Only allowed to update rules for one security profile at a time"
msgstr ""
"Tek seferde bir güvenlik profili için kuralların güncellenmesine izin verilir"

msgid "Only remote_ip_prefix or remote_group_id may be provided."
msgstr "Yalnızca remote_ip_prefix veya remote_group_id sağlanabilir."

msgid "OpenFlow interface to use."
msgstr "Kullanılacak OpenFlow arayüzü."

#, python-format
msgid ""
"Operation %(op)s is not supported for device_owner %(device_owner)s on port "
"%(port_id)s."
msgstr ""
"İşlem %(op)s %(port_id)s bağlantı noktası üzerinde %(device_owner)s "
"device_owner için  desteklenmiyor."

msgid "Owner type of the device: network/compute"
msgstr "Aygıt sahip türü: ağ/hesap"

msgid "POST requests are not supported on this resource."
msgstr "POST istekleri bu kaynakta desteklenmiyor."

#, python-format
msgid "Parsing bridge_mappings failed: %s."
msgstr "bridge_mappins ayrıştırma başarısız: %s."

msgid "Path to PID file for this process"
msgstr "Bu sürecin PID dosyasının yolu"

msgid "Path to the router directory"
msgstr "Yönlendirici dizininin yolu"

msgid "Peer patch port in integration bridge for tunnel bridge."
msgstr "Tünel köprüsü için tümleştirme köprüsündeki eş yama bağlantı noktası."

msgid "Peer patch port in tunnel bridge for integration bridge."
msgstr "Tümleştirme köprüsü için tünel köprüsündeki eş yama bağlantı noktası."

msgid "Ping timeout"
msgstr "Ping zaman aşımı"

msgid "Plugin does not support updating provider attributes"
msgstr "Eklenti sağlayıcı özniteliklerini güncellemeyi desteklemiyor"

#, python-format
msgid "Port %(id)s does not have fixed ip %(address)s"
msgstr "%(id)s bağlantı noktası %(address)s sabit ip'sine sahip değil"

msgid "Private key of client certificate."
msgstr "İstemci sertifikasının özel anahtarı."

#, python-format
msgid "Probe %s deleted"
msgstr "Deneme %s silindi"

#, python-format
msgid "Probe created : %s "
msgstr "Deneme oluşturuldu: %s "

msgid "Process is already started"
msgstr "Süreç zaten başlamış"

msgid "Process is not running."
msgstr "Süreç çalışmıyor."

msgid "Protocol to access nova metadata, http or https"
msgstr "Nova metadata'ya erişmek için iletişim kuralı, http veya https"

msgid ""
"Range of seconds to randomly delay when starting the periodic task scheduler "
"to reduce stampeding. (Disable by setting to 0)"
msgstr ""
"Devresel görev zamanlayıcıyı başlatırken izdiham yaratmayı engellemek için "
"beklenecek rastgele saniye aralığı. (0 olarak ayarlayıp kapatabilirsiniz)"

msgid "Remote metadata server experienced an internal server error."
msgstr "Uzak metadata sunucu dahil sunucu hatası yaşadı."

msgid ""
"Representing the resource type whose load is being reported by the agent. "
"This can be \"networks\", \"subnets\" or \"ports\". When specified (Default "
"is networks), the server will extract particular load sent as part of its "
"agent configuration object from the agent report state, which is the number "
"of resources being consumed, at every report_interval.dhcp_load_type can be "
"used in combination with network_scheduler_driver = neutron.scheduler."
"dhcp_agent_scheduler.WeightScheduler When the network_scheduler_driver is "
"WeightScheduler, dhcp_load_type can be configured to represent the choice "
"for the resource being balanced. Example: dhcp_load_type=networks"
msgstr ""
"Ajan tarafından yükü rapor edilen kaynak türünü temsil eder. Bu \"ağlar\", "
"\"alt ağlar\", veya \"bağlantı noktaları\" olabilir. Belirtildiğinde "
"(varsayılanı ağlardır), sunucu ajan yapılandırma nesnesinin parçası olarak "
"gönderilen belirli yükü ajan rapor durumundan çıkartır, ki bu her "
"report_interval'da tüketilen kaynak sayısıdır. dhcp_load_type "
"network_scheduler_driver WeightScheduler olduğunda network_scheduler_driver "
"= neutron.scheduler.WeightScheduler ile birlikte kullanılabilir, "
"dhcp_load_type dengelenen kaynak için seçimi temsil edecek şekilde "
"yapılandırılabilir. Örneğin: dhcp_load_type=networks"

msgid "Request Failed: internal server error while processing your request."
msgstr "İstek Başarısız: isteğiniz işlenirken dahili sunucu hatası oluştu."

#, python-format
msgid ""
"Resource '%(resource_id)s' is already associated with provider "
"'%(provider)s' for service type '%(service_type)s'"
msgstr ""
"'%(resource_id)s' kaynağı '%(service_type)s' servis türü için zaten "
"'%(provider)s' sağlayıcısıyla ilişkilendirilmiş"

msgid "Resource body required"
msgstr "Kaynak gövdesi gerekiyor"

msgid "Resource not found."
msgstr "Kaynak bulunamadı."

msgid "Resources required"
msgstr "Kaynaklar gerekiyor"

msgid "Root permissions are required to drop privileges."
msgstr "İzinlerin düşürülmesi için Root izinleri gerekli."

#, python-format
msgid "Router already has a port on subnet %s"
msgstr "Yönlendirici zaten %s alt ağında bir bağlantı noktasına sahip"

msgid ""
"Seconds between nodes reporting state to server; should be less than "
"agent_down_time, best if it is half or less than agent_down_time."
msgstr ""
"Düğümlerin sunucuya durum raporu yapması arasında geçen saniye; "
"agent_down_time'dan az olmalı, en iyisi agent_down_time'ın yarısı ya da daha "
"azı olmasıdır."

msgid ""
"Seconds to regard the agent is down; should be at least twice "
"report_interval, to be sure the agent is down for good."
msgstr ""
"Ajanın çalışmıyor olduğuna karar vermek için geçmesi gereken saniye; ajanın "
"gerçekten kapalı olduğundan emin olmak için report_interval değerinin en az "
"iki katı olmalı."

#, python-format
msgid "Security Group %(id)s %(reason)s."
msgstr "Güvenlik Grubu %(id)s %(reason)s."

#, python-format
msgid "Security Group Rule %(id)s %(reason)s."
msgstr "Güvenlik Grubu Kuralı %(id)s %(reason)s."

#, python-format
msgid "Security group %(id)s does not exist"
msgstr "%(id)s güvenlik grubu mevcut değil"

#, python-format
msgid "Security group rule %(id)s does not exist"
msgstr "Güvenlik grubu kuralı %(id)s mevcut değil"

#, python-format
msgid ""
"Security group rule protocol %(protocol)s not supported. Only protocol "
"values %(values)s and integer representations [0 to 255] are supported."
msgstr ""
"Güvenlik grubu kuralı iletişim kuralı %(protocol)s desteklenmiyor. Yalnızca "
"iletişim kuralı değerleri %(values)s ve tam sayı temsilleri [0 dan 255 e]  "
"destekleniyor."

msgid ""
"Send notification to nova when port data (fixed_ips/floatingip) changes so "
"nova can update its cache."
msgstr ""
"Bağlantı noktası verisi (sabit_ipler/değişkenip) değiştiğinde nova'ya "
"bildirim gönder ki nova zulasını güncelleyebilsin."

msgid "Send notification to nova when port status changes"
msgstr "Bağlantı noktası durumu değiştiğinde nova'ya bildirim gönder"

#, python-format
msgid ""
"Service provider '%(provider)s' could not be found for service type "
"%(service_type)s"
msgstr ""
"%(service_type)s servis türü için '%(provider)s' servis sağlayıcı bulunamadı"

#, python-format
msgid "Service type %(service_type)s does not have a default service provider"
msgstr "%(service_type)s servis türü varsayılan servis sağlayıcıya sahip değil"

msgid ""
"Set new timeout in seconds for new rpc calls after agent receives SIGTERM. "
"If value is set to 0, rpc timeout won't be changed"
msgstr ""
"Ajan SIGTERM aldıktan sonra yeni rpc çağrıları için saniye olarak yeni zaman "
"aşımı ayarla. Değer 0 olarak ayarlanırsa, rpc zaman aşımı değiştirilmeyecek"

msgid ""
"Set or un-set the don't fragment (DF) bit on outgoing IP packet carrying GRE/"
"VXLAN tunnel."
msgstr ""
"Dışa giden IP paketi taşıyan GRE/VXLAN tünelinde bölümlenme yapma (DF) "
"bitini ayarla ya da ayarlama."

msgid "Shared address scope can't be unshared"
msgstr "Paylaşılan adres kapsamının paylaştırılması durdurulamaz"

msgid "String prefix used to match IPset names."
msgstr "IPset isimleriyle eşleştirme için kullanılan karakter dizisi önekleri."

msgid "Subnet for router interface must have a gateway IP"
msgstr "Yönlendirici arayüzü için alt ağ bir geçit IP'ye sahip olmalı"

msgid "Subnet pool has existing allocations"
msgstr "Alt ağ havuzunun mevcut ayırmaları var"

msgid "Subnet used for the l3 HA admin network."
msgstr "L3 HA yönetici ağı için kullanılan alt ağ."

msgid ""
"System-wide flag to determine the type of router that tenants can create. "
"Only admin can override."
msgstr ""
"Kiracıların oluşturabileceği yönlendirici türünü belirlemek için sistem "
"genelinde bayrak. Yalnızca yönetici üzerine yazabilir."

msgid "TCP Port used by Neutron metadata namespace proxy."
msgstr ""
"Neutron metadata isim uzayı vekili tarafından kullanılan TCP bağlantı "
"noktası."

msgid "TCP Port used by Nova metadata server."
msgstr "Nova metadata sunucusu tarafından kullanılan TCP Bağlantı noktası."

msgid "TTL for vxlan interface protocol packets."
msgstr "Vxlan arayüz iletişim kuralı paketleri için TTL."

#, python-format
msgid "Tenant %(tenant_id)s not allowed to create %(resource)s on this network"
msgstr ""
"Kiracı %(tenant_id)s'in bu ağda %(resource)s oluşturmasına izin verilmiyor"

msgid ""
"The 'gateway_external_network_id' option must be configured for this agent "
"as Neutron has more than one external network."
msgstr ""
"Neutron birden fazla harici ağa sahip olduğundan "
"'gateway_external_network_id' seçeneği bu ajan için yapılandırılmalıdır."

msgid "The UDP port to use for VXLAN tunnels."
msgstr "VXLAN tünelleri için kullanılacak UDP bağlantı noktası."

#, python-format
msgid ""
"The address allocation request could not be satisfied because: %(reason)s"
msgstr "Adres ayırma isteği sağlanamadı çünkü: %(reason)s"

msgid "The advertisement interval in seconds"
msgstr "Saniye cinsinden duyuru aralığı"

#, python-format
msgid "The allocation pool %(pool)s is not valid."
msgstr "Ayırma havuzu %(pool)s geçerli değil."

#, python-format
msgid ""
"The allocation pool %(pool)s spans beyond the subnet cidr %(subnet_cidr)s."
msgstr ""
"Ayırma havuzu %(pool)s %(subnet_cidr)s alt ağ cidr'inin ötesine uzanıyor."

msgid "The core plugin Neutron will use"
msgstr "Neutron'un kullanacağı çekirdek eklenti"

msgid "The driver used to manage the DHCP server."
msgstr "DHCP sunucusunu yönetmek için kullanılan sürücü."

msgid "The driver used to manage the virtual interface."
msgstr "Sanal arayüzü yönetmek için kullanılan sürücü."

#, python-format
msgid ""
"The following device_id %(device_id)s is not owned by your tenant or matches "
"another tenants router."
msgstr ""
"device_id %(device_id)s sizin kiracınıza ait değil veya başka bir kiracının "
"yönlendiricisiyle eşleşiyor."

msgid "The interface for interacting with the OVSDB"
msgstr "OVSDB ile etkileşim için arayüz"

msgid ""
"The maximum number of items returned in a single response, value was "
"'infinite' or negative integer means no limit"
msgstr ""
"Tek bir yanıtta döndürülecek azami öğe sayısı, 'infinite' değeri ya da "
"negatif tam sayı sınır yok demektir"

msgid ""
"The number of seconds the agent will wait between polling for local device "
"changes."
msgstr ""
"Ajanın yerel aygıt değişiklikleri için sorgulama yapma aralığında "
"bekleyeceği saniye sayısı."

msgid ""
"The number of seconds to wait before respawning the ovsdb monitor after "
"losing communication with it."
msgstr ""
"İletişim koptuktan sonra ovsdb izleyiciyi yeniden başlatmak için beklenecek "
"saniye sayısı."

msgid "The number of sort_keys and sort_dirs must be same"
msgstr "sort_keys ile sort_dirs sayıları aynı olmalı"

#, python-format
msgid "The port '%s' was deleted"
msgstr "Bağlantı noktası '%s' silinmiş"

msgid "The port to bind to"
msgstr "Bağlanılacak bağlantı noktası"

#, python-format
msgid "The requested content type %s is invalid."
msgstr "İstenen içerik türü %s geçersiz."

msgid "The resource could not be found."
msgstr "Kaynak bulunamadı."

#, python-format
msgid ""
"The router %(router_id)s has been already hosted by the L3 Agent "
"%(agent_id)s."
msgstr ""
"%(router_id)s yönlendiricisi zaten %(agent_id)s L3 Ajanı tarafından "
"sunuluyor."

msgid ""
"The server has either erred or is incapable of performing the requested "
"operation."
msgstr ""
"Sunucu ya hata verdi ya da istenen işlemi yapabilecek yeterlilikte değil."

msgid "The service plugins Neutron will use"
msgstr "Neutron'un kullanacağı servis eklentileri"

#, python-format
msgid "The subnet request could not be satisfied because: %(reason)s"
msgstr "Alt ağ isteği sağlanamadı çünkü: %(reason)s"

msgid "The type of authentication to use"
msgstr "Kullanılacak kimlik doğrulama türü"

msgid ""
"True to delete all ports on all the OpenvSwitch bridges. False to delete "
"ports created by Neutron on integration and external network bridges."
msgstr ""
"Tüm OpenvSwitch köprülerinde tüm bağlantı noktalarını silmek için True. "
"Neutron tarafından tümleştirme ve harici ağ köprüleri üzerinde oluşturulan "
"bağlantı noktalarını silmek için False."

msgid "Tunnel IP value needed by the ML2 plugin"
msgstr "Tünel IP değerine ML2 eklentisi tarafından ihtiyaç duyuluyor"

msgid "Tunnel bridge to use."
msgstr "Kullanılacak tünel köprüsü."

msgid "URL to database"
msgstr "Veri tabanı URL'si"

#, python-format
msgid "Unable to access %s"
msgstr "%s'e erişilemiyor"

#, python-format
msgid "Unable to calculate %(address_type)s address because of:%(reason)s"
msgstr "%(reason)s sebebiyle %(address_type)s adresi hesaplanamıyor"

#, python-format
msgid ""
"Unable to complete operation for %(subnet_id)s. The number of DNS "
"nameservers exceeds the limit %(quota)s."
msgstr ""
"%(subnet_id)s için işlem tamamlanamadı. DNS isim sunucuları sayısı %(quota)s "
"sayısını aşıyor."

#, python-format
msgid ""
"Unable to complete operation for %(subnet_id)s. The number of host routes "
"exceeds the limit %(quota)s."
msgstr ""
"%(subnet_id)s için işlem tamamlanamadı. İstemci rotaları sayısı %(quota)s "
"sınırını aşıyor."

#, python-format
msgid "Unable to convert value in %s"
msgstr "%s degeri dönüştürülemiyor"

msgid "Unable to create the Agent Gateway Port"
msgstr "Ajan Geçit Bağlantı Noktası oluşturulamıyor"

msgid "Unable to create the SNAT Interface Port"
msgstr "SNAT Arayüz Bağlantı Noktası oluşturulamıyor"

#, python-format
msgid ""
"Unable to create the flat network. Physical network %(physical_network)s is "
"in use."
msgstr "Düz ağ oluşturulamıyor. Fiziksel ağ %(physical_network)s kullanımda."

msgid ""
"Unable to create the network. No available network found in maximum allowed "
"attempts."
msgstr ""
"Ağ oluşturulamıyor. İzin verilen azami deneme içinde kullanılabilir ağ "
"bulunamadı."

#, python-format
msgid "Unable to determine mac address for %s"
msgstr "%s içim mac adresi tanımlanamadı"

#, python-format
msgid "Unable to find '%s' in request body"
msgstr "İstek gövdesinde '%s' bulunamadı"

#, python-format
msgid "Unable to find IP address %(ip_address)s on subnet %(subnet_id)s"
msgstr "%(subnet_id)s alt ağında %(ip_address)s IP adresi bulunamıyor"

#, python-format
msgid "Unable to find resource name in %s"
msgstr "%s içinde kaynak ismi bulunamadı"

#, python-format
msgid ""
"Unable to identify a target field from:%s. Match should be in the form "
"%%(<field_name>)s"
msgstr ""
"%s den bir hedef alan tanımlanamadı. Eşleşme %%(<field_name>)s biçiminde "
"olmalı"

#, python-format
msgid ""
"Unable to verify match:%(match)s as the parent resource: %(res)s was not "
"found"
msgstr "Eşleşme:%(match)s doğrulanamadı çünkü üst kaynak: %(res)s bulunamadı"

#, python-format
msgid "Unexpected response code: %s"
msgstr "Beklenmedik yanıt kodu: %s"

#, python-format
msgid "Unexpected response: %s"
msgstr "Beklenmeyen yanıt: %s"

#, python-format
msgid "Unknown address type %(address_type)s"
msgstr "Bilinmeyen adres türü %(address_type)s"

#, python-format
msgid "Unknown attribute '%s'."
msgstr "Bilinmeyen öznitelik '%s'."

#, python-format
msgid "Unknown chain: %r"
msgstr "Tanınmayan zincir: %r"

#, python-format
msgid "Unknown quota resources %(unknown)s."
msgstr "Bilinmeyen kota kaynakları %(unknown)s."

msgid "Unmapped error"
msgstr "Unmapped hata"

msgid "Unrecognized action"
msgstr "Tanınmayan eylem"

msgid "Unsupported Content-Type"
msgstr "Desteklenmeyen içerik türü"

#, python-format
msgid "Unsupported network type %(net_type)s."
msgstr "Desteklenmeyen ağ türü %(net_type)s."

msgid "Unsupported request type"
msgstr "Desteklenmeyen istek türü"

msgid "Updating default security group not allowed."
msgstr "Varsayılan güvenlik grubunu güncellemeye izin verilmiyor."

msgid ""
"Use ML2 l2population mechanism driver to learn remote MAC and IPs and "
"improve tunnel scalability."
msgstr ""
"Uzak MAC ve IP'leri öğrenmek ve tünel ölçeklenebilirliğini artırmak için ML2 "
"l2population mekanizması sürücüsünü kullan."

msgid "Use either --delta or relative revision, not both"
msgstr "Ya --delta ya ilişkili sürüm kullanın, ikisini birden değil"

msgid ""
"User (uid or name) running metadata proxy after its initialization (if "
"empty: agent effective user)."
msgstr ""
"İlklendirilmesinden sonra metadata vekilini çalıştıran kullanıcı (uid veya "
"isim) (boşsa: ajan etkin kullanıcı)."

msgid "User (uid or name) running this process after its initialization"
msgstr ""
"İlklendirilmesinden sonra bu süreci çalıştıran kullanıcı (uid veya isim)"

msgid "VRRP authentication password"
msgstr "VRRP kimlik doğrulama parolası"

msgid "VRRP authentication type"
msgstr "VRRP kimlik doğrulama türü"

msgid ""
"Where to store Neutron state files. This directory must be writable by the "
"agent."
msgstr ""
"Neutron durum dosyalarının nerede depolanacağı. Bu dizin ajan tarafından "
"yazılabilir olmalıdır."

msgid ""
"With IPv6, the network used for the external gateway does not need to have "
"an associated subnet, since the automatically assigned link-local address "
"(LLA) can be used. However, an IPv6 gateway address is needed for use as the "
"next-hop for the default route. If no IPv6 gateway address is configured "
"here, (and only then) the neutron router will be configured to get its "
"default route from router advertisements (RAs) from the upstream router; in "
"which case the upstream router must also be configured to send these RAs. "
"The ipv6_gateway, when configured, should be the LLA of the interface on the "
"upstream router. If a next-hop using a global unique address (GUA) is "
"desired, it needs to be done via a subnet allocated to the network and not "
"through this parameter. "
msgstr ""
"IPv6 ile, harici geçit için kullanılan ağ ilişkili bir alt ağa sahip olmak "
"zorunda değildir, çünkü otomatik olarak atanan bağlantı-yerel adres (LLA) "
"kullanılabilir. Ancak bir IPv6 geçit adresine varsayılan rota için sonraki-"
"nokta olarak kullanılmak üzere ihtiyaç vardır. Burada bir IPv6 geçit adresi "
"yapılandırılmazsa, (ve yalnızca bu durumda) neutron yönlendirici, varsayılan "
"rotasını üst seviye yönlendirici duyurularından (RA) alacak şekilde "
"yapılandırılır; ki bu durumda üst seviye yönlendirici bu RA'ları gönderecek "
"şekilde yapılandırılmalıdır. ipv6_gateway, yapılandırıldığında, üst seviye "
"yönlendirici üzerindeki arayüzün LLA'sı olmalıdır. Eğer genel benzersiz "
"adres (GUA) kullanan bir sonraki-nokta isteniyorsa, bu ağa ayrılmış bir alt "
"ağ vasıtasıyla yapılmalıdır, bu parametre ile değil. "

msgid "You must implement __call__"
msgstr "__call__ fonksiyonunu uygulamalısınız."

msgid ""
"You must provide a config file for bridge - either --config-file or "
"env[NEUTRON_TEST_CONFIG_FILE]"
msgstr ""
"Köprü için bir yapılandırma dosyası sağlamalısınız - ya --config-file ya da "
"env[NEUTRON_TEST_CONFIG_FILE]"

msgid "You must provide a revision or relative delta"
msgstr "Bir sürüm ya da ilişkili fark sağlamalısınız"

msgid "allocation_pools allowed only for specific subnet requests."
msgstr "allocation_pools yalnızca belirli alt ağ istekleri için izinli."

msgid "binding:profile value too large"
msgstr "bağ:profil değeri çok büyük"

msgid "cidr and prefixlen must not be supplied together"
msgstr "cidr ve prefixlen birlikte verilmemelidir"

#, python-format
msgid "dhcp_agents_per_network must be >= 1. '%s' is invalid."
msgstr "dhcp_agents_per_network >= 1 olmalı. '%s' geçersiz."

msgid "fixed_ip_address cannot be specified without a port_id"
msgstr "fixed_ip_addres port_id olmadan belirtilemez"

#, python-format
msgid "has device owner %s"
msgstr "%s aygıt sahibine sahip"

msgid "in use"
msgstr "kullanımda"

#, python-format
msgid "ip command failed on device %(dev_name)s: %(reason)s"
msgstr "ip komutu %(dev_name)s aygıtı üzerinde başarısız: %(reason)s"

#, python-format
msgid "ip link capability %(capability)s is not supported"
msgstr "ip bağlantı yeteneği %(capability)s desteklenmiyor"

#, python-format
msgid "ip link command is not supported: %(reason)s"
msgstr "ip bağlantı komutu desteklenmiyor: %(reason)s"

msgid "ip_version must be specified in the absence of cidr and subnetpool_id"
msgstr "subnetpool_id ve cidr olmadığında ip_version belirtilmelidir"

msgid "ipv6_address_mode is not valid when ip_version is 4"
msgstr "ip_version 4 olduğunda ipv6_address_mode geçerli değildir"

msgid "ipv6_ra_mode is not valid when ip_version is 4"
msgstr "ip_version 4 olduğunda ipv6_ra_mode geçerli değildir"

#, python-format
msgid ""
"ipv6_ra_mode set to '%(ra_mode)s' with ipv6_address_mode set to "
"'%(addr_mode)s' is not valid. If both attributes are set, they must be the "
"same value"
msgstr ""
"ipv6_ra_mode kipi '%(ra_mode)s' olarak ipv6_address_mode '%(addr_mode)s' "
"olarak ayarlanması geçersizdir. İki öznitelik de ayarlanıyorsa, aynı değerde "
"olmalılar"

msgid "mac address update"
msgstr "mac adres güncellemesi"

msgid "network_type required"
msgstr "network_type gerekli"

#, python-format
msgid "network_type value '%s' not supported"
msgstr "network_type değeri '%s' desteklenmiyor"

msgid "new subnet"
msgstr "yeni alt ağ"

#, python-format
msgid "physical_network '%s' unknown for flat provider network"
msgstr "physical_network '%s' düz sağlayıcı ağı için bilinmiyor"

msgid "physical_network required for flat provider network"
msgstr "Düz sağlayıcı ağı için physical_network gerekir"

#, python-format
msgid "provider:physical_network specified for %s network"
msgstr "sağlayıcı:physical_network %s ağı için belirtildi"

msgid "respawn_interval must be >= 0 if provided."
msgstr "eğer sağlanmışsa respawn_interval >= 0 olmalı."

#, python-format
msgid "segmentation_id out of range (%(min)s through %(max)s)"
msgstr "segmentation_id aralık dışında (%(min)s %(max)s arasında)"

msgid "segmentation_id requires physical_network for VLAN provider network"
msgstr ""
"segmentation_id VLAN sağlayıcı ağı için physical_network'e ihtiyaç duyar"

msgid "the nexthop is not connected with router"
msgstr "Sonraki nokta yönlendiriciyle bağlı değil"

msgid "the nexthop is used by router"
msgstr "sonraki nokta yönlendirici tarafından kullanılıyor"
