# Copyright (c) 2016 Intel Corporation.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from oslo_versionedobjects import fields as obj_fields

from neutron.db.models import dns as models
from neutron.db.models import l3
from neutron.objects import base
from neutron.objects import common_types


@base.NeutronObjectRegistry.register
class FloatingIPDNS(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = models.FloatingIPDNS

    primary_keys = ['floatingip_id']
    foreign_keys = {'FloatingIP': {'floatingip_id': 'id'}}

    fields = {
        'floatingip_id': common_types.UUIDField(),
        'dns_name': common_types.DomainNameField(),
        'dns_domain': common_types.DomainNameField(),
        'published_dns_name': common_types.DomainNameField(),
        'published_dns_domain': common_types.DomainNameField(),
    }


@base.NeutronObjectRegistry.register
class FipADPortsLists(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = l3.FipADPortsLists

    fields = {
        'floatingip_id': common_types.UUIDField(),
        'allowed_port_numbers': obj_fields.ListOfStringsField(
            nullable=True, default=[]),
        'denied_port_numbers': obj_fields.ListOfStringsField(
            nullable=True, default=[]),
    }

    fields_no_update = ['floatingip_id']

    foreign_keys = {'FloatingIP': {'floatingip_id': 'id'}}

    primary_keys = ['floatingip_id']

    @classmethod
    def modify_fields_from_db(cls, db_obj):
        result = super(
            FipADPortsLists, cls).modify_fields_from_db(db_obj)
        if 'allowed_port_numbers' in result:
            result['allowed_port_numbers'] = (
                cls.load_json_from_str(
                    result['allowed_port_numbers'], default=[]))
        if 'denied_port_numbers' in result:
            result['denied_port_numbers'] = (
                cls.load_json_from_str(
                    result['denied_port_numbers'], default=[]))
        return result

    @classmethod
    def modify_fields_to_db(cls, fields):
        result = super(
            FipADPortsLists, cls).modify_fields_to_db(fields)
        if 'allowed_port_numbers' in result:
            result['allowed_port_numbers'] = (
                cls.convert_list_to_str(result['allowed_port_numbers']))
        if 'denied_port_numbers' in result:
            result['denied_port_numbers'] = (
                cls.convert_list_to_str(result['denied_port_numbers']))
        return result
