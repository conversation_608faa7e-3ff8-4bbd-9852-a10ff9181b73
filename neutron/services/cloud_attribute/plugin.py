#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

from neutron_lib import exceptions
from neutron_lib.plugins import directory
from oslo_log import helpers as log_helpers
from oslo_log import log as logging
from sqlalchemy.orm import exc

from neutron._i18n import _
from neutron.common import utils
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.db import common_db_mixin
from neutron.extensions import cloud_attribute
from neutron.extensions.cloud_attribute import validate_cloud_attributes
from neutron.objects import agent as agent_obj
from neutron.objects import network as net_obj
from neutron.objects import router as l3_obj
from neutron.objects import subnet as subnet_obj


LOG = logging.getLogger(__name__)


# Resources supported cloud_attributes
resource_object_map = {
    'routers': l3_obj.RouterExtraAttributes,
    'networks': net_obj.NetworkCloudAttributes,
    'subnets': subnet_obj.SubnetCloudAttributes,
    'agents': agent_obj.AgentCloudAttributes
}

resource_primary_key_map = {
    'routers': 'router_id',
    'networks': 'network_id',
    'subnets': 'subnet_id',
    'agents': 'agent_id'
}

list_to_extend_response = ['networks', 'subnets']

MAX_CLOUD_ATTRIBUTE_LEN = 4096


@resource_extend.has_resource_extenders
class CloudAttributePlugin(common_db_mixin.CommonDbMixin,
                           cloud_attribute.CloudAttributePluginBase):
    """Implementation of the Neutron Cloud_attribute Service Plugin."""

    supported_extension_aliases = ['cloud-attribute']

    __filter_validation_support = True

    @staticmethod
    @resource_extend.extends(list_to_extend_response)
    def _extend_cloud_attributes_to_response(response_data, db_data):
        if not directory.get_plugin(cloud_attribute.
                                    CLOUD_ATTRIBUTE_PLUGIN_TYPE):
            return
        if db_data.get('cloud_attributes'):
            cloud_attributes = db_data[
                'cloud_attributes'].get('cloud_attributes', '{}')
            response_data['cloud_attributes'] = (
                utils.load_json_from_str(cloud_attributes))

    def _get_resource_obj(self, context, resource, resource_id):
        obj = resource_object_map[resource]
        key = resource_primary_key_map[resource]
        try:
            return obj.get_object(context, **{key: resource_id})
        except exc.NoResultFound:
            raise cloud_attribute.CloudAttributeResourceNotFound(
                resource=resource, resource_id=resource_id)

    @log_helpers.log_method_call
    def get_cloud_attributes(self, context, resource, resource_id):
        obj = self._get_resource_obj(context, resource, resource_id)
        cloud_attributes = obj.cloud_attributes
        result = {'cloud_attributes': cloud_attributes}
        return result

    @log_helpers.log_method_call
    def get_cloud_attribute(self, context, resource, resource_id, key):
        obj = self._get_resource_obj(context, resource, resource_id)
        cloud_attributes = obj.cloud_attributes
        if key not in cloud_attributes.keys():
            msg = (_("The existing cloud_attributes does not contain "
                     "key: %s") % key)
            raise exceptions.InvalidInput(error_message=msg)
        return {key: cloud_attributes.get(key)}

    @log_helpers.log_method_call
    @db_api.retry_if_session_inactive()
    def extend_cloud_attributes(self, context, resource, resource_id, body):
        ext_cloud_attributes = body.get('cloud_attributes')
        if ext_cloud_attributes is None:
            msg = (_("The input body dose not contain cloud_attributes"))
            raise exceptions.InvalidInput(error_message=msg)
        if not isinstance(ext_cloud_attributes, dict):
            msg = (_("The input cloud_attributes %s is not of type dict") %
                   ext_cloud_attributes)
            raise exceptions.InvalidInput(error_message=msg)
        obj = self._get_resource_obj(context, resource, resource_id)
        cloud_attributes = obj.cloud_attributes
        for key, value in ext_cloud_attributes.items():
            if key not in cloud_attributes.keys():
                cloud_attributes[key] = value
                continue
            if not isinstance(cloud_attributes[key], list):
                msg = (_("The existing cloud_attributes[%s] is not of "
                         "type list") % key)
                raise exceptions.InvalidInput(error_message=msg)
            if isinstance(value, list):
                for item in value:
                    if item not in cloud_attributes[key]:
                        cloud_attributes[key].append(item)
            else:
                if value not in cloud_attributes[key]:
                    cloud_attributes[key].append(value)
        validate_cloud_attributes(cloud_attributes)
        obj.cloud_attributes = cloud_attributes
        obj.update()
        return {'cloud_attributes': cloud_attributes}

    @log_helpers.log_method_call
    @db_api.retry_if_session_inactive()
    def remove_cloud_attribute(self, context, resource, resource_id, key):
        obj = self._get_resource_obj(context, resource, resource_id)
        cloud_attributes = obj.cloud_attributes
        try:
            del cloud_attributes[key]
        except KeyError:
            msg = (_("The existing cloud_attributes does not contain "
                     "key: %s") % key)
            raise exceptions.InvalidInput(error_message=msg)
        obj.cloud_attributes = cloud_attributes
        obj.update()
        LOG.info("Finished removing cloud_attributes[%(key)s] of %(resource)s "
                 "%(resource_id)s", {'key': key, 'resource': resource,
                                     'resource_id': resource_id})

    @log_helpers.log_method_call
    @db_api.retry_if_session_inactive()
    def remove_cloud_attributes_all(self, context, resource, resource_id):
        obj = self._get_resource_obj(context, resource, resource_id)
        obj.cloud_attributes = {}
        obj.update()
        LOG.info("Finished removing all cloud attributes of %(resource)s "
                 "%(resource_id)s", {'resource': resource,
                                     'resource_id': resource_id})

    @log_helpers.log_method_call
    @db_api.retry_if_session_inactive()
    def remove_cloud_attributes(self, context, resource, resource_id, body):
        cloud_attributes = body.get('cloud_attributes')
        if cloud_attributes is None:
            msg = (_("The input body dose not contain cloud_attributes"))
            raise exceptions.InvalidInput(error_message=msg)
        if isinstance(cloud_attributes, list):
            self._remove_cloud_attrs_by_keys(context, resource, resource_id,
                                             keys=cloud_attributes)
        elif isinstance(cloud_attributes, dict):
            self._remove_cloud_attrs_by_dicts(context, resource, resource_id,
                                              dicts=cloud_attributes)
        else:
            msg = (_("The input cloud_attributes %s is neither a list type "
                     "nor a dict type") % cloud_attributes)
            raise exceptions.InvalidInput(error_message=msg)
        LOG.info("Finished removing cloud_attributes of %(resource)s "
                 "%(resource_id)s with input: %(input)s", {
                     'resource': resource, 'resource_id': resource_id,
                     'input': cloud_attributes})

    def _remove_cloud_attrs_by_keys(self, context, resource, resource_id,
                                    keys):
        obj = self._get_resource_obj(context, resource, resource_id)
        cloud_attributes = obj.cloud_attributes
        try:
            for key in keys:
                del cloud_attributes[key]
        except KeyError:
            msg = (_("The existing cloud_attributes does not contain "
                     "key: %s") % key)
            raise exceptions.InvalidInput(error_message=msg)
        obj.cloud_attributes = cloud_attributes
        obj.update()

    def _delete_from_list(self, cloud_attributes, key, item):
        if item not in cloud_attributes[key]:
            msg = (_("The existing cloud_attributes[%(key)s] does not contain "
                     "value: %(item)s (Note that it may be due to different "
                     "data types)") % {'key': key, 'item': item})
            raise exceptions.InvalidInput(error_message=msg)
        orig_list = cloud_attributes[key]
        orig_list[:] = [x for x in orig_list if x != item]
        if orig_list == []:
            del cloud_attributes[key]

    def _remove_cloud_attrs_by_dicts(self, context, resource, resource_id,
                                     dicts):
        obj = self._get_resource_obj(context, resource, resource_id)
        cloud_attributes = obj.cloud_attributes
        for key, value in dicts.items():
            if key not in cloud_attributes.keys():
                msg = (_("The existing cloud_attributes does not contain "
                        "key: %s") % key)
                raise exceptions.InvalidInput(error_message=msg)
            if not isinstance(cloud_attributes.get(key), list):
                if cloud_attributes[key] == value:
                    del cloud_attributes[key]
                    continue
                else:
                    msg = (_("The existing cloud_attributes[%(key)s] is not "
                             "equal to: %(value)s (Note that it may be due to "
                             "different data types)") % {'key': key,
                                                         'value': value})
                    raise exceptions.InvalidInput(error_message=msg)
            if isinstance(value, list):
                for item in value:
                    self._delete_from_list(cloud_attributes, key, item)
            else:
                self._delete_from_list(cloud_attributes, key, value)
        obj.cloud_attributes = cloud_attributes
        obj.update()
