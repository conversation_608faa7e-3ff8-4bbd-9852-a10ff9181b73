#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron._i18n import _
from neutron_lib import exceptions as n_exc


class ElasticSnatNotFound(n_exc.NotFound):
    message = _("Elastic Snat %(id)s could not be found.")


class ElasticSnatNotSupportFilterField(n_exc.BadRequest):
    message = _("Elastic Snat filter %(filter)s is not supported.")


class SetRouterGatewayEnableSnatTrueNotAllowed(n_exc.BadRequest):
    message = _("Set router %(router_id)s gateway enable_snat to True "
                "is not allowed while enable Elastic Snat plugin.")


class FipInUseByElasticSnat(n_exc.InUse):
    message = _("Floating IP %(id)s in use by Elastic Snat resources.")


class RouterGatewayInUseByElasticSnat(n_exc.InUse):
    message = _("Router %(id)s gateway in use by Elastic Snat resources.")


class RouterInterfaceInUseByElasticSnat(n_exc.InUse):
    message = _("Router %(id)s interface in use by Elastic Snat resources.")


class CannotChangeEnableSnatState(n_exc.BadRequest):
    message = _("Router %(id)s enabel_snat can not be set to True "
                "while there are Elastic Snat resources.")


class FailedToCreateOrUpdateElasticSnat(n_exc.BadRequest):
    message = _("Failed to create or update Elastic Snat, error: %(error)s.")


class FailedToCreateElasticSnatCIDROverlaps(n_exc.BadRequest):
    message = _("Failed to create or update Elastic Snat because there are "
                "CIDR under this router overlaps with each other.")


class DuplicatedFloatingIPForElasticSnat(n_exc.BadRequest):
    message = _("Failed to create Elastic Snat, duplicated floating IP "
                "%(floatingip_id)s, use UPDATE for existing record.")
