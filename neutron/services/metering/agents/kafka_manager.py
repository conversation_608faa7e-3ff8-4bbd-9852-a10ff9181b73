#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import ConfigParser

from oslo_config import cfg
from oslo_log import log as logging
from pykafka import exceptions as kafka_exc
from pykafka import KafkaClient

LOG = logging.getLogger(__name__)

MESSAGE_NUM_FOR_PRINT = 200

mconf = ConfigParser.ConfigParser()
mconf.read("/etc/neutron/metering_agent.ini")
kafka_host = '0.0.0.0:9092'
try:
    kafka_host = mconf.get("DEFAULT", "kafka_host")
except Exception as e:
    LOG.warning('Need reload metering_agent.ini')


class KafkaManagerBase(object):
    def __init__(self):
        self.kafka_dict = {'initSucc': False, 'client': None}
        self.is_config_ready = self._is_full_config_loaded()

    def _is_full_config_loaded(self):
        if not kafka_host:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return False
        return True

    def reconnect_kafa(self):
        LOG.info("Need to implement reconnect kafka function")


class EipKafkaManager(KafkaManagerBase):
    def __init__(self):
        super(EipKafkaManager, self).__init__()
        self.initKafa = True
        self.eip_counter = 0

    def reconnect_kafa(self):
        LOG.info('Rconnecting Eip kafa server:%s ...', cfg.CONF.get(
            'kafka_host'))
        # check necessary config
        if not self.is_config_ready:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return

        try:
            client = KafkaClient(hosts=cfg.CONF.get('kafka_host'))
            self.kafka_dict['client'] = client

            # gernate producer
            for t in ['monitor_topic_eip']:
                tpc = self.kafka_dict['client'].topics[t]
                p = tpc.get_sync_producer()
                if p:
                    self.kafka_dict['producer_%s' % t] = p
                    LOG.debug('init Eip topic: %s successfully.', t)
                else:
                    LOG.error('init Eip producer failed, topic=%s', t)
                    return
            self.kafka_dict['initSucc'] = True

        except kafka_exc.NoBrokersAvailableError:
            LOG.warning('Connect remote kafaka server failed for NoBroker.')
            self.kafka_dict['initSucc'] = False
        except Exception as e:
            LOG.warning('Connect failed for reason:%s', e)
            self.kafka_dict['initSucc'] = False


class RouterKafkaManager(KafkaManagerBase):
    def __init__(self):
        super(RouterKafkaManager, self).__init__()
        self.initKafa = True
        self.router_counter = 0

    def reconnect_kafa(self):
        LOG.info('Rconnecting Router kafa server:%s ...', cfg.CONF.get(
            'kafka_host'))
        # check necessary config
        if not self.is_config_ready:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return

        try:
            client = KafkaClient(hosts=cfg.CONF.get('kafka_host'))
            self.kafka_dict['client'] = client

            # gernate producer
            for t in ['monitor_topic_router']:
                tpc = self.kafka_dict['client'].topics[t]
                p = tpc.get_sync_producer()
                if p:
                    self.kafka_dict['producer_%s' % t] = p
                    LOG.debug('init Router topic: %s successfully.', t)
                else:
                    LOG.error('init Router producer failed, topic=%s', t)
                    return
            self.kafka_dict['initSucc'] = True

        except kafka_exc.NoBrokersAvailableError:
            LOG.warning('Connect remote kafaka server failed for NoBroker.')
            self.kafka_dict['initSucc'] = False
        except Exception as e:
            LOG.warning('Connect failed for reason:%s', e)
            self.kafka_dict['initSucc'] = False


class VmKafkaManager(KafkaManagerBase):
    def __init__(self):
        super(VmKafkaManager, self).__init__()
        self.initKafa = True
        self.vm_health_counter = 0

    def reconnect_kafa(self):
        LOG.info('Rconnecting Vm kafa server:%s ...', cfg.CONF.get(
            'kafka_host'))
        # check necessary config
        if not self.is_config_ready:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return

        try:
            client = KafkaClient(hosts=cfg.CONF.get('kafka_host'))
            self.kafka_dict['client'] = client

            # gernate producer
            for t in ['monitor_topic_vm_healthchk']:
                tpc = self.kafka_dict['client'].topics[t]
                p = tpc.get_sync_producer()
                if p:
                    self.kafka_dict['producer_%s' % t] = p
                    LOG.debug('init Vm topic: %s successfully.', t)
                else:
                    LOG.error('init Vm producer failed, topic=%s', t)
                    return
            self.kafka_dict['initSucc'] = True

        except kafka_exc.NoBrokersAvailableError:
            LOG.warning('Connect remote kafaka server failed for NoBroker.')
            self.kafka_dict['initSucc'] = False
        except Exception as e:
            LOG.warning('Connect failed for reason:%s', e)
            self.kafka_dict['initSucc'] = False


class Vpc2VpcKafkaManager(KafkaManagerBase):
    def __init__(self):
        super(Vpc2VpcKafkaManager, self).__init__()
        self.initKafa = True
        self.vpc2vpc_dhcp_counter = 0
        self.vpc2vpc_rns_counter = 0

    def reconnect_kafa(self):
        LOG.info('Rconnecting Vpc kafa server:%s ...', cfg.CONF.get(
            'kafka_host'))
        # check necessary config
        if not self.is_config_ready:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return

        try:
            client = KafkaClient(hosts=cfg.CONF.get('kafka_host'))
            self.kafka_dict['client'] = client

            # gernate producer
            for t in ['monitor_topic_vpc2vpc']:
                tpc = self.kafka_dict['client'].topics[t]
                p = tpc.get_sync_producer()
                if p:
                    self.kafka_dict['producer_%s' % t] = p
                    LOG.debug('init  topic: %s successfully.', t)
                else:
                    LOG.error('init Vpc2vpc producer failed, topic=%s', t)
                    return
            self.kafka_dict['initSucc'] = True

        except kafka_exc.NoBrokersAvailableError:
            LOG.warning('Connect remote kafaka server failed for NoBroker.')
            self.kafka_dict['initSucc'] = False
        except Exception as e:
            LOG.warning('Connect failed for reason:%s', e)
            self.kafka_dict['initSucc'] = False


class KafkaManagerExt(object):
    def __init__(self):
        self.kafka_dict = {'initSucc': False, 'client': None}
        self.is_config_ready = self._is_full_config_loaded()
        self.router_conn_detail_counter = 0
        self.vpc_counter = 0
        self.vpc_qg_counter = 0

    @staticmethod
    def _is_full_config_loaded():
        if not kafka_host:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return False
        return True

    def handle_loss_connection(self, procedure_key, message):
        # NOTE(wl): only handle connection lost exception
        producer = (self.kafka_dict['client'].topics[
                        procedure_key].get_sync_producer())
        producer.stop()
        producer.start()
        producer.produce(message)

    def reconnect_kafa(self):
        # NOTE(wl): completely init kafka client and producer
        LOG.info('KafkaManagerExt reconnect kafka server:%s ...', kafka_host)
        # check necessary config
        if not self.is_config_ready:
            LOG.error('Can not get kafka host or topic configuration'
                      'Complete them then restart metering agent.')
            return

        try:
            client = KafkaClient(hosts=kafka_host)
            self.kafka_dict['client'] = client

            # gernate producer
            for t in ['monitor_topic_router_con_detail', 'monitor_topic_vpc']:
                tpc = self.kafka_dict['client'].topics[t]
                p = tpc.get_sync_producer()
                if p:
                    self.kafka_dict['producer_%s' % t] = p
                    LOG.debug('init KafkaManagerExt topic: %s successfully.',
                              t)
                else:
                    LOG.error('init KafkaManagerExt producer failed,topic=%s',
                              t)
                    return

            self.kafka_dict['initSucc'] = True

        except kafka_exc.NoBrokersAvailableError:
            LOG.warning('Connect kafaka server failed for NoBroker.')
            self.kafka_dict['initSucc'] = False
        except Exception as e:
            LOG.warning('Connect failed for reason:%s', e)
            self.kafka_dict['initSucc'] = False
