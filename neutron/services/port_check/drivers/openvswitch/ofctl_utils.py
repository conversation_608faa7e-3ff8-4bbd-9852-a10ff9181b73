# Copyright (c) 2021 China Unicom Cloud Data Co.,Ltd.
# Copyright (c) 2019 - 2020 China Telecom Corporation
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import base64

import netaddr

from oslo_log import log as logging
from ryu.lib import ofctl_utils
from ryu.ofproto import nicira_ext
from ryu.ofproto import ofproto_common
from ryu.ofproto import ofproto_v1_3
from ryu.ofproto import ofproto_v1_3_parser

UTIL = ofctl_utils.OFCtlUtil(ofproto_v1_3)
LOG = logging.getLogger(__name__)


def nicira_ext_action_to_str(act, ofctl_action_to_str):
    sub_type = act.subtype
    if (sub_type == nicira_ext.NXAST_RESUBMIT or
            sub_type == nicira_ext.NXAST_RESUBMIT_TABLE):
        return 'resubmit(%s,%s)' % (act.in_port, act.table_id)
    elif sub_type == nicira_ext.NXAST_REG_MOVE:
        src_start = act.src_ofs
        dst_start = act.dst_ofs
        src_end = src_start + act.n_bits
        dst_end = dst_start + act.n_bits
        return ('move:%s[%s..%s]->%s[%s..%s]' %
                (act.src_field, src_start, src_end,
                 act.dst_field, dst_start, dst_end))
    elif sub_type == nicira_ext.NXAST_REG_LOAD:
        start = act.ofs
        end = start + act.nbits
        return 'load:%x->%s[%s..%s]' % (act.value, act.dst, start, end)
    elif sub_type == nicira_ext.NXAST_LEARN:
        specs = []
        add_spec = specs.append
        for spec in act.specs:
            dst_type = spec._dst_type
            if dst_type == 0:  # match
                if isinstance(spec.src, (tuple, list)):
                    src = spec.src[0]
                    src_start = spec.src[1]
                    src_end = src_start + spec.n_bits
                    src_start_end = '[%s..%s]' % (src_start, src_end)
                else:
                    src = spec.src
                    src_start_end = '[]'
                if isinstance(spec.dst, (tuple, list)):
                    dst = spec.dst[0]
                    dst_start = spec.dst[1]
                    dst_end = dst_start + spec.n_bits
                    dst_start_end = '[%s..%s]' % (dst_start, dst_end)
                else:
                    dst = spec.dst
                    dst_start_end = '[]'
                if isinstance(src, str):
                    src = '%s%s' % (src, src_start_end)
                else:
                    src = '%s' % src
                if isinstance(dst, str):
                    dst = '%s%s' % (dst, dst_start_end)
                else:
                    dst = '%s' % dst
                add_spec('%s=%s' % (dst, src))
            elif dst_type == 1:  # load
                if isinstance(spec.src, (tuple, list)):
                    src = spec.src[0]
                    start = spec.src[1]
                    end = start + spec.n_bits
                    src_start_end = '[%s..%s]' % (start, end)
                else:
                    src = spec.src
                    src_start_end = '[]'
                if isinstance(spec.dst, (tuple, list)):
                    dst = spec.dst[0]
                    start = spec.dst[1]
                    end = start + spec.n_bits
                    dst_start_end = '[%s..%s]' % (start, end)
                else:
                    dst = spec.dst
                    dst_start_end = '[]'
                add_spec('load:%s%s->%s%s' %
                         (src, src_start_end, dst, dst_start_end))
            elif dst_type == 2:  # output
                if isinstance(spec.src, (tuple, list)):
                    src = spec.src[0]
                    start = spec.src[1]
                    end = start + spec.n_bits
                    start_end = '[%s..%s]' % (start, end)
                else:
                    src = spec.src
                    start_end = '[]'
                add_spec('output:%s%s' % (src, start_end))
        return ('learn(table_id=%s, priority=%s, %s)' %
                (act.table_id, act.priority, ','.join(specs)))
    elif sub_type == nicira_ext.NXAST_CONJUNCTION:
        return ('conjunction(%s, %s/%s)' %
                (act.id, act.clause + 1, act.n_clauses))
    elif sub_type == nicira_ext.NXAST_CT:
        if act.zone_ofs_nbits != 0:
            start = act.zone_ofs_nbits
            end = start + 16
            zone = act.zone_src + ('[%s..%s]' % (start, end))
        else:
            zone = act.zone_src
        actions = [ofctl_action_to_str(action) for action in act.actions]
        return ('ct(flags=%s, zone=%s, table=%s, alg=%s, actions=%s}' %
                (act.flags, zone, act.recirc_table, act.alg, actions))
    data_str = base64.b64encode(act.data)
    return 'NX_UNKNOWN: {subtype: %s, data: %s}' % (sub_type,
                                                    data_str.decode('utf-8'))


def action_to_str(act):
    action_type = act.cls_action_type
    if action_type == ofproto_v1_3.OFPAT_OUTPUT:
        port = UTIL.ofp_port_to_user(act.port)
        buf = 'output:' + str(port)
    elif action_type == ofproto_v1_3.OFPAT_PUSH_VLAN:
        buf = 'push_vlan:' + str(act.ethertype)
    elif action_type == ofproto_v1_3.OFPAT_POP_VLAN:
        buf = 'pop_vlan'
    elif action_type == ofproto_v1_3.OFPAT_GROUP:
        group_id = UTIL.ofp_group_to_user(act.group_id)
        buf = 'group:' + str(group_id)
    elif action_type == ofproto_v1_3.OFPAT_SET_NW_TTL:
        buf = 'set_nw_ttl:' + str(act.nw_ttl)
    elif action_type == ofproto_v1_3.OFPAT_DEC_NW_TTL:
        buf = 'dec_nw_ttl'
    elif action_type == ofproto_v1_3.OFPAT_SET_FIELD:
        buf = 'set_field:%s->%s' % (act.value, act.key)
    elif action_type == ofproto_v1_3.OFPAT_EXPERIMENTER:
        if act.experimenter == ofproto_common.NX_EXPERIMENTER_ID:
            try:
                return nicira_ext_action_to_str(act, action_to_str)
            except Exception:
                LOG.debug('Error parsing NX_ACTION(%s)',
                          act.__class__.__name__, exc_info=True)
        data_str = base64.b64encode(act.data)
        buf = 'EXPERIMENTER: {experimenter:%s, data:%s}' % \
            (act.experimenter, data_str.decode('utf-8'))
    else:
        buf = 'UNKNOWN'
    return buf


def actions_to_str(instructions):
    actions = []

    for instruction in instructions:
        if isinstance(instruction,
                      ofproto_v1_3_parser.OFPInstructionActions):
            if instruction.type == ofproto_v1_3.OFPIT_APPLY_ACTIONS:
                for a in instruction.actions:
                    actions.append(action_to_str(a))
            elif instruction.type == ofproto_v1_3.OFPIT_CLEAR_ACTIONS:
                actions.append('CLEAR_ACTIONS')
            else:
                actions.append('UNKNOWN')
        elif isinstance(instruction,
                        ofproto_v1_3_parser.OFPInstructionGotoTable):
            table_id = UTIL.ofp_table_to_user(instruction.table_id)
            buf = 'goto_table:' + str(table_id)
            actions.append(buf)
        elif isinstance(instruction,
                        ofproto_v1_3_parser.OFPInstructionMeter):
            meter_id = UTIL.ofp_meter_to_user(instruction.meter_id)
            buf = 'METER:' + str(meter_id)
            actions.append(buf)
        else:
            continue
    return actions


def str_to_int(value):
    if isinstance(value, str):
        value = int(value, 16)
    return value


def to_match_vid(value):
    if isinstance(value, str):
        value = str_to_int(value)
    return int(value) | 0x1000


def to_match_masked_int(value):
    if isinstance(value, str) and '/' in value:
        value = value.split('/')
        return str_to_int(value[0]), str_to_int(value[1])
    return str_to_int(value)


def to_match_eth(value):
    if '/' in value:
        value = value.split('/')
        return value[0], value[1]
    return value


def to_match_ip(value):
    if '/' in value:
        (ip_addr, ip_mask) = value.split('/')
        if ip_mask.isdigit():
            ip = netaddr.ip.IPNetwork(value)
            ip_addr = str(ip.ip)
            ip_mask = str(ip.netmask)
        return ip_addr, ip_mask
    return value
