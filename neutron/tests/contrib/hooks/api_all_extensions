# Keep entries alphabetically
# NOTE: The first entry should not use '+=' and a comma.
NETWORK_API_EXTENSIONS="address-scope"
NETWORK_API_EXTENSIONS+=",agent"
NETWORK_API_EXTENSIONS+=",allowed-address-pairs"
NETWORK_API_EXTENSIONS+=",auto-allocated-topology"
NETWORK_API_EXTENSIONS+=",availability_zone"
NETWORK_API_EXTENSIONS+=",availability_zone_filter"
NETWORK_API_EXTENSIONS+=",binding"
NETWORK_API_EXTENSIONS+=",binding-extended"
NETWORK_API_EXTENSIONS+=",default-subnetpools"
NETWORK_API_EXTENSIONS+=",dhcp_agent_scheduler"
NETWORK_API_EXTENSIONS+=",dns-integration"
NETWORK_API_EXTENSIONS+=",dvr"
NETWORK_API_EXTENSIONS+=",empty-string-filtering"
NETWORK_API_EXTENSIONS+=",ext-gw-mode"
NETWORK_API_EXTENSIONS+=",external-net"
NETWORK_API_EXTENSIONS+=",extra_dhcp_opt"
NETWORK_API_EXTENSIONS+=",extraroute"
NETWORK_API_EXTENSIONS+=",filter-validation"
NETWORK_API_EXTENSIONS+=",fip-port-details"
NETWORK_API_EXTENSIONS+=",flavors"
NETWORK_API_EXTENSIONS+=",ip-substring-filtering"
NETWORK_API_EXTENSIONS+=",l3-flavors"
NETWORK_API_EXTENSIONS+=",l3-ha"
NETWORK_API_EXTENSIONS+=",l3_agent_scheduler"
NETWORK_API_EXTENSIONS+=",logging"
NETWORK_API_EXTENSIONS+=",metering"
NETWORK_API_EXTENSIONS+=",multi-provider"
NETWORK_API_EXTENSIONS+=",net-mtu"
NETWORK_API_EXTENSIONS+=",net-mtu-writable"
NETWORK_API_EXTENSIONS+=",network-ip-availability"
NETWORK_API_EXTENSIONS+=",network_availability_zone"
NETWORK_API_EXTENSIONS+=",network-segment-range"
NETWORK_API_EXTENSIONS+=",pagination"
NETWORK_API_EXTENSIONS+=",port-security"
NETWORK_API_EXTENSIONS+=",project-id"
NETWORK_API_EXTENSIONS+=",provider"
NETWORK_API_EXTENSIONS+=",qos"
NETWORK_API_EXTENSIONS+=",qos-fip"
NETWORK_API_EXTENSIONS+=",quotas"
NETWORK_API_EXTENSIONS+=",quota_details"
NETWORK_API_EXTENSIONS+=",rbac-policies"
NETWORK_API_EXTENSIONS+=",router"
NETWORK_API_EXTENSIONS+=",router_availability_zone"
NETWORK_API_EXTENSIONS+=",security-group"
NETWORK_API_EXTENSIONS+=",port-mac-address-regenerate"
NETWORK_API_EXTENSIONS+=",port-security-groups-filtering"
NETWORK_API_EXTENSIONS+=",segment"
NETWORK_API_EXTENSIONS+=",segments-peer-subnet-host-routes"
NETWORK_API_EXTENSIONS+=",service-type"
NETWORK_API_EXTENSIONS+=",sorting"
NETWORK_API_EXTENSIONS+=",standard-attr-description"
NETWORK_API_EXTENSIONS+=",standard-attr-revisions"
NETWORK_API_EXTENSIONS+=",standard-attr-segment"
NETWORK_API_EXTENSIONS+=",standard-attr-timestamp"
NETWORK_API_EXTENSIONS+=",standard-attr-tag"
NETWORK_API_EXTENSIONS+=",subnet_allocation"
NETWORK_API_EXTENSIONS+=",trunk"
NETWORK_API_EXTENSIONS+=",trunk-details"
