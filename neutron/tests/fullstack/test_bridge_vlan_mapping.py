# Copyright (c) 2021 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
import re

from neutron_lib import constants
from oslo_utils import uuidutils

from neutron.common import utils as common_utils
from neutron.tests.common.exclusive_resources import ip_network
from neutron.tests.common import machine_fixtures
from neutron.tests.fullstack import base
from neutron.tests.fullstack.resources import environment
from neutron.tests.fullstack.resources import machine

TENNAT_PHYSICAL_NETWORK_NAME = "physnet1"
EXTNET_PHYSICAL_NETWORK_NAME = "physnet2"
EXTNET_SEGMENT_ID = 150
PFNNET_SEGMENT_ID = 200

NP_EGRESS_NAT = 80
INGRESS_TABLE = 82


class OvsBridgeVlanMappingTestBase(base.BaseFullStackTestCase):
    def _create_external_network_and_subnet(self, tenant_id, seg_id, phy_net):
        network = self.safe_client.create_network(
            tenant_id, name='public', external=True,
            network_type='vlan', segmentation_id=seg_id,
            physical_network=phy_net)
        cidr = self.useFixture(
            ip_network.ExclusiveIPNetwork(
                "240.0.0.0", "***************", "24")).network
        subnet = self.safe_client.create_subnet(tenant_id, network['id'], cidr)
        return network, subnet

    def _create_and_attach_subnet(
            self, tenant_id, subnet_cidr, network_id, router_id):
        subnet = self.safe_client.create_subnet(
            tenant_id, network_id, subnet_cidr)

        router_interface_info = self.safe_client.add_router_interface(
            router_id, subnet['id'])
        self.block_until_port_status_active(
            router_interface_info['port_id'])

    def block_until_port_status_active(self, port_id):
        def is_port_status_active():
            port = self.client.show_port(port_id)
            return port['port']['status'] == 'ACTIVE'
        common_utils.wait_until_true(lambda: is_port_status_active(), sleep=1)

    def _create_external_vm(self, network, subnet):
        vm = self.useFixture(
            machine_fixtures.FakeMachine(
                self.environment.central_bridge,
                common_utils.ip_to_cidr(subnet['gateway_ip'], 24)))
        vm.bridge.set_db_attribute(
            "Port", vm.port.name,
            "tag", network.get("provider:segmentation_id"))
        return vm

    def _create_net_subnet_and_vm(self, tenant_id, subnet_cidrs, host, router):
        network = self.safe_client.create_network(
            tenant_id, network_type='vlan')
        for cidr in subnet_cidrs:
            self._create_and_attach_subnet(
                tenant_id, cidr, network['id'], router['id'])

        return self._boot_fake_vm_in_network(host, tenant_id, network['id'])

    def _boot_fake_vm_in_network(self, host, tenant_id, network_id, wait=True):
        sg = self.safe_client.create_security_group(tenant_id)
        port = self.safe_client.create_port(
            tenant_id, network_id,
            host.hostname,
            device_owner='compute:test',
            security_groups=[sg['id']])
        self.safe_client.create_security_group_rule(
            tenant_id, sg['id'],
            direction=constants.INGRESS_DIRECTION,
            ethertype=constants.IPv4,
            protocol=constants.PROTO_NAME_ICMP)
        vm = self.useFixture(
            machine.FakeFullstackMachine(
                host, network_id, tenant_id, self.safe_client,
                neutron_port=port))
        if wait:
            vm.block_until_boot()
        return vm


class OvsBridgeVlanMappingMergeDiffPhysicalNetworkTest(
        OvsBridgeVlanMappingTestBase):
    use_dhcp = False
    number_of_hosts = 2

    def setUp(self):
        # telnet network is physnet1
        # external network is physnet2
        # use same physical bridge on compute node
        # fullstack ml2 config: network_vlan_ranges = physnet1:1000:2999
        # compute node configuration
        # bridge_mapping = physnet1:br-ethXXX, physnet2:br-ethXXX
        host_desc = [
            environment.HostDescription(
                l2_agent_type=constants.AGENT_TYPE_OVS,
                firewall_driver='openvswitch',
                l3_agent=True,
                dhcp_agent=self.use_dhcp) for _ in range(self.number_of_hosts)]
        env_desc = environment.EnvironmentDescription(
            network_type='vlan',
            mech_drivers='openvswitch',
            bridge_mappings_phy_name=EXTNET_PHYSICAL_NETWORK_NAME)
        env = environment.Environment(env_desc, host_desc)
        super(OvsBridgeVlanMappingMergeDiffPhysicalNetworkTest,
              self).setUp(env)

    def test_vlan_mapping_eip_traffic_on_compute(self):
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(
            tenant_id,
            EXTNET_SEGMENT_ID,
            EXTNET_PHYSICAL_NETWORK_NAME)
        external_vm = self._create_external_vm(ext_net, ext_sub)
        router = self.safe_client.create_router(
            tenant_id, external_network=ext_net['id'])

        # ping external vm to test snat
        vm = self._create_net_subnet_and_vm(
            tenant_id, ['20.0.0.0/24'],
            self.environment.hosts[1], router)
        vm.block_until_ping(external_vm.ip)

        # ping floating ip from external vm
        fip = self.safe_client.create_floatingip(
            tenant_id, ext_net['id'], vm.ip, vm.neutron_port['id'])
        external_vm.block_until_ping(fip['floating_ip_address'])


class OvsBridgeVlanMappingDivideSamePhysicalNetworkTest(
        OvsBridgeVlanMappingTestBase):
    use_dhcp = False
    number_of_hosts = 2

    def setUp(self):
        # telnet network is physnet1
        # external network is physnet1
        # use different physical bridge on network node
        # network node configuration
        # bridge_mapping = physnet1:br-ethXXX, external:br-vlanXXX
        # bridge_vlan_mapping = br-vlanXXX:100:200
        host_desc = [
            environment.HostDescription(
                l2_agent_type=constants.AGENT_TYPE_OVS,
                firewall_driver='openvswitch',
                l3_agent=True,
                dhcp_agent=self.use_dhcp) for _ in range(self.number_of_hosts)]
        env_desc = environment.EnvironmentDescription(
            network_type='vlan',
            mech_drivers='openvswitch',
            bridge_vlan_mappings_ranges='100:200')
        env = environment.Environment(env_desc, host_desc)
        super(OvsBridgeVlanMappingDivideSamePhysicalNetworkTest,
              self).setUp(env)

    def test_vlan_mapping_eip_traffic_on_extra_net_card_network(self):
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(
            tenant_id,
            EXTNET_SEGMENT_ID,
            TENNAT_PHYSICAL_NETWORK_NAME)
        external_vm = self._create_external_vm(ext_net, ext_sub)
        router = self.safe_client.create_router(
            tenant_id, external_network=ext_net['id'])

        # ping external vm to test snat
        vm = self._create_net_subnet_and_vm(
            tenant_id, ['20.0.0.0/24'],
            self.environment.hosts[1], router)
        vm.block_until_ping(external_vm.ip)

        # ping floating ip from external vm
        fip = self.safe_client.create_floatingip(
            tenant_id, ext_net['id'], vm.ip, vm.neutron_port['id'])
        external_vm.block_until_ping(fip['floating_ip_address'])


class OvsBridgeVlanMappingDivideServiceDataPath(OvsBridgeVlanMappingTestBase):
    use_dhcp = False
    number_of_hosts = 1

    def setUp(self):
        # telnet network is physnet1
        # private floating network is physnet1
        # use different bridge on private floating network
        # configuration
        # bridge_mapping = physnet1:br-ethXXX, external:br-vlanXXX
        # bridge_vlan_mapping = br-vlanXXX:200:200
        host_desc = [
            environment.HostDescription(
                l2_agent_type=constants.AGENT_TYPE_OVS,
                firewall_driver='openvswitch',
                of_interface='native',
                dhcp_agent=self.use_dhcp) for _ in range(self.number_of_hosts)]
        env_desc = environment.EnvironmentDescription(
            network_type='vlan',
            mech_drivers='openvswitch',
            enable_service_datapath=True,
            bridge_vlan_mappings_ranges='200:200')
        env = environment.Environment(env_desc, host_desc)
        super(OvsBridgeVlanMappingDivideServiceDataPath, self).setUp(env)
        self._prepare_resources()
        self._setup_pfn_config_and_restart_services()

    def _prepare_resources(self):
        self.tenant_id = uuidutils.generate_uuid()

        self.pfn_net = self.safe_client.create_network(
            self.tenant_id, 'pfn-network',
            network_type='vlan', segmentation_id=PFNNET_SEGMENT_ID,
            physical_network=TENNAT_PHYSICAL_NETWORK_NAME)
        self.pfn_dest = '*******/24'
        self.pfn_nexthop = '***************'
        subnet_routes = [
            {"destination": self.pfn_dest, "nexthop": self.pfn_nexthop}]
        self.subnet_v4 = self.safe_client.create_subnet(
            self.tenant_id, self.pfn_net['id'],
            cidr='*************/24',
            enable_dhcp=False,
            gateway_ip=None,
            name='pfn-sub',
            host_routes=subnet_routes)

    def _create_vm(self):
        network = self.safe_client.create_network(
            self.tenant_id, network_type='vlan')
        self.safe_client.create_subnet(
            self.tenant_id, network['id'], '***********/24')

        return self._boot_fake_vm_in_network(
            self.environment.hosts[0], self.tenant_id, network['id'])

    def _setup_pfn_config_and_restart_services(self):
        private_floating_config = {
            'enable_privatefloating': True,
            'privatefloating_network': self.pfn_net['id'],
        }
        server = self.environment.neutron_server
        server_config = server.neutron_cfg_fixture.config
        server_config['privatefloating'] = private_floating_config
        server.neutron_cfg_fixture.write_config_to_configfile()
        server.restart()
        common_utils.wait_until_true(server.server_is_live)

        l2_agent = self.environment.hosts[0].l2_agent
        l2_agent.restart()
        agent = self.safe_client.client.list_agents(
            agent_type=constants.AGENT_TYPE_OVS)['agents'][0]
        self._wait_until_agent_up(agent['id'])

    def wait_for_service_path_flows_applied(self, vm):
        def check_nat_flow():
            flows = vm.host.br_phys_vlan.dump_flows_for_table(NP_EGRESS_NAT)
            flows_list = flows.splitlines()

            ips = [ip['ip_address'] for ip in vm.neutron_port['fixed_ips']]
            for flow in flows_list:
                if ips[0] in flow:
                    return True
            return False

        common_utils.wait_until_true(check_nat_flow)

    def wait_for_service_path_ingress_output_flows(self, vm):
        def check_output_flow():
            flows = vm.bridge.dump_flows_for_table(INGRESS_TABLE)
            flows_list = flows.splitlines()

            vif_port = vm.bridge.get_vif_port_by_id(vm.neutron_port['id'])
            vlan = vm.bridge.get_port_tag_by_name(vif_port.port_name)
            patch_ports = vm.bridge.get_vif_port_to_ofport_map()
            int_patch_ofport = 1
            for port_name, port_ofport in patch_ports.items():
                if port_name.startswith('int-br-vl'):
                    int_patch_ofport = port_ofport
                    break
            expected_flow = ('table=82, priority=200,ip,reg6={reg6},'
                             'in_port={in_port},dl_dst={dl_dst},'
                             'nw_src={nw_src} actions=output:{output}').format(
                                reg6=hex(vlan), in_port=int_patch_ofport,
                                dl_dst=vif_port.vif_mac, nw_src=self.pfn_dest,
                                output=vif_port.ofport)

            pattern = (r'(cookie=|duration=|n_packets=|n_bytes=|idle_age=)'
                       r'[^,]*,\s*')
            for flow in flows_list:
                cleaned_flow = re.sub(pattern, '', flow)
                cleaned_flow = cleaned_flow.strip()
                if expected_flow == cleaned_flow:
                    return True
            return False

        common_utils.wait_until_true(check_output_flow)

    def clean_resources(self):
        res = self.safe_client.client.list_ports()
        for port in res['ports']:
            self.safe_client.client.delete_port(port['id'])

    def test_divide_service_datapath(self):
        vm = self._create_vm()
        self.wait_for_service_path_flows_applied(vm)
        self.wait_for_service_path_ingress_output_flows(vm)
        self.clean_resources()
