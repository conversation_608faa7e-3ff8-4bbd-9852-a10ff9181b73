# Copyright (c) 2024 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
import re

from neutron_lib import constants
from oslo_utils import uuidutils

from neutron.cmd.sanity import checks
from neutron.common import utils as common_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common import (constants as
                                                                  ovs_constants
                                                                  )
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.ovs_ofctl \
    import br_tun
from neutron.tests.fullstack import base
from neutron.tests.fullstack.resources import environment as env_package
from neutron.tests.fullstack.resources import machine
from neutron.tests.unit import testlib_api
load_tests = testlib_api.module_load_tests

TRAFFIC_MIRROR_TBL = ovs_constants.TRAFFIC_MIRROR
FILTER_TBL = ovs_constants.TRAFFIC_MIRROR_FILTER_TABLE
OUT_TBL = ovs_constants.TRAFFIC_MIRROR_OUTPUT_TABLE


class OVSVersionChecker(object):
    conntrack_supported = None

    @classmethod
    def supports_ovs_firewall(cls):
        if cls.conntrack_supported is None:
            cls.conntrack_supported = checks.ovs_conntrack_supported()
        return cls.conntrack_supported


class TrafficMirrorBaseTest(base.BaseFullStackTestCase):
    of_interface = 'native'
    l2_agent_type = constants.AGENT_TYPE_OVS
    firewall_driver = 'openvswitch'
    num_hosts = 2
    explicitly_egress_direct = False
    network_type = 'vxlan'

    @property
    def traffic_bridge(self):
        return ('br-mirror' + str(uuidutils.generate_uuid()))[0:14]

    @property
    def traffic_peer_patch_port(self):
        return ('int-br-mirror' + str(uuidutils.generate_uuid()))[0:20]

    @property
    def int_peer_patch_port(self):
        return ('mirror-br-int' + str(uuidutils.generate_uuid()))[0:20]

    def setUp(self):
        host_descriptions = [
            env_package.HostDescription(of_interface=self.of_interface,
                                        l2_agent_type=self.l2_agent_type,
                                        firewall_driver=self.firewall_driver,
                                        dhcp_agent=False)
            for _ in range(self.num_hosts)
        ]
        env = env_package.Environment(
            env_package.EnvironmentDescription(
                mech_drivers='openvswitch',
                network_type=self.network_type,
                explicitly_egress_direct=self.explicitly_egress_direct,
                traffic_bridge=self.traffic_bridge,
                enable_traffic_mirror=True,
                traffic_peer_patch_port=self.traffic_peer_patch_port,
                int_peer_patch_port=self.int_peer_patch_port,
            ), host_descriptions)
        super(TrafficMirrorBaseTest, self).setUp(env)


class TrafficMirrorTestOvs(TrafficMirrorBaseTest):
    network_type = 'vxlan'
    scenarios = [('ovs-openflow-native', {
        'firewall_driver': 'openvswitch',
        'of_interface': 'native',
        'l2_agent_type': constants.AGENT_TYPE_OVS,
        'num_hosts': 2,
        'explicitly_egress_direct': False
    })]
    tenant_id = uuidutils.generate_uuid()
    vm_id1 = uuidutils.generate_uuid()
    vm_id2 = uuidutils.generate_uuid()

    def _prepare_test_resources(self):
        self.network = self.safe_client.create_network(self.tenant_id,
                                                       'network-test')
        self.subnet_1 = self.safe_client.create_subnet(self.tenant_id,
                                                       self.network['id'],
                                                       cidr='***********/24',
                                                       enable_dhcp=False,
                                                       name='subnet-1')
        self.subnet_2 = self.safe_client.create_subnet(self.tenant_id,
                                                       self.network['id'],
                                                       cidr='***********/24',
                                                       enable_dhcp=False,
                                                       name='subnet-2')
        sgs = [
            self.safe_client.create_security_group(self.tenant_id)
            for _ in range(1)
        ]
        self.port1 = self.safe_client.create_port(
            self.tenant_id,
            self.network['id'],
            self.environment.hosts[0].hostname,
            device_owner="compute:nova-1",
            device_id=self.vm_id1,
            security_groups=[sgs[0]['id']])
        self.port2 = self.safe_client.create_port(
            self.tenant_id,
            self.network['id'],
            self.environment.hosts[1].hostname,
            device_owner="compute:nova-2",
            device_id=self.vm_id2,
            security_groups=[sgs[0]['id']])
        self.safe_client.create_security_group_rule(
            self.tenant_id,
            sgs[0]['id'],
            direction=constants.INGRESS_DIRECTION,
            ethertype=constants.IPv4,
            protocol=constants.PROTO_NAME_ICMP)
        self.safe_client.create_security_group_rule(
            self.tenant_id,
            sgs[0]['id'],
            direction=constants.INGRESS_DIRECTION,
            ethertype=constants.IPv6,
            protocol=constants.PROTO_NAME_ICMP)
        vm1 = self.useFixture(
            machine.FakeFullstackMachine(self.environment.hosts[0],
                                         self.network['id'],
                                         self.tenant_id,
                                         self.safe_client,
                                         neutron_port=self.port1,
                                         use_dhcp=False,
                                         use_dhcp6=False))
        vm2 = self.useFixture(
            machine.FakeFullstackMachine(self.environment.hosts[1],
                                         self.network['id'],
                                         self.tenant_id,
                                         self.safe_client,
                                         neutron_port=self.port2,
                                         use_dhcp=False,
                                         use_dhcp6=False))
        return machine.FakeFullstackMachinesList([vm1, vm2])

    def create_traffic_mirror_filter(self, tenant_id, name):
        return self.safe_client.create_traffic_mirror_filter(tenant_id, name)

    def create_traffic_mirror_filter_rule(self,
                                          tenant_id,
                                          filter_id,
                                          ethertype='IPv4',
                                          src_cidr=None,
                                          dst_cidr=None,
                                          src_port_range=None,
                                          dst_port_range=None,
                                          protocol="all",
                                          direction='ingress',
                                          action='accept',
                                          priority=10):
        body = {
            'tenant_id': tenant_id,
            'protocol': protocol,
            'traffic_mirror_filter_id': filter_id,
            'ethertype': ethertype,
            'direction': direction,
            'action': action,
            'priority': priority,
        }
        if src_cidr:
            body['src_cidr'] = src_cidr
        if dst_cidr:
            body['dst_cidr'] = dst_cidr
        if src_port_range:
            body['src_port_range'] = src_port_range
        if dst_port_range:
            body['dst_port_range'] = dst_port_range
        return self.safe_client.create_traffic_mirror_filter_rule(body)

    def create_traffic_mirror_session(self,
                                      tenant_id,
                                      sources,
                                      target_port_id,
                                      name=None,
                                      filter_id=None,
                                      priority=1,
                                      packet_length=100,
                                      virtual_network_id=1,
                                      enable=True):
        body = {
            'tenant_id': tenant_id,
            'traffic_mirror_sources': sources,
            'traffic_mirror_target_port_id': target_port_id,
            'traffic_mirror_target_type': 'eni',
            'priority': priority,
            'packet_length': packet_length,
            'enabled': enable,
        }
        if name:
            body['name'] = name
        if filter_id:
            body['traffic_mirror_filter_id'] = filter_id
        if virtual_network_id:
            body['virtual_network_id'] = virtual_network_id
        return self.safe_client.create_traffic_mirror_session(body)

    def test_traffic_mirror(self):
        try:
            self._create_traffic_mirror_session()
        except Exception as test_ex:
            raise test_ex
        finally:
            for host in self.environment.hosts:
                bridge_name = (host.l2_agent.agent_config['TRAFFIC_MIRROR']
                               ['traffic_bridge'])
                bridge = br_tun.OVSTunnelBridge(bridge_name, "system")
                if bridge.bridge_exists(bridge_name):
                    bridge.delete_bridge(bridge_name)

    def _create_traffic_mirror_session(self):
        vms = self._prepare_test_resources()
        vms.block_until_all_boot()
        self.tmf = self.create_traffic_mirror_filter(self.tenant_id,
                                                     'test_filter')
        self.create_traffic_mirror_filter_rule(tenant_id=self.tenant_id,
                                               filter_id=self.tmf['id'],
                                               src_cidr='***********/24',
                                               dst_cidr='***********/24',
                                               src_port_range='1024:2048',
                                               dst_port_range='1024:2048',
                                               direction='ingress',
                                               action='accept',
                                               priority=90)
        self.create_traffic_mirror_filter_rule(tenant_id=self.tenant_id,
                                               filter_id=self.tmf['id'],
                                               src_cidr='***********/24',
                                               direction='ingress',
                                               action='reject',
                                               priority=30)
        self.create_traffic_mirror_filter_rule(filter_id=self.tmf['id'],
                                               tenant_id=self.tenant_id,
                                               direction='egress',
                                               action='accept',
                                               priority=90)
        self.create_traffic_mirror_filter_rule(tenant_id=self.tenant_id,
                                               filter_id=self.tmf['id'],
                                               dst_cidr='***********/24',
                                               dst_port_range='1024:1024',
                                               ethertype='IPv4',
                                               direction='egress',
                                               action='reject',
                                               priority=50)
        self.tm_session = self.create_traffic_mirror_session(
            self.tenant_id,
            sources=[self.port1['id']],
            target_port_id=self.port2['id'],
            name="tes",
            filter_id=self.tmf['id'])
        segmentation_id = self.tm_session['segmentation_id']
        port_mac = vms[0].mac_address
        port_id = self.port1['id']
        vif_port = vms[0].bridge.get_vif_port_by_id(port_id)
        local_vlan = vms[0].bridge.get_port_tag_by_name(vif_port.port_name)
        tun_patch_ofport = -1
        mirror_patch_ofport = -1
        self.mirror_bridge = (self.environment.hosts[0].l2_agent.agent_config
                              )['TRAFFIC_MIRROR']['traffic_bridge']
        self.br_mirror = br_tun.OVSTunnelBridge(self.mirror_bridge, "system")
        self.mirror_bridge2 = (
            self.environment.hosts[1].l2_agent.agent_config['TRAFFIC_MIRROR']
            ['traffic_bridge'])
        self.br_mirror2 = br_tun.OVSTunnelBridge(self.mirror_bridge2, "system")

        self.wait_for_bridge_resources(vms[0])
        patch_port_dict = vms[0].bridge.get_vif_port_to_ofport_map()
        for patch_name in patch_port_dict.keys():
            if patch_name.startswith('patch-tun'):
                tun_patch_ofport = patch_port_dict[patch_name]
            if patch_name.startswith('int-br-mirror'):
                mirror_patch_ofport = patch_port_dict[patch_name]
        self.assertGreater(mirror_patch_ofport, -1)
        self.assertGreater(tun_patch_ofport, -1)

        target_port_id = self.port2['id']
        target_vif_port = vms[1].bridge.get_vif_port_by_id(target_port_id)
        target_vlan = vms[1].bridge.get_port_tag_by_name(
            target_vif_port.port_name)

        if self.firewall_driver == 'openvswitch':
            self.wait_for_source_port_flows(vms[0], vif_port, port_mac,
                                            local_vlan, tun_patch_ofport)
            self.wait_for_filter_flows(vms[0], vif_port, local_vlan, port_mac)
            self.wait_for_output_flows(vms[0], vif_port, local_vlan, port_mac,
                                       mirror_patch_ofport)
            self.wait_for_mirror_bridge_flows(vlan=local_vlan, mac=port_mac)
            self.wait_for_target_mirror_bridge_flows(vlan=target_vlan,
                                                     tun_id=segmentation_id)

        if self.firewall_driver == 'openvswitch_stateless':
            self.wait_for_stateless_source_port_flows(vms[0], vif_port,
                                                      local_vlan, port_mac,
                                                      tun_patch_ofport)
            self.wait_for_stateless_filter_flows(vms[0], vif_port, local_vlan,
                                                 port_mac)
            self.wait_for_stateless_output_flows(vms[0], vif_port, local_vlan,
                                                 port_mac, mirror_patch_ofport)
            self.wait_for_mirror_bridge_flows(vlan=local_vlan, mac=port_mac)
            self.wait_for_target_mirror_bridge_flows(vlan=target_vlan,
                                                     tun_id=segmentation_id)

    def wait_for_target_mirror_bridge_flows(self, vlan, tun_id):
        def check_flows():
            target_mirror_flows = self.br_mirror2.dump_flows_for_table(table=0)
            flows_arr = re.split(r'\n', target_mirror_flows)
            observed_flows = [
                self.get_flow(flow) for flow in flows_arr if 'drop' not in flow
            ]
            patch_ports = self.br_mirror2.get_vif_port_to_ofport_map()
            int_patch_ofport = 1
            for port_name, port_ofport in patch_ports.items():
                if port_name.startswith('mirror-br'):
                    int_patch_ofport = port_ofport
            expected_flow = (
                'table=0,priority=10,tun_id={tun_id} '
                'actions=mod_vlan_vid:{vlan},output:{int_patch}'.format(
                    tun_id=hex(tun_id), vlan=vlan, int_patch=int_patch_ofport))
            if expected_flow in observed_flows:
                return True
            else:
                return False

        common_utils.wait_until_true(check_flows)

    def wait_for_mirror_bridge_flows(self, vlan, mac):
        def check_flows():
            flows = self.br_mirror.dump_flows_for_table(table=0)
            flows_arr = re.split(r'\n', flows)
            observed_flows = [
                self.get_flow(flow) for flow in flows_arr if 'drop' not in flow
            ]
            observed_flows = [
                re.sub(r'actions=.*$', '', flow) for flow in observed_flows
            ]
            expected_flows = [
                'table=0,priority=210,dl_vlan={vlan},dl_src={mac} '.format(
                    vlan=vlan, mac=mac),
                'table=0,priority=210,dl_vlan={vlan},dl_dst={mac} '.format(
                    vlan=vlan, mac=mac),
            ]
            for flow in expected_flows:
                if flow not in observed_flows:
                    return False
            return True

        common_utils.wait_until_true(check_flows)

    def wait_for_bridge_resources(self, vm):
        def check_br_mirror():
            ports = vm.bridge.get_vif_port_to_ofport_map().keys()
            tun_patch_found = False
            mirror_patch_found = False
            for port_name in ports:
                if port_name.startswith('patch-tun'):
                    tun_patch_found = True
                if port_name.startswith('int-br-mirror'):
                    mirror_patch_found = True
            return tun_patch_found and mirror_patch_found

        common_utils.wait_until_true(check_br_mirror, timeout=90)

    def wait_for_stateless_output_flows(self, vm, vif_port, local_vlan,
                                        port_mac, mirror_patch_ofport):
        def check_output_flows():
            flows = vm.bridge.dump_flows_for(table=OUT_TBL)
            observed_flow = re.split(r'\n', flows)
            observed_flow_arr = [self.get_flow(flow) for flow in observed_flow]
            expected_flow_arr = [
                'table=223,priority=110,in_port={in_port},dl_vlan={vlan},'
                'dl_src={mac} actions=output:{mirror_patch_ofport}'.format(
                    in_port=vif_port.ofport,
                    vlan=local_vlan,
                    mac=port_mac,
                    mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=110,dl_vlan={vlan},dl_dst={mac} '
                'actions=mod_vlan_vid:{vlan},output:{mirror_patch_ofport}'.
                format(vlan=local_vlan,
                       mac=port_mac,
                       mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=100,in_port={in_port},dl_src={mac} '
                'actions=mod_vlan_vid:{vlan},output:{mirror_patch_ofport}'.
                format(in_port=vif_port.ofport,
                       mac=port_mac,
                       vlan=local_vlan,
                       mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=1 actions=drop'
            ]
            return sorted(observed_flow_arr) == sorted(expected_flow_arr)

        common_utils.wait_until_true(check_output_flows)

    def wait_for_filter_flows(self, vm, vif_port, local_vlan, port_mac):
        def check_filter_flows():
            observed_filter_flow = vm.bridge.dump_flows_for(table=FILTER_TBL)
            observed_flow_list = observed_filter_flow.split('\n')
            observed_flow_arr = [
                self.get_flow(flow) for flow in observed_flow_list
            ]
            expected_flow_arr = [
                'table=220,priority=190,ip,reg6={vlan},'
                'dl_dst={mac},nw_src=***********/24,'
                'nw_dst=***********/24 actions=resubmit(,221)'.format(
                    mac=port_mac, vlan=hex(local_vlan)),
                'table=220,priority=190,ip,in_port={in_port},dl_src={mac} '
                'actions=resubmit(,221)'.format(mac=port_mac,
                                                in_port=vif_port.ofport),
                'table=220,priority=150,ip,in_port={in_port},dl_src={mac},'
                'nw_dst=***********/24 actions=drop'.format(
                    mac=port_mac, in_port=vif_port.ofport),
                'table=220,priority=130,ip,reg6={vlan},dl_dst={mac},'
                'nw_src=***********/24 actions=drop'.format(
                    mac=port_mac, vlan=hex(local_vlan)),
            ]
            for flow in expected_flow_arr:
                if flow not in observed_flow_arr:
                    return False
            return True

        common_utils.wait_until_true(check_filter_flows)

    def wait_for_stateless_source_port_flows(self, vm, vif_port, local_vlan,
                                             port_mac, tun_patch_ofport):
        def check_port_flows():
            observed_flows = vm.bridge.dump_flows_for(table=TRAFFIC_MIRROR_TBL)

            observed_flow_list = observed_flows.replace('\\n',
                                                        '\n').split('\n')
            observed_flow_arr = [
                self.get_flow(flow) for flow in observed_flow_list
                if 'resubmit(,220)' in flow
            ]
            expected_flow_arr = [
                'table={table},priority=111,in_port={in_port},dl_vlan={vlan},'
                'dl_dst={mac} '
                'actions=resubmit(,71),mod_vlan_vid:{vlan},resubmit(,220)'.
                format(table=TRAFFIC_MIRROR_TBL,
                       in_port=tun_patch_ofport,
                       vlan=local_vlan,
                       mac=port_mac),
                'table={table},priority=101,in_port={in_port},'
                'dl_vlan={vlan},dl_src={mac} '
                'actions=resubmit(,71),strip_vlan,resubmit(,220)'.format(
                    table=TRAFFIC_MIRROR_TBL,
                    in_port=vif_port.ofport,
                    vlan=local_vlan,
                    mac=port_mac),
                'table={table},priority=101,dl_vlan={vlan},dl_dst={mac} '
                'actions=resubmit(,71),mod_vlan_vid:{vlan},resubmit(,220)'.
                format(table=TRAFFIC_MIRROR_TBL, vlan=local_vlan,
                       mac=port_mac),
            ]
            return sorted(observed_flow_arr) == sorted(expected_flow_arr)

        common_utils.wait_until_true(check_port_flows)

    def wait_for_stateless_filter_flows(self, vm, vif_port, local_vlan,
                                        port_mac):
        def check_filter_flows():
            observed_filter_flow = vm.bridge.dump_flows_for(table=FILTER_TBL)
            observed_flow_list = observed_filter_flow.split('\n')
            observed_flow_arr = [
                self.get_flow(flow) for flow in observed_flow_list
            ]
            expected_flow_arr = [
                'table=220,priority=190,ip,dl_vlan={vlan},'
                'dl_dst={mac},nw_src=***********/24,'
                'nw_dst=***********/24 actions=resubmit(,221)'.format(
                    mac=port_mac, vlan=local_vlan),
                'table=220,priority=190,ip,in_port={in_port},dl_src={mac} '
                'actions=resubmit(,221)'.format(mac=port_mac,
                                                in_port=vif_port.ofport),
                'table=220,priority=150,ip,in_port={in_port},dl_src={mac},'
                'nw_dst=***********/24 actions=drop'.format(
                    mac=port_mac, in_port=vif_port.ofport),
                'table=220,priority=130,ip,dl_vlan={vlan},dl_dst={mac},'
                'nw_src=***********/24 actions=drop'.format(mac=port_mac,
                                                            vlan=local_vlan),
            ]
            for flow in expected_flow_arr:
                if flow not in observed_flow_arr:
                    return False
            return True

        common_utils.wait_until_true(check_filter_flows)

    def wait_for_source_port_flows(self, vm, vif_port, port_mac, vlan,
                                   patch_ofport):
        def check_port_flow():
            egress_flow = vm.bridge.dump_flows_for(table=TRAFFIC_MIRROR_TBL,
                                                   in_port=vif_port.ofport,
                                                   reg6=vlan,
                                                   dl_src=port_mac)
            egress_flow = self.get_flow(egress_flow)
            expected_egress_flow = (
                'table={table},priority=101,reg6={vlan},'
                'in_port={in_port},'
                'dl_src={mac} '
                'actions=resubmit(,71),'
                'load:{vlan}->NXM_NX_REG6[],resubmit(,220)').format(
                    table=TRAFFIC_MIRROR_TBL,
                    vlan=hex(vlan),
                    mac=port_mac,
                    in_port=vif_port.ofport)

            ingress_flow = vm.bridge.dump_flows_for(table=TRAFFIC_MIRROR_TBL,
                                                    reg6=vlan,
                                                    dl_dst=port_mac)
            ingress_flows = re.split(r'\n', ingress_flow)
            ingress_flow_arr = [
                self.get_flow(flow) for flow in ingress_flows
                if 'resubmit(,220)' in flow
            ]
            expected_ingress_flow_arr = [
                'table={table},priority=111,reg6={vlan},'
                'in_port={patch_ofport},dl_dst={mac} '
                'actions=resubmit(,81),load:0x1->NXM_NX_REG6[],resubmit(,220)'.
                format(table=TRAFFIC_MIRROR_TBL,
                       vlan=hex(vlan),
                       patch_ofport=patch_ofport,
                       mac=port_mac),
                'table={table},priority=101,reg6={vlan},dl_dst={mac} '
                'actions=resubmit(,71),load:0x1->NXM_NX_REG6[],resubmit(,220)'.
                format(table=TRAFFIC_MIRROR_TBL, vlan=hex(vlan), mac=port_mac)
            ]

            return (egress_flow == expected_egress_flow and
                    ingress_flow_arr == expected_ingress_flow_arr)

        common_utils.wait_until_true(check_port_flow)

    def get_flow(self, flow, fields_to_remove=tuple()):
        if not fields_to_remove:
            fields_to_remove = [
                'cookie', 'n_packets', 'n_bytes', 'idle_age', 'duration'
            ]
        pattern = re.compile(r'|'.join(
            r'{}=[A-Za-z0-9]*(\d+\.\d+s)?,\s*'.format(key)
            for key in fields_to_remove))
        result = re.sub(pattern, '', flow).replace(', ', ',').lstrip()
        return str(result)

    def wait_for_output_flows(self, vm, vif_port, local_vlan, port_mac,
                              mirror_patch_ofport):
        def check_output_flows():
            flows = vm.bridge.dump_flows_for(table=OUT_TBL)
            observed_flow = re.split(r'\n', flows)
            observed_flow_arr = [self.get_flow(flow) for flow in observed_flow]
            expected_flow_arr = [
                'table=223,priority=110,in_port={in_port},dl_vlan={vlan},'
                'dl_src={mac} actions=output:{mirror_patch_ofport}'.format(
                    in_port=vif_port.ofport,
                    vlan=local_vlan,
                    mac=port_mac,
                    mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=100,reg6={vlan},dl_dst={mac} '
                'actions=mod_vlan_vid:{vlan_num},output:{mirror_patch_ofport}'.
                format(vlan=hex(local_vlan),
                       vlan_num=local_vlan,
                       mac=port_mac,
                       mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=110,dl_vlan={vlan},dl_dst={mac} '
                'actions=output:{mirror_patch_ofport}'.format(
                    vlan=local_vlan,
                    mac=port_mac,
                    mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=100,in_port={in_port},dl_src={mac} '
                'actions=mod_vlan_vid:{vlan},output:{mirror_patch_ofport}'.
                format(in_port=vif_port.ofport,
                       mac=port_mac,
                       vlan=local_vlan,
                       mirror_patch_ofport=mirror_patch_ofport),
                'table=223,priority=1 actions=drop'
            ]
            return sorted(observed_flow_arr) == sorted(expected_flow_arr)

        common_utils.wait_until_true(check_output_flows)


class TrafficMirrorTestOvsStateless(TrafficMirrorTestOvs):
    scenarios = [
        ('ovs-stateless-openflow-native', {
            'firewall_driver': 'openvswitch_stateless',
            'of_interface': 'native',
            'l2_agent_type': constants.AGENT_TYPE_OVS,
            'num_hosts': 2,
            'explicitly_egress_direct': True
        }),
    ]
