#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from neutron_lib import context
from oslo_config import cfg

from neutron.agent.common import ovs_lib
from neutron.agent.l2.extensions.nat import service_datapath
from neutron.conf.agent import common as agent_config
from neutron.conf.plugins.ml2.drivers import ovs_conf
from neutron.plugins.ml2.drivers.openvswitch.agent \
    import ovs_agent_extension_api as ovs_ext_api
from neutron.tests import base


class ServiceDataPathAgentExtensionTestCase(base.BaseTestCase):

    def setUp(self):
        super(ServiceDataPathAgentExtensionTestCase, self).setUp()
        agent_config.register_availability_zone_opts_helper(cfg.CONF)
        ovs_conf.register_ovs_agent_opts(cfg=cfg.CONF)
        self.context = context.get_admin_context()
        self.int_br = mock.Mock()
        self.tun_br = mock.Mock()
        self.vlan_br = mock.Mock()
        self.plugin_rpc = mock.Mock()
        self.remote_resource_cache = mock.Mock()
        self.plugin_rpc.remote_resource_cache = self.remote_resource_cache
        self.sdp_ext = service_datapath.ServiceDataPathAgentExtension()
        self.init_privatefloating_info = mock.patch.object(
            self.sdp_ext, 'init_privatefloating_info').start()
        self.bridge_mappings = {"vlan": "br-vlan"}
        self.int_ofport = 200
        self.phys_ofport = 100
        self.agent_api = ovs_ext_api.OVSAgentExtensionAPI(
            self.int_br,
            self.tun_br,
            plugin_rpc=self.plugin_rpc,
            phys_brs={"vlan": self.vlan_br},
            phys_ofports={"vlan": self.phys_ofport},
            bridge_mappings=self.bridge_mappings)
        self.sdp_ext.consume_api(self.agent_api)
        self.sdp_ext.initialize(None, None)
        # set int_br back to mock
        self.sdp_ext.int_br = self.int_br
        self.sdp_ext.enable_private_floating = True
        self.sdp_ext.privatefloating_info = {
            "availability_zone_privatefloating_network": {"nova": "net1"},
            "default_availability_zone": "nova"
        }
        self.sdp_ext.route_destinations = {'*******/24'}
        self.sdp_ext.rcache_api.get_resource_by_id = (
            mock.Mock(side_effect=self._get_resource_by_id))

    def tearDown(self):
        self.sdp_ext.app_mgr.uninstantiate("ARPProcessor")
        super(ServiceDataPathAgentExtensionTestCase, self).tearDown()

    def _get_resource_by_id(self, rtype, id):
        subnet_data = {'1': {'cidr': '*******/24'},
                       '2': {'cidr': '*******/24'},
                       '3': {'cidr': '*******/25'},
                       '4': {'cidr': '*******/25'}}
        return subnet_data[id]

    def test_port_subnet_overlay_with_private_floating_route_dest(self):
        port_local_vlan = 10
        port_mac_address = "aa:aa:aa:aa:aa:aa"
        port_name = "tap-p1"
        port_id = "p1"
        port_ofport = 1
        port_device_owner = "compute:test"
        port_provider_ips = [("*******", "3")]
        mock_log = mock.patch.object(service_datapath.LOG, 'warning').start()
        self.int_br.db_get_val = mock.Mock()
        self.int_br.db_get_val.return_value = {"tag": port_local_vlan}
        with mock.patch.object(self.sdp_ext.ext_api, "get_provider_ip",
                               return_value=port_provider_ips), \
                mock.patch.object(
                    self.sdp_ext,
                    "process_install_service_path_flows"):
            port = {"port_id": port_id,
                    "fixed_ips": [{"ip_address": "*******",
                                   "subnet_id": "4"}],
                    "vif_port": ovs_lib.VifPort(port_name, port_ofport,
                                                port_id,
                                                port_mac_address, "br-int"),
                    "device_owner": port_device_owner,
                    "mac_address": port_mac_address,
                    "segmentation_id": 100,
                    "physical_network": 'test'}
            self.sdp_ext.handle_port(self.context, port)
            mock_log.assert_called_with(
                'Port subnet %(p_sub)s overlaps with private '
                'floating subnet route destination %(dest)s, '
                'it may affect the traffic of '
                'service datapath',
                {'p_sub': '*******/25', 'dest': '*******/24'})

    def test_port_subnet_overlay_with_private_floating_subnet(self):
        port_local_vlan = 10
        port_mac_address = "aa:aa:aa:aa:aa:aa"
        port_name = "tap-p1"
        port_id = "p1"
        port_ofport = 1
        port_device_owner = "compute:test"
        port_provider_ips = [("*******", "3")]
        mock_log = mock.patch.object(service_datapath.LOG, 'warning').start()
        self.int_br.db_get_val = mock.Mock()
        self.int_br.db_get_val.return_value = {"tag": port_local_vlan}
        with mock.patch.object(self.sdp_ext.ext_api, "get_provider_ip",
                               return_value=port_provider_ips), \
                mock.patch.object(
                    self.sdp_ext,
                    "process_install_service_path_flows"):
            port = {"port_id": port_id,
                    "fixed_ips": [{"ip_address": "*******",
                                   "subnet_id": "1"}],
                    "vif_port": ovs_lib.VifPort(port_name, port_ofport,
                                                port_id,
                                                port_mac_address, "br-int"),
                    "device_owner": port_device_owner,
                    "mac_address": port_mac_address,
                    "segmentation_id": 100,
                    "physical_network": 'test'}
            self.sdp_ext.handle_port(self.context, port)
            mock_log.assert_called_with(
                'Port subnet %(p_sub)s overlaps with private floating '
                'subnet %(pf_sub)s, it may affect the traffic of '
                'service datapath',
                {'p_sub': '*******/24', 'pf_sub': '*******/25'})

    def test_handle_port(self):
        port_local_vlan = 10
        port_mac_address = "aa:aa:aa:aa:aa:aa"
        port_name = "tap-p1"
        port_id = "p1"
        port_ofport = 1
        port_device_owner = "compute:test"
        port_provider_ips = [("*******", "2")]
        port_seg_id = 100
        port_phy_net = 'default'
        self.int_br.db_get_val = mock.Mock()
        self.int_br.db_get_val.return_value = {"tag": port_local_vlan}
        with mock.patch.object(self.sdp_ext.ext_api, "get_provider_ip",
                               return_value=port_provider_ips),\
             mock.patch.object(
                 self.sdp_ext,
                 "process_install_service_path_flows") as process_sdp:
            port = {"port_id": port_id,
                    "fixed_ips": [{"ip_address": "*******",
                                   "subnet_id": "1"}],
                    "vif_port": ovs_lib.VifPort(port_name, port_ofport,
                                                port_id,
                                                port_mac_address, "br-int"),
                    "device_owner": port_device_owner,
                    "mac_address": port_mac_address,
                    "segmentation_id": port_seg_id,
                    "physical_network": port_phy_net}
            self.sdp_ext.handle_port(self.context, port)

            port_info = {"port_id": port_id,
                         "device_owner": port_device_owner,
                         "port_name": port_name,
                         "vlan": port_local_vlan,
                         "mac_address": port_mac_address,
                         "fixed_ips": port["fixed_ips"],
                         "provider_ips": port_provider_ips,
                         "ofport": port_ofport,
                         "segmentation_id": port_seg_id,
                         "physical_network": port_phy_net}
            self.int_br.db_get_val.assert_called_once_with(
                'Port', port_name, 'other_config')
            process_sdp.assert_called_once_with(port_info)

    def test_handle_port_no_compute(self):
        port_local_vlan = 10
        port_mac_address = "aa:aa:aa:aa:aa:aa"
        port_name = "tap-p1"
        port_id = "p1"
        port_ofport = 1
        port_device_owner = "fake:test"
        port_provider_ips = [("*******", "2")]
        self.int_br.db_get_val = mock.Mock()
        self.int_br.db_get_val.return_value = {"tag": port_local_vlan}
        with mock.patch.object(self.sdp_ext.ext_api, "get_provider_ip",
                               return_value=port_provider_ips),\
             mock.patch.object(
                 self.sdp_ext,
                 "process_install_service_path_flows") as process_sdp:
            port = {"port_id": port_id,
                    "fixed_ips": [{"ip_address": "*******",
                                   "subnet_id": "1"}],
                    "vif_port": ovs_lib.VifPort(port_name, port_ofport,
                                                port_id,
                                                port_mac_address, "br-int"),
                    "device_owner": port_device_owner,
                    "mac_address": port_mac_address}
            self.sdp_ext.handle_port(self.context, port)
            self.int_br.db_get_val.assert_not_called()
            process_sdp.assert_not_called()

    def test_handle_port_no_compute_with_config(self):
        cfg.CONF.set_override('extra_allowed_device_owners',
                              ['fake:test', 'neutron:lbv2_interface'],
                              group='SERVICEPATH')
        port_local_vlan = 10
        port_mac_address = "aa:aa:aa:aa:aa:aa"
        port_name = "tap-p1"
        port_id = "p1"
        port_ofport = 1
        port_device_owner = "fake:test"
        port_provider_ips = [("*******", "2")]
        port_seg_id = 100
        port_phy_net = 'default'
        self.int_br.db_get_val = mock.Mock()
        self.int_br.db_get_val.return_value = {"tag": port_local_vlan}
        with mock.patch.object(self.sdp_ext.ext_api, "get_provider_ip",
                               return_value=port_provider_ips),\
             mock.patch.object(
                 self.sdp_ext,
                 "process_install_service_path_flows") as process_sdp:
            port = {"port_id": port_id,
                    "fixed_ips": [{"ip_address": "*******",
                                   "subnet_id": "1"}],
                    "vif_port": ovs_lib.VifPort(port_name, port_ofport,
                                                port_id,
                                                port_mac_address, "br-int"),
                    "device_owner": port_device_owner,
                    "mac_address": port_mac_address,
                    "segmentation_id": port_seg_id,
                    "physical_network": port_phy_net}
            self.sdp_ext.handle_port(self.context, port)

            port_info = {"port_id": port_id,
                         "device_owner": port_device_owner,
                         "port_name": port_name,
                         "vlan": port_local_vlan,
                         "mac_address": port_mac_address,
                         "fixed_ips": port["fixed_ips"],
                         "provider_ips": port_provider_ips,
                         "ofport": port_ofport,
                         "segmentation_id": port_seg_id,
                         "physical_network": port_phy_net}
            self.int_br.db_get_val.assert_called_once_with(
                'Port', port_name, 'other_config')
            process_sdp.assert_called_once_with(port_info)
