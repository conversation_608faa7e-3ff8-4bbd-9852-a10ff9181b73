#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import constants as lib_const
from neutron_lib import context
from oslo_utils import uuidutils

from neutron.agent.l3 import agent as l3_agent
from neutron.agent.l3.extensions import elastic_snat
from neutron.agent.l3 import l3_agent_extension_api as l3_ext_api
from neutron.agent.l3 import router_info as l3router
from neutron.agent.linux import iptables_manager
from neutron.objects import elastic_snat as elastic_snat_obj
from neutron.objects import router
from neutron.tests.unit.agent.l3 import test_agent

_uuid = uuidutils.generate_uuid

HOSTNAME = 'testhost'


class ElasticSnatAgentExtensionBaseTestCase(
    test_agent.BasicRouterOperationsFramework):

    def setUp(self):
        super(ElasticSnatAgentExtensionBaseTestCase, self).setUp()

        self.elastic_snat_ext = elastic_snat.ElasticSnatAgentExtension()

        self.context = context.get_admin_context()
        self.connection = mock.Mock()
        self.ext_net_id = _uuid()
        self.router_id = _uuid()
        self.floatingip_1 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='***********',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.floatingip_2 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='***********',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.gateway_port_id = _uuid()

        self.elastic_snat_1 = elastic_snat_obj.ElasticSnat(
            context=None, id=_uuid(), name="elastic_snat_1",
            router_id=self.router_id,
            gateway_port_id=self.gateway_port_id,
            floatingip_id=self.floatingip_1.id,
            internal_cidrs=['*******', '*******', '*******/24'],
            floating_ip_address=self.floatingip_1.floating_ip_address)

        self.subnet_id_1 = _uuid()
        self.subnet_id_2 = _uuid()
        self.elastic_snat_2 = elastic_snat_obj.ElasticSnat(
            context=None, id=_uuid(), name="elastic_snat_2",
            router_id=self.router_id,
            gateway_port_id=self.gateway_port_id,
            floatingip_id=self.floatingip_2.id,
            subnets=[self.subnet_id_1, self.subnet_id_2],
            internal_cidrs=['*******/24', '*******/24'],
            floating_ip_address=self.floatingip_2.floating_ip_address)
        self.elastic_snats = [self.elastic_snat_1, self.elastic_snat_2]

        self.agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)
        self.ex_gw_port = {'id': self.gateway_port_id,
                           'fixed_ips': [{'ip_address': '************'}]}
        self.fip1 = {
            'id': self.floatingip_1.id,
            'floating_ip_address': self.floatingip_1.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'host': HOSTNAME}
        self.fip2 = {
            'id': self.floatingip_2.id,
            'floating_ip_address': self.floatingip_2.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'host': HOSTNAME}
        self.router = {'id': self.router_id,
                       'gw_port': self.ex_gw_port,
                       'ha': False,
                       'distributed': False,
                       '_elastic_snat_floatingips': [self.fip1, self.fip2],
                       '_elastic_snat_rules': self.elastic_snats}
        self.router_info = l3router.RouterInfo(
            self.agent, self.router_id, self.router,
            **self.ri_kwargs)
        self.fip_managed_by_elastic_snats = [self.floatingip_1.id,
                                             self.floatingip_2.id]
        self.router_info.ex_gw_port = self.ex_gw_port
        self.router_info.fip_managed_by_elastic_snats = (
            self.fip_managed_by_elastic_snats)
        self.agent.router_info[self.router['id']] = self.router_info

        def _get_router_info(router_id):
            return self.agent.router_info.get(router_id)
        self.get_router_info = mock.patch(
            'neutron.agent.l3.l3_agent_extension_api.'
            'L3AgentExtensionAPI.get_router_info').start()
        self.get_router_info.side_effect = _get_router_info

        self.agent_api = l3_ext_api.L3AgentExtensionAPI(None, None)
        self.elastic_snat_ext.consume_api(self.agent_api)


class ElasticSnatAgentExtensionTestCase(ElasticSnatAgentExtensionBaseTestCase):

    def setUp(self):
        super(ElasticSnatAgentExtensionTestCase, self).setUp()
        self.elastic_snat_ext.initialize(
            self.connection, lib_const.L3_AGENT_MODE)

    def _assert_called_iptables_process(self,
                                        mock_add_rule, mock_add_fip,
                                        mock_send_fip_status):

        mock_add_rule.assert_has_calls(
            [mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s ******* '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_1.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s ******* '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_1.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/24 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_1.floating_ip_address}),

             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/24 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_2.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/24 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_2.floating_ip_address})])

        fip1 = str(self.floatingip_1.floating_ip_address)
        fip2 = str(self.floatingip_2.floating_ip_address)
        mock_add_fip.assert_has_calls(
            [mock.call({'floating_ip_address': fip1}, mock.ANY, mock.ANY),
             mock.call({'floating_ip_address': fip2}, mock.ANY, mock.ANY)])
        fip_status = {
            self.floatingip_1.id: lib_const.FLOATINGIP_STATUS_ACTIVE,
            self.floatingip_2.id: lib_const.FLOATINGIP_STATUS_ACTIVE}
        mock_send_fip_status.assert_called_once_with(mock.ANY, fip_status)

    @mock.patch.object(elastic_snat.ElasticSnatAgentExtension,
                       '_sending_elastic_snat_fip_status')
    @mock.patch.object(iptables_manager.IptablesTable, 'add_rule')
    @mock.patch.object(l3router.RouterInfo, 'add_floating_ip')
    def test_add_update_router(self, mock_add_fip,
                               mock_add_rule,
                               mock_send_fip_status):
        mock_add_fip.return_value = lib_const.FLOATINGIP_STATUS_ACTIVE
        self.elastic_snat_ext.add_router(self.context, self.router)
        self._assert_called_iptables_process(mock_add_rule, mock_add_fip,
                                             mock_send_fip_status)

        mock_add_fip.reset_mock()
        mock_send_fip_status.reset_mock()
        mock_add_rule.reset_mock()

        router = {'id': self.router_id,
                  'gw_port': self.ex_gw_port,
                  'ha': False,
                  'distributed': False,
                  '_elastic_snat_floatingips': [self.fip2],
                  '_elastic_snat_rules': [self.elastic_snat_2]}
        router_info = l3router.RouterInfo(
            self.agent, self.router_id, router,
            **self.ri_kwargs)
        self.agent.router_info[self.router_id] = router_info
        self.elastic_snat_ext.update_router(self.context, router)
        mock_add_rule.assert_has_calls(
            [mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/24 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_2.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/24 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_2.floating_ip_address})])

        fip2 = str(self.floatingip_2.floating_ip_address)
        mock_add_fip.assert_has_calls(
            [mock.call({'floating_ip_address': fip2}, mock.ANY, mock.ANY)])
        fip_status = {
            self.floatingip_2.id: lib_const.FLOATINGIP_STATUS_ACTIVE}
        mock_send_fip_status.assert_called_once_with(mock.ANY, fip_status)

    @mock.patch.object(elastic_snat.ElasticSnatAgentExtension,
                       '_sending_elastic_snat_fip_status')
    @mock.patch.object(iptables_manager.IptablesTable, 'add_rule')
    @mock.patch.object(l3router.RouterInfo, 'add_floating_ip')
    def test_add_router_with_internal_cidr_overlaps(self, mock_add_fip,
                                      mock_add_rule,
                                      mock_send_fip_status):
        mock_add_fip.return_value = lib_const.FLOATINGIP_STATUS_ACTIVE

        elastic_snat_1 = elastic_snat_obj.ElasticSnat(
            context=None, id=_uuid(), name="elastic_snat_1",
            router_id=self.router_id,
            gateway_port_id=self.gateway_port_id,
            floatingip_id=self.floatingip_1.id,
            subnets=[self.subnet_id_1, self.subnet_id_2],
            internal_cidrs=['*******/24', '*******/30'],
            floating_ip_address=self.floatingip_1.floating_ip_address)

        elastic_snat_2 = elastic_snat_obj.ElasticSnat(
            context=None, id=_uuid(), name="elastic_snat_2",
            router_id=self.router_id,
            gateway_port_id=self.gateway_port_id,
            floatingip_id=self.floatingip_2.id,
            subnets=[self.subnet_id_1, self.subnet_id_2],
            internal_cidrs=['*******/27', '*******'],
            floating_ip_address=self.floatingip_2.floating_ip_address)

        router = {'id': self.router_id,
                  'gw_port': self.ex_gw_port,
                  'ha': False,
                  'distributed': False,
                  '_elastic_snat_floatingips': [self.fip2],
                  '_elastic_snat_rules': [elastic_snat_1,
                                          elastic_snat_2]}
        router_info = l3router.RouterInfo(
            self.agent, self.router_id, router,
            **self.ri_kwargs)
        self.agent.router_info[self.router_id] = router_info
        self.elastic_snat_ext.update_router(self.context, router)
        mock_add_rule.assert_has_calls(
            [mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s ******* '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_2.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/30 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_1.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/27 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_2.floating_ip_address}),
             mock.call(elastic_snat.DEFAULT_ELASTIC_SNAT_CHAIN,
                       '-s *******/24 '
                       '-j SNAT --to-source %(fip)s' % {
                           "fip": self.floatingip_1.floating_ip_address})])

        fip1 = str(self.floatingip_1.floating_ip_address)
        fip2 = str(self.floatingip_2.floating_ip_address)
        mock_add_fip.assert_has_calls(
            [mock.call({'floating_ip_address': fip1}, mock.ANY, mock.ANY),
             mock.call({'floating_ip_address': fip2}, mock.ANY, mock.ANY)])
        fip_status = {
            self.floatingip_1.id: lib_const.FLOATINGIP_STATUS_ACTIVE,
            self.floatingip_2.id: lib_const.FLOATINGIP_STATUS_ACTIVE}
        mock_send_fip_status.assert_called_once_with(mock.ANY, fip_status)
