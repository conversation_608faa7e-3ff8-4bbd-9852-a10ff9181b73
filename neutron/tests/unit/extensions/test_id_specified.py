#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from neutron_lib import constants as lib_consts
from neutron_lib import context
from neutron_lib import exceptions as lib_exc
from neutron_lib.plugins import constants as plugins_constants
from neutron_lib.plugins import directory
from oslo_utils import uuidutils

from neutron.db import _model_query as model_query
from neutron.db import db_base_plugin_common
from neutron.db import db_base_plugin_v2
from neutron.db.qos import models
from neutron.objects.qos import policy
from neutron.objects.qos import rule
from neutron.tests.unit.db import test_db_base_plugin_v2

_uuid = uuidutils.generate_uuid

DB_PLUGIN = 'neutron.tests.unit.extensions.test_id_specified.' \
            'IdSpecifiedTestPlugin'


class IdSpecifiedTestPlugin(db_base_plugin_v2.NeutronDbPluginV2):
    supported_extension_aliases = ["id-specified"]


class IdSpecifiedExtensionTestCase(
    test_db_base_plugin_v2.NeutronDbPluginV2TestCase):

    def setUp(self, plugin=DB_PLUGIN):
        svc_plugins = ('neutron.services.qos.qos_plugin.QoSPlugin',)
        super(IdSpecifiedExtensionTestCase, self).setUp(
            plugin=plugin, service_plugins=svc_plugins)
        self.qos_plugin = directory.get_plugin(plugins_constants.QOS)
        self.ctxt = context.Context('fake_user', 'fake_tenant')
        self.policy_data = {
            'policy': {'id': uuidutils.generate_uuid(),
                       'project_id': uuidutils.generate_uuid(),
                       'name': 'test-policy',
                       'shared': True,
                       'rules': []}}
        self.policy = policy.QosPolicy(
            self.ctxt, **self.policy_data['policy'])

    def test_create_network_without_id(self):
        res = self._create_network(self.fmt, 'net', True)
        self.assertEqual(201, res.status_int)

    def test_create_network_with_non_exist_id(self):
        uuid = uuidutils.generate_uuid()
        params = {'id': uuid,
                  'arg_list': ('id',)}
        with self.network() as net:
            res = self._show('networks', net['network']['id'])
            self.assertNotEqual(uuid, res['network']['id'])
            with self.network(**params) as net1:
                res1 = self._show('networks', net1['network']['id'])
                self.assertEqual(uuid, res1['network']['id'])

    def test_create_network_with_exist_id(self):
        with self.network() as net:
            network_id = net['network']['id']
            params = {'id': network_id,
                      'arg_list': ('id',)}
            res = self._create_network(self.fmt, 'net', True, **params)
            self.assertEqual(400, res.status_int)

    def test_create_network_with_not_valid_id(self):
        uuid = "96235fef"
        params = {'id': uuid,
                  'arg_list': ('id',)}
        res = self._create_network(self.fmt, 'net', True, **params)
        self.assertEqual(400, res.status_int)

    def test_create_subnet_without_id(self):
        with self.network() as net:
            res = self._create_subnet(
                self.fmt, net['network']['id'], '10.1.0.0/24')
            self.assertEqual(201, res.status_int)

    def test_create_subnet_with_non_exist_id(self):
        uuid = uuidutils.generate_uuid()
        with self.subnet() as sub:
            network_id = sub['subnet']['network_id']
            res = self._create_subnet(
                self.fmt, network_id, '10.1.0.0/24', id=uuid)
            sub1 = self.deserialize(self.fmt, res)
            self.assertEqual(uuid, sub1['subnet']['id'])

    def test_create_subnet_with_exist_id(self):
        with self.subnet() as sub:
            network_id = sub['subnet']['network_id']
            subnet_id = sub['subnet']['id']
            res = self._create_subnet(
                self.fmt, network_id, '10.1.0.0/24', id=subnet_id)
            self.assertEqual(400, res.status_int)

    def test_create_subnet_with_not_valid_id(self):
        uuid = "96235fef"
        with self.subnet() as sub:
            network_id = sub['subnet']['network_id']
            res = self._create_subnet(
                self.fmt, network_id, '10.1.0.0/24', id=uuid)
            self.assertEqual(400, res.status_int)

    def test_create_port_without_id(self):
        with self.network() as net:
            network_id = net['network']['id']
            res = self._create_port(self.fmt, net_id=network_id)
            self.assertEqual(201, res.status_int)

    def test_create_port_with_non_exist_id(self):
        uuid = uuidutils.generate_uuid()
        params = {'id': uuid,
                  'arg_list': ('id',)}
        with self.port() as port:
            res = self._show('ports', port['port']['id'])
            self.assertNotEqual(uuid, res['port']['id'])
            with self.port(**params) as port1:
                res1 = self._show('ports', port1['port']['id'])
                self.assertEqual(uuid, res1['port']['id'])

    def test_create_port_with_exist_id(self):
        with self.network() as net:
            network_id = net['network']['id']
            res = self._create_port(self.fmt, net_id=network_id)
            port1 = self.deserialize(self.fmt, res)
            self.assertEqual(201, res.status_int)
            params = {'id': port1['port']['id'],
                      'arg_list': ('id',)}
            res = self._create_port(self.fmt, net_id=network_id, **params)
            self.assertEqual(400, res.status_int)

    def test_create_port_with_not_valid_id(self):
        uuid = "96235fef"
        params = {'id': uuid,
                  'arg_list': ('id',)}
        with self.network() as net:
            network_id = net['network']['id']
            res = self._create_port(self.fmt, net_id=network_id, **params)
            self.assertEqual(400, res.status_int)

    def test_create_qos_policy_without_id_and_gen_uuid(self):
        policy1 = {'policy': {'name': 'test-policy',
                              'id': lib_consts.ATTR_NOT_SPECIFIED}}
        self.qos_plugin.create_policy(self.ctxt, policy1)
        assert uuidutils.is_uuid_like(policy1['policy']['id'])

    def test_create_qos_policy_with_non_exist_id(self):
        policy1 = {'policy': {'name': 'test-policy',
                              'id': _uuid()}}
        policy_obj = policy.QosPolicy(
            self.ctxt, **policy1['policy'])
        with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                        return_value=policy_obj):
            self.qos_plugin.create_policy(self.ctxt, policy1)
        self.assertEqual(policy_obj.name, "test-policy")

        policy2 = {'policy': {'name': 'test-policy2',
                              'id': _uuid()}}
        policy2_obj = policy.QosPolicy(
            self.ctxt, **policy2['policy'])
        with mock.patch.object(
                db_base_plugin_common.DbBasePluginCommon,
                "_validate_if_uuid_used",
                return_value=False) as validate_uuid:
            with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                            return_value=policy2_obj):
                self.qos_plugin.create_policy(self.ctxt, policy2)
                validate_uuid.assert_called()
        self.assertEqual(policy2_obj.id, policy2['policy']['id'])

    def test_create_qos_policy_with_exist_id(self):
        policy1 = {'policy': {'name': 'test-policy',
                              'id': _uuid()}}
        policy_obj = policy.QosPolicy(
            self.ctxt, **policy1['policy'])
        with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                        return_value=policy_obj):
            self.qos_plugin.create_policy(self.ctxt, policy1)
        self.assertEqual(policy_obj.name, "test-policy")

        policy2 = {'policy': {'name': 'test-policy2',
                              'id': policy_obj.id}}
        with mock.patch.object(model_query, 'get_by_id',
                               return_value=models.QosPolicy(
                                   id=policy_obj.id)):
            self.assertRaises(lib_exc.BadRequest,
                              self.qos_plugin.create_policy,
                              self.ctxt, policy2)

    def test_create_qos_policy_with_not_valid_id(self):
        policy1 = {'policy': {'name': 'test-policy1', 'id': '1234567'}}
        with mock.patch.object(self.qos_plugin, "create_policy",
                               return_value=models.QosPolicy(
                                   id=policy1['policy']['id'])) as create_qos:
            create_qos.assert_not_called()

    def test_create_qos_bandwidth_limit_rule_without_id_and_gen_uuid(self):
        mock.patch('neutron.objects.db.api.create_object').start()
        mock.patch(
            'neutron.objects.qos.policy.QosPolicy.obj_load_attr').start()
        mock.patch(
            'neutron.objects.base.NeutronDbObject.modify_fields_from_db'
        ).start()
        bandwidth_rule = {'bandwidth_limit_rule': {
            'id': lib_consts.ATTR_NOT_SPECIFIED, 'max_kbps': 100,
            'max_burst_kbps': 150}}
        with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                        return_value=self.policy):
            self.qos_plugin.create_policy_bandwidth_limit_rule(
                self.ctxt, self.policy.id, bandwidth_rule)
        assert uuidutils.is_uuid_like(
            bandwidth_rule['bandwidth_limit_rule']['id'])

    def test_create_qos_bandwidth_limit_rule_with_non_exist_id(self):
        mock.patch('neutron.objects.db.api.create_object').start()
        mock.patch(
            'neutron.objects.qos.policy.QosPolicy.obj_load_attr').start()
        mock.patch(
            'neutron.objects.base.NeutronDbObject.modify_fields_from_db'
        ).start()
        bandwidth_rule1 = {'bandwidth_limit_rule': {
            'id': _uuid(), 'max_kbps': 100, 'max_burst_kbps': 150}}
        bandwidth_rule_obj = rule.QosBandwidthLimitRule(
            self.ctxt, **bandwidth_rule1['bandwidth_limit_rule'])
        with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                        return_value=self.policy):
            with mock.patch('neutron.objects.qos.rule.QosBandwidthLimitRule.'
                            'get_object', return_value=bandwidth_rule_obj):
                with mock.patch.object(
                        db_base_plugin_common.DbBasePluginCommon,
                        "_validate_if_uuid_used",
                        return_value=False) as validate_uuid:
                    setattr(self.policy, "rules", [bandwidth_rule_obj])
                    self.qos_plugin.create_policy_bandwidth_limit_rule(
                        self.ctxt, self.policy.id, bandwidth_rule1)
                    validate_uuid.assert_called()
        rules = self.policy.rules
        self.assertEqual(bandwidth_rule_obj.id, rules[0].id)

    def test_create_qos_bandwidth_limit_rule_with_exist_id(self):
        bandwidth_rule1 = {'bandwidth_limit_rule': {
            'id': _uuid(), 'max_kbps': 100, 'max_burst_kbps': 150}}
        bandwidth_rule_obj = rule.QosBandwidthLimitRule(
            self.ctxt, **bandwidth_rule1['bandwidth_limit_rule'])
        bandwidth_rule2 = {'bandwidth_limit_rule': {
            'id': bandwidth_rule_obj.id, 'max_kbps': 100,
            'max_burst_kbps': 150}}
        setattr(self.policy, "rules", [bandwidth_rule_obj])
        with mock.patch.object(model_query, 'get_by_id',
                               return_value=models.QosBandwidthLimitRule(
                                   id=bandwidth_rule_obj.id)):
            with mock.patch.object(
                    db_base_plugin_common.DbBasePluginCommon,
                    "_validate_if_uuid_used",
                    return_value=True) as validate_uuid:
                self.assertRaises(
                    lib_exc.BadRequest,
                    self.qos_plugin.create_policy_bandwidth_limit_rule,
                    self.ctxt, self.policy.id, bandwidth_rule2)
                validate_uuid.assert_called()

    def test_create_qos_bandwidth_limit_rule_with_not_valid_id(self):
        bandwidth_rule1 = {'bandwidth_limit_rule': {
            'id': "1234567", 'max_kbps': 100, 'max_burst_kbps': 150}}
        with mock.patch.object(
                self.qos_plugin, "create_policy_rule",
                return_value=models.QosBandwidthLimitRule(
                    id=bandwidth_rule1['bandwidth_limit_rule']['id'])) as \
                create_rule:
            create_rule.assert_not_called()

    def test_create_qos_packet_rate_limit_rule_without_id_and_gen_uuid(self):
        mock.patch('neutron.objects.db.api.create_object').start()
        mock.patch(
            'neutron.objects.qos.policy.QosPolicy.obj_load_attr').start()
        mock.patch(
            'neutron.objects.base.NeutronDbObject.modify_fields_from_db'
        ).start()
        packet_rate_limit = {'packet_rate_limit_rule': {
            'id': lib_consts.ATTR_NOT_SPECIFIED, 'max_kpps': 200,
            'max_burst_kpps': 300}}
        with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                        return_value=self.policy):
            self.qos_plugin.create_policy_packet_rate_limit_rule(
                self.ctxt, self.policy.id, packet_rate_limit)
        assert uuidutils.is_uuid_like(
            packet_rate_limit['packet_rate_limit_rule']['id'])

    def test_create_qos_packet_rate_limit_rule_with_non_exist_id(self):
        mock.patch('neutron.objects.db.api.create_object').start()
        mock.patch(
            'neutron.objects.qos.policy.QosPolicy.obj_load_attr').start()
        mock.patch(
            'neutron.objects.base.NeutronDbObject.modify_fields_from_db'
        ).start()
        pps_rule1 = {'packet_rate_limit_rule': {
            'id': _uuid(), 'max_kpps': 200, 'max_burst_kpps': 300}}
        pps_rule_obj = rule.QosPacketRateLimitRule(
            self.ctxt, **pps_rule1['packet_rate_limit_rule'])
        with mock.patch('neutron.objects.qos.policy.QosPolicy.get_object',
                        return_value=self.policy):
            with mock.patch('neutron.objects.qos.rule.QosPacketRateLimitRule.'
                            'get_object', return_value=pps_rule_obj):
                with mock.patch.object(
                        db_base_plugin_common.DbBasePluginCommon,
                        "_validate_if_uuid_used",
                        return_value=False) as validate_uuid:
                    setattr(self.policy, "rules", [pps_rule_obj])
                    self.qos_plugin.create_policy_packet_rate_limit_rule(
                        self.ctxt, self.policy.id, pps_rule1)
                    validate_uuid.assert_called()
        rules = self.policy.rules
        self.assertEqual(pps_rule_obj.id, rules[0].id)

    def test_create_qos_packet_rate_limit_rule_with_exist_id(self):
        pps_rule1 = {'packet_rate_limit_rule': {
            'id': _uuid(), 'max_kpps': 100, 'max_burst_kpps': 150}}
        pps_rule_obj = rule.QosPacketRateLimitRule(
            self.ctxt, **pps_rule1['packet_rate_limit_rule'])
        pps_rule2 = {'packet_rate_limit_rule': {
            'id': pps_rule_obj.id, 'max_kpps': 100,
            'max_burst_kpps': 150}}
        setattr(self.policy, "rules", [pps_rule_obj])
        with mock.patch.object(model_query, 'get_by_id',
                               return_value=models.QosPacketRateLimitRule(
                                   id=pps_rule_obj.id)):
            with mock.patch.object(db_base_plugin_common.DbBasePluginCommon,
                                   "_validate_if_uuid_used",
                                   return_value=True) as validate_uuid:
                self.assertRaises(
                    lib_exc.BadRequest,
                    self.qos_plugin.create_policy_packet_rate_limit_rule,
                    self.ctxt, self.policy.id, pps_rule2)
                validate_uuid.assert_called()

    def test_create_qos_packet_rate_limit_rule_with_not_valid_id(self):
        pps_rule1 = {'packet_rate_limit_rule': {
            'id': "1234567", 'max_kpps': 100, 'max_burst_kpps': 150}}
        with mock.patch.object(
                self.qos_plugin, "create_policy_rule",
                return_value=models.QosPacketRateLimitRule(
                    id=pps_rule1['packet_rate_limit_rule']['id'])) as \
                create_rule:
            create_rule.assert_not_called()
