# Copyright (c) 2013 OpenStack Foundation.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import webob.exc

from neutron.api import extensions
from neutron.db import db_base_plugin_v2
from neutron.db import port_cloud_attrs_db
from neutron.extensions import _port_cloud_attributes_lib as pca_ext
from neutron.tests.unit.api import test_extensions
from neutron.tests.unit.db import test_db_base_plugin_v2

DB_PLUGIN = (
    'neutron.tests.unit.extensions.test_port_cloud_attributes.'
    'PortCloudAttrsTestPlugin')


class PortCloudAttrsTestPlugin(db_base_plugin_v2.NeutronDbPluginV2,
                               port_cloud_attrs_db.PortCloudAttributesDbMixin):

    supported_extension_aliases = ["port-cloud-attributes"]

    def create_port(self, context, port):
        with context.session.begin(subtransactions=True):
            port_cloud_attrs = port['port'].get(
                pca_ext.COLLECTION_NAME, {})
            new_port = super(PortCloudAttrsTestPlugin, self).create_port(
                context, port)
            self._process_port_create_port_cloud_attrs(
                context, new_port, port_cloud_attrs)
        return new_port

    def update_port(self, context, id, port):
        with context.session.begin(subtransactions=True):
            new_port = super(PortCloudAttrsTestPlugin, self).update_port(
                context, id, port)
            self._process_port_update_port_cloud_attrs(
                context, id, port, new_port)
        return new_port

    def delete_attrs(self, context, id, remove_attrs):
        with context.session.begin(subtransactions=True):
            super(PortCloudAttrsTestPlugin, self).delete_attrs(
                context, id)
            self.remove_port_cloud_attributes(context, id, remove_attrs)


class PortCloudAttributesDBTestCase(
    test_db_base_plugin_v2.NeutronDbPluginV2TestCase):

    def setUp(self, plugin=DB_PLUGIN):
        super(PortCloudAttributesDBTestCase, self).setUp(plugin=plugin)
        ext_mgr = extensions.PluginAwareExtensionManager.get_instance()
        self.ext_api = test_extensions.setup_extensions_middleware(ext_mgr)


class TestPortCloudAttributes(PortCloudAttributesDBTestCase):

    def _check_opts(self, expected, returned):
        for key, value in returned.items():
            if key in expected.keys() and value == expected[key]:
                break
            self.assertEqual(value, expected[key])

    def _create_port_check_if_equal(self, attrs):
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            self._check_opts(
                attrs, port['port'][pca_ext.COLLECTION_NAME])

    def test_create_port_with_cloud_attrs_str(self):
        attrs = {'key1': 'value1',
                 'key2': 'value2'}
        self._create_port_check_if_equal(attrs)

    def test_create_port_with_cloud_attrs_list(self):
        attrs = {'key1': ['value1', 'value2']}
        self._create_port_check_if_equal(attrs)

    def test_create_port_with_cloud_attrs_dict(self):
        attrs = {'key1': {"subkey1": "subvalue1"}}
        self._create_port_check_if_equal(attrs)

    def test_create_port_with_cloud_attrs_list_based_dict(self):
        attrs = {'key1': [{"subkey1": "subvalue1"}, {"subkey2": "subvalue2"},
                          {"subkey3": "subvalue3"}]}
        self._create_port_check_if_equal(attrs)

    def test_create_port_without_cloud_attrs(self):
        attrs = {}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            cur_port = self._show('ports', port['port']['id'])['port']
            self.assertEqual(attrs, cur_port[pca_ext.COLLECTION_NAME])

    def _extend_port_check_if_equal(self, attrs, updated_attrs,
                                    expected_attrs):
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            update_port = {pca_ext.COLLECTION_NAME: updated_attrs}

            req = self.new_update_request('ports', update_port,
                                          port['port']['id'],
                                          subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPOk.code, res.status_int)
            cloud_attrs = self.deserialize('json', res)
            self._check_opts(expected_attrs,
                             cloud_attrs[pca_ext.COLLECTION_NAME])

    def _update_port_check_if_equal(self, attrs, updated_attrs,
                                    expected_attrs):
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            update_port = {'port': {pca_ext.COLLECTION_NAME: updated_attrs}}

            req = self.new_update_request('ports', update_port,
                                          port['port']['id'])
            res = req.get_response(self.api)
            self.assertEqual(webob.exc.HTTPOk.code, res.status_int)
            port = self.deserialize('json', res)
            self._check_opts(expected_attrs,
                             port['port'][pca_ext.COLLECTION_NAME])

    def test_extend_cloud_attrs_when_key_non_exist(self):
        attrs = {'key1': 'value1'}
        updated_attrs = {'key2': 'value2', "key3": [{
            "subkey1": "subvalue1"}, {"subkey2": "subvalue2"}]}
        expected_attrs = {'key1': 'value1', 'key2': 'value2', "key3": [{
            "subkey1": "subvalue1"}, {"subkey2": "subvalue2"}]}
        self._extend_port_check_if_equal(attrs, updated_attrs, expected_attrs)

    def test_extend_cloud_attrs_when_key_exist_value_non_exist(self):
        attrs = {'key1': ['value1'], 'key2': [{"subkey2": "subvalue2"}]}
        updated_attrs = {'key1': 'value2',
                         'key2': {"subkey4": "subvalue4"}}
        expected_attrs = {'key1': ['value1', 'value2'], 'key2': [{
            "subkey2": "subvalue2"}, {"subkey4": "subvalue4"}]}
        self._extend_port_check_if_equal(attrs, updated_attrs, expected_attrs)

    def test_extend_cloud_attrs_when_key_exist_value_exist(self):
        attrs = {'key1': ['value1', 'value2'],
                 'key2': [{"subkey1": "subvalue1"}, {"subkey2": "subvalue2"}]}
        updated_attrs = {'key1': 'value1',
                         'key2': {"subkey2": "subvalue2"}}
        expected_attrs = {'key1': ['value1', 'value2'],
                          'key2': [{"subkey1": "subvalue1"},
                                   {"subkey2": "subvalue2"}]}
        self._extend_port_check_if_equal(attrs, updated_attrs, expected_attrs)

    def test_update_port_cloud_attrs_when_key_exist_value_exist(self):
        attrs = {'key1': 'value1', 'key2': {"subkey2": "subvalue2"}}
        updated_attrs = {'key1': 'value1',
                         'key2': {"subkey2": "subvalue2"}}
        expected_attrs = {'key1': 'value1',
                          'key2': {"subkey2": "subvalue2"}}
        self._update_port_check_if_equal(attrs, updated_attrs, expected_attrs)

    def test_update_port_cloud_attrs_when_key_non_exist(self):
        attrs = {'key1': ['value1']}
        updated_attrs = {'key2': ['value2'], "key3": [{
            "subkey1": "subvalue1"}, {"subkey2": "subvalue2"}]}
        expected_attrs = {'key1': ['value1'], 'key2': ['value2'], "key3": [{
            "subkey1": "subvalue1"}, {"subkey2": "subvalue2"}]}
        self._update_port_check_if_equal(attrs, updated_attrs, expected_attrs)

    def test_update_port_cloud_attrs_when_key_exist_value_non_exist(self):
        attrs = {'key1': ['value1'], 'key2': [{"subkey2": "subvalue2"}]}
        updated_attrs = {'key1': ['value2'],
                         'key2': [{"subkey3": "subvalue3"}]}
        expected_attrs = {'key1': ['value2'],
                          'key2': [{"subkey3": "subvalue3"}]}
        self._update_port_check_if_equal(attrs, updated_attrs, expected_attrs)

    def test_get_port_all_cloud_attrs(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            req = self.new_show_request('ports', port['port']['id'],
                                        subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPOk.code, res.status_int)
            extra_attrs = self.deserialize('json', res)
            self._check_opts(attrs,
                             extra_attrs[pca_ext.COLLECTION_NAME])

    def test_get_port_one_exist_cloud_attrs(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            req = self.new_show_request('ports', port['port']['id'],
                                        subresource=pca_ext.COLLECTION_NAME,
                                        sub_id='key1')
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPOk.code, res.status_int)
            cloud_attrs = self.deserialize('json', res)
            self._check_opts({'key1': ['value1']},
                             cloud_attrs)

    def test_get_port_one_non_exist_cloud_attrs(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            req = self.new_show_request('ports', port['port']['id'],
                                        subresource=pca_ext.COLLECTION_NAME,
                                        sub_id='key4')
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNotFound.code, res.status_int)

    def _delete_port_cloud_attrs_check_if_equal(self, attrs, deleted_attrs):
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            delete_port = {pca_ext.COLLECTION_NAME: deleted_attrs}
            req = self.new_delete_request('ports', port['port']['id'],
                                          data=delete_port,
                                          subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNoContent.code, res.status_int)
            cur_port = self._show('ports', port['port']['id'])['port']
            self.assertNotIn(deleted_attrs.keys(),
                             cur_port[pca_ext.COLLECTION_NAME].keys())

    def test_delete_cloud_attrs_key_exist_and_str_value_exist(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        deleted_attrs = {'key1': 'value1'}
        self._delete_port_cloud_attrs_check_if_equal(attrs, deleted_attrs)

    def test_delete_cloud_attrs_key_exist_and_str_value_non_exist(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        deleted_attrs = {'key4': 'value1'}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            delete_port = {pca_ext.COLLECTION_NAME: deleted_attrs}
            req = self.new_delete_request('ports', port['port']['id'],
                                          data=delete_port,
                                          subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNotFound.code, res.status_int)

    def test_delete_cloud_attrs_key_exist_and_dict_value_exist(self):
        attrs = {"key2": ["value2"], "key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}]}
        deleted_attrs = {"key1": {"subkey1": "subvalue1"}}
        self._delete_port_cloud_attrs_check_if_equal(attrs, deleted_attrs)

    def test_delete_cloud_attrs_key_exist_and_dict_value_non_exist(self):
        attrs = {"key2": ["value2"], "key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}]}
        deleted_attrs = {"key1": {"subkey3": "subvalue3"}}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            delete_port = {pca_ext.COLLECTION_NAME: deleted_attrs}
            req = self.new_delete_request('ports', port['port']['id'],
                                          data=delete_port,
                                          subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNotFound.code, res.status_int)

    def test_delete_cloud_attrs_key_exist_and_all_value_exist(self):
        attrs = {"key2": ["value2"], "key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}, {"subkey3": "subvalue3"}]}
        deleted_attrs = {"key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}, {"subkey3": "subvalue3"}]}
        self._delete_port_cloud_attrs_check_if_equal(attrs, deleted_attrs)

    def test_delete_cloud_attrs_key_exist_and_part_value_exist(self):
        attrs = {"key2": ["value2"], "key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}, {"subkey3": "subvalue3"}]}
        deleted_attrs = {"key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}, {"subkey5": "subvalue5"}]}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            delete_port = {pca_ext.COLLECTION_NAME: deleted_attrs}
            req = self.new_delete_request('ports', port['port']['id'],
                                          data=delete_port,
                                          subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNotFound.code, res.status_int)

    def test_delete_all_cloud_attrs(self):
        attrs = {"key2": ["value2"], "key1": [{"subkey1": "subvalue1"}, {
            "subkey2": "subvalue2"}, {"subkey3": "subvalue3"}]}
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            req = self.new_delete_request('ports', port['port']['id'],
                                          subresource=pca_ext.COLLECTION_NAME)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNoContent.code, res.status_int)
            cur_port = self._show('ports', port['port']['id'])['port']
            self.assertEqual({}, cur_port[pca_ext.COLLECTION_NAME])

    def test_delete_port_cloud_attrs_specify_exist_key(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        deleted_attrs_keys = 'key1'
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            req = self.new_delete_request('ports', port['port']['id'],
                                          subresource=pca_ext.COLLECTION_NAME,
                                          sub_id=deleted_attrs_keys)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNoContent.code, res.status_int)
            cur_port = self._show('ports', port['port']['id'])['port']
            self.assertNotIn(deleted_attrs_keys,
                             cur_port[pca_ext.COLLECTION_NAME].keys())

    def test_delete_port_cloud_attrs_specify_non_exist_key(self):
        attrs = {'key1': ['value1'],
                 'key2': ['value2']}
        deleted_attrs_keys = 'key3'
        params = {pca_ext.COLLECTION_NAME: attrs,
                  'arg_list': (pca_ext.COLLECTION_NAME,)}
        with self.port(**params) as port:
            req = self.new_delete_request('ports', port['port']['id'],
                                          subresource=pca_ext.COLLECTION_NAME,
                                          sub_id=deleted_attrs_keys)
            res = req.get_response(self.ext_api)
            self.assertEqual(webob.exc.HTTPNotFound.code, res.status_int)
