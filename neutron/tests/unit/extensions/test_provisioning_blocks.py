# Copyright 2015 Rackspace
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.callbacks import resources
from neutron_lib import context
from neutron_lib import exceptions as n_exc

from neutron.db import db_base_plugin_v2
from neutron.db import provisioning_blocks
from neutron.extensions import provisioning_blocks as pb_ext
from neutron.tests.unit.db import test_db_base_plugin_v2


class ProvisioningBlocksExtensionManager(object):

    def get_resources(self):
        return []

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []

    def get_extended_resources(self, version):
        extension = pb_ext.Provisioning_blocks()
        return extension.get_extended_resources(version)


class ProvisioningBlocksExtensionTestPlugin(
    db_base_plugin_v2.NeutronDbPluginV2,
    provisioning_blocks.ProvisioningBlocksMixin):
    """Test plugin to mixin the provisioning-blocks extensions.
    """

    supported_extension_aliases = ["provisioning-blocks"]


class ProvisioningBlocksTestCase(
    test_db_base_plugin_v2.NeutronDbPluginV2TestCase):
    """Test API extension provisioning-blocks.
    """

    def setUp(self):
        plugin = ('neutron.tests.unit.'
                  'extensions.test_provisioning_blocks.'
                  'ProvisioningBlocksExtensionTestPlugin')
        ext_mgr = ProvisioningBlocksExtensionManager()
        super(ProvisioningBlocksTestCase, self).setUp(
            plugin=plugin, ext_mgr=ext_mgr)

    def test_get_provisioning_blocks(self):
        with self.port() as port:
            port_id = port['port']['id']
            ctx = context.get_admin_context()
            provisioning_blocks.add_provisioning_component(
                ctx, port_id, resources.PORT, 'test')
            res = self.plugin.get_provisioning_blocks(ctx, port_id)
            pb = {'entity': u'test', 'standard_attr_id': 3}
            self.assertEqual([pb], res)

    def test_get_provisioning_blocks_invalid_id(self):
        invalid_id = 'invalid_id'
        ctx = context.get_admin_context()
        try:
            self.plugin.get_provisioning_blocks(ctx, invalid_id)
        except n_exc.PortNotFound as e:
            error_msg = e.msg
            self.assertEqual(n_exc.PortNotFound(
                port_id=invalid_id).msg, error_msg)
