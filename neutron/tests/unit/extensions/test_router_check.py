#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import context
from neutron_lib import exceptions as n_exc
from neutron_lib.exceptions import l3 as l3_exc
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_utils import uuidutils

from neutron.db import l3_hamode_db
from neutron.db import l3_hascheduler_db
from neutron.extensions import l3
from neutron.extensions import router_check as router_check_ext
from neutron.services.router_check import plugin
from neutron.tests.unit.extensions import test_l3

_uuid = uuidutils.generate_uuid


class RouterCheckTestPlugin(test_l3.TestL3NatIntPlugin,
                            plugin.RouterCheckPlugin,
                            l3_hamode_db.L3_HA_NAT_db_mixin,
                            l3_hascheduler_db.L3_HA_scheduler_db_mixin):
    supported_extension_aliases = ['router', 'router-check']


class RouterCheckTestExtensionManager(object):
    def get_resources(self):
        return (l3.L3.get_resources() +
                router_check_ext.Router_check.get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class RouterCheckTestCase(test_l3.L3BaseForIntTests,
                          test_l3.L3NatTestCaseMixin):
    def setUp(self):
        plugin = (
            'neutron.tests.unit.'
            'extensions.test_router_check.'
            'RouterCheckTestPlugin'
        )
        ext_mgr = RouterCheckTestExtensionManager()
        super(RouterCheckTestCase, self).setUp(
            plugin=plugin,
            ext_mgr=ext_mgr)
        self.rc_plugin = directory.get_plugin()
        self.rc_plugin.l3_plugin = directory.get_plugin(constants.L3)
        self.rc_plugin._l3_rpc_notifier = mock.Mock()
        self.ctx = context.get_admin_context()

    def test_router_check_without_router_and_host(self):
        self.assertRaises(n_exc.BadRequest,
                          self.rc_plugin.router_check, self.ctx)

    def test_router_check_with_router_id_and_host(self):
        self.assertRaises(n_exc.Conflict,
                          self.rc_plugin.router_check,
                          self.ctx, _uuid(), 'fake-host')

    def test_router_check_non_exist_router(self):
        self.assertRaises(l3_exc.RouterNotFound,
                          self.rc_plugin.router_check,
                          self.ctx, _uuid())

    def test_router_ns_info_non_exist_router(self):
        self.assertRaises(l3_exc.RouterNotFound,
                          self.rc_plugin.router_ns_info,
                          self.ctx, _uuid())

    def test_router_check_by_ha_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': [{'host': 'fake-host'}]}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        self.rc_plugin.l3_plugin.get_active_host_for_ha_router = (
            mock.Mock(return_value='fake-host'))
        check_result = ({router['id']: {
            "status": True, "ha_state_error": ''}}, [])
        self.rc_plugin.check_routers_ha_state = mock.Mock(
            return_value=check_result)
        result = {'router_check': [
            {'router_id': router['id'], 'check result': {}}]}
        self.rc_plugin._l3_rpc_notifier.routers_check = mock.Mock(
            return_value=result)
        self.rc_plugin.router_check(self.ctx, router['id'])
        (self.rc_plugin.
         l3_plugin.get_active_host_for_ha_router.assert_called())
        self.rc_plugin.check_routers_ha_state.assert_called()
        (self.rc_plugin.
         _l3_rpc_notifier.routers_check.assert_called())

    def test_router_check_by_traditional_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': False}
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': [{'host': 'fake-host'}]}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        check_result = ({router['id']: {
            "status": True, "ha_state_error": ''}}, [])
        self.rc_plugin.check_routers_ha_state = mock.Mock(
            return_value=check_result)
        result = {'router_check': [
            {'router_id': router['id'], 'check result': {}}]}
        self.rc_plugin._l3_rpc_notifier.routers_check = mock.Mock(
            return_value=result)
        self.rc_plugin.router_check(self.ctx, router['id'])
        (self.rc_plugin.
         l3_plugin.list_l3_agents_hosting_router.assert_called())
        self.rc_plugin.check_routers_ha_state.assert_called()
        (self.rc_plugin.
         _l3_rpc_notifier.routers_check.assert_called())

    def test_router_check_by_no_bound_host_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': False}
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': []}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        self.assertRaises(n_exc.BadRequest,
                          self.rc_plugin.router_check,
                          self.ctx, router['id'])

    def test_router_check_by_host(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        host = 'fake-host'
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': [{'host': 'fake-host'}]}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        self.rc_plugin._get_routers_by_host = mock.Mock(
            return_value=[router['id']])
        self.rc_plugin.l3_plugin.get_active_host_for_ha_router = (
            mock.Mock(return_value='fake-host'))
        check_result = ({router['id']: {
            "status": True, "ha_state_error": ''}}, [])
        self.rc_plugin.check_routers_ha_state = mock.Mock(
            return_value=check_result)
        result = {'router_check': [
            {'router_id': router['id'], 'check result': {}}]}
        self.rc_plugin._l3_rpc_notifier.routers_check = mock.Mock(
            return_value=result)
        self.rc_plugin.router_check(self.ctx, host=host)
        (self.rc_plugin.
         l3_plugin.get_active_host_for_ha_router.assert_not_called())
        self.rc_plugin.check_routers_ha_state.assert_called()
        (self.rc_plugin.
         _l3_rpc_notifier.routers_check.assert_called())

    def test_router_ns_info_by_ha_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': [{'host': 'fake-host'}]}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        self.rc_plugin.l3_plugin.get_active_host_for_ha_router = (
            mock.Mock(return_value='fake-host'))
        self.rc_plugin._l3_rpc_notifier.router_ns_info = mock.Mock()
        self.rc_plugin.router_ns_info(self.ctx, router['id'])
        (self.rc_plugin.
         l3_plugin.get_active_host_for_ha_router.assert_called())
        (self.rc_plugin.
         _l3_rpc_notifier.router_ns_info.assert_called())

    def test_router_ns_info_by_traditional_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': False}
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': [{'host': 'fake-host'}]}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        self.rc_plugin._l3_rpc_notifier.router_ns_info = mock.Mock()
        self.rc_plugin.router_ns_info(self.ctx, router['id'])
        (self.rc_plugin.l3_plugin.
         list_l3_agents_hosting_router.assert_called())
        (self.rc_plugin.
         _l3_rpc_notifier.router_ns_info.assert_called())

    def test_router_ns_info_by_no_bound_host_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': False}
        router = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})

        fake_agents = {'agents': []}
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=fake_agents))
        self.assertRaises(n_exc.BadRequest,
                          self.rc_plugin.router_ns_info,
                          self.ctx, router['id'])

    def test_check_routers_ha_state_by_traditional_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': False}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        fake_agents = {'agents': [{'host': 'fake-host'}]}
        expect_result = ({router.id: {"status": True,
                                      "ha_state_error": ''}},
                         [router.id])
        self._check_routers_ha_state(router, fake_agents,
                                     expect_result,
                                     active_host='fake-host')

    def test_check_routers_ha_state_by_one_active_ha_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        fake_agents = {'agents': [{'host': 'fake-host',
                                   'ha_state': 'active'}]}
        msg = 'No backup router.'
        expect_result = ({router.id: {"status": False,
                                      "ha_state_error": msg}},
                         [router.id])
        self._check_routers_ha_state(router, fake_agents,
                                     expect_result,
                                     active_host='fake-host')

    def test_check_routers_ha_state_by_one_other_ha_router(self):
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        fake_agents = {'agents': [{'host': 'fake-host',
                                   'ha_state': 'other'}]}
        msg = 'No master router.'
        expect_result = ({router.id: {"status": False,
                                      "ha_state_error": msg}},
                         [])
        self._check_routers_ha_state(router, fake_agents,
                                     expect_result)

    def test_check_routers_ha_state_active_standby(self):
        # Check ha_state with HA routers
        # which ha_states are active and standby.
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        agents = {'agents': [{'host': 'fake1-host',
                              'ha_state': 'active'},
                             {'host': 'fake2-host',
                              'ha_state': 'standby'}]}
        expect_result = ({router.id: {"status": True,
                                      "ha_state_error": ''}},
                         [router.id])
        self._check_routers_ha_state(router, agents,
                                     expect_result,
                                     active_host='fake1-host')

    def test_check_routers_ha_state_active_active(self):
        # Check ha_state with HA routers
        # which ha_states are active and standby.
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        fake_agents = {'agents': [{'host': 'fake1-host',
                                   'ha_state': 'active'},
                                  {'host': 'fake2-host',
                                   'ha_state': 'active'}]}
        msg = 'Dual master router.'
        expect_result = ({router.id: {"status": False,
                                      "ha_state_error": msg}},
                         [router.id])
        self._check_routers_ha_state(router, fake_agents,
                                     expect_result,
                                     active_host='fake2-host')

    def test_check_routers_ha_state_standby_standby(self):
        # Check ha_state with HA routers
        # which ha_states are standby and standby.
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        fake_agents = {'agents': [{'host': 'fake1-host',
                                   'ha_state': 'standby'},
                                  {'host': 'fake2-host',
                                   'ha_state': 'standby'}]}
        msg = 'Dual backup router.'
        expect_result = ({router.id: {"status": False,
                                      "ha_state_error": msg}},
                         [])
        self._check_routers_ha_state(router, fake_agents, expect_result)

    def test_check_routers_ha_state_unknown_state(self):
        # Check ha_state with HA routers
        # which ha_states are other and unknown.
        tenant_id = _uuid()
        router = {'name': 'router1',
                  'admin_state_up': True,
                  'tenant_id': tenant_id,
                  'ha': True}
        router_dict = self.rc_plugin.l3_plugin.create_router(
            self.ctx, {'router': router})
        router = self.rc_plugin.l3_plugin._get_router(
            self.ctx, router_dict['id'])

        fake_agents = {'agents': [{'host': 'fake1-host',
                                   'ha_state': 'other'},
                                  {'host': 'fake2-host',
                                   'ha_state': 'unknown'}]}
        msg = 'Unknown ha state.'
        expect_result = ({router.id: {"status": False,
                                      "ha_state_error": msg}},
                         [])
        self._check_routers_ha_state(router, fake_agents, expect_result)

    def _check_routers_ha_state(self, router, agents,
                                expect_result, active_host=None):
        self.rc_plugin.l3_plugin.list_l3_agents_hosting_router = (
            mock.Mock(return_value=agents))
        self.rc_plugin.l3_plugin.get_active_host_for_ha_router = (
            mock.Mock(return_value=active_host))

        result = self.rc_plugin.check_routers_ha_state(
            self.ctx, [router], active_host)
        self.assertEqual(2, len(result))
        self.assertEqual(result[0][router.id]['status'],
                         expect_result[0][router.id]['status'])
        self.assertEqual(result[0][router.id]['ha_state_error'],
                         expect_result[0][router.id]['ha_state_error'])
        self.assertEqual(result[1], expect_result[1])
