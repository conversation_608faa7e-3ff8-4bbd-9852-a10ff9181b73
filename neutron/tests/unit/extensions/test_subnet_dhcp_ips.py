#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib import constants
from oslo_config import cfg
import webob.exc

from neutron.db import db_base_plugin_v2
from neutron.db import subnet_dhcp_ips_db
from neutron.extensions import subnet_dhcp_ips
from neutron.tests.unit.db import test_db_base_plugin_v2


class SubnetDhcpIpsExtensionManager(object):

    def get_resources(self):
        return []

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []

    def get_extended_resources(self, version):
        extension = subnet_dhcp_ips.Subnet_dhcp_ips()
        return extension.get_extended_resources(version)


class SubnetDhcpIpsExtensionTestPlugin(
        db_base_plugin_v2.NeutronDbPluginV2,
        subnet_dhcp_ips_db.SubnetDhcpIPsMixin):
    """Test plugin to mixin the subnet dhcp ips extension.
    """

    supported_extension_aliases = ["subnet-dhcp-ips"]


class SubnetDhcpIpsExtensionTestCase(
         test_db_base_plugin_v2.NeutronDbPluginV2TestCase):
    """Test API extension subnet_dhcp_ips attributes.
    """
    CIDRS = ['10.0.0.0/8', '20.0.0.0/8', '30.0.0.0/8']
    IP_VERSION = 4

    def setUp(self):
        cfg.CONF.set_override('dhcp_agents_per_network', 2)

        plugin = ('neutron.tests.unit.extensions.test_subnet_dhcp_ips.'
                  'SubnetDhcpIpsExtensionTestPlugin')
        ext_mgr = SubnetDhcpIpsExtensionManager()
        super(SubnetDhcpIpsExtensionTestCase,
              self).setUp(plugin=plugin, ext_mgr=ext_mgr)

    def _create_dhcp_ips_subnet(self, dhcp_ips=None, gateway_ip=None,
                                cidr=None, network=None, **kwargs):
        if not network:
            with self.network() as network:
                pass
        network = network['network']
        if not cidr:
            cidr = self.CIDRS[0]
        args = {'net_id': network['id'],
                'tenant_id': network['tenant_id'],
                'cidr': cidr,
                'gateway_ip': gateway_ip or constants.ATTR_NOT_SPECIFIED,
                'ip_version': self.IP_VERSION,
                'enable_dhcp': True}
        if dhcp_ips is not None:
            args['dhcp_ips'] = dhcp_ips
        if kwargs:
            args.update(kwargs)
        return self._create_subnet(self.fmt, **args)

    def _create_dhcp_ips_subnet_and_ports(self, dhcp_ips=None,
                                          gateway_ip=None, **kwargs):
        res = self._create_dhcp_ips_subnet(dhcp_ips, gateway_ip, **kwargs)
        subnet = self.deserialize('json', res)['subnet']

        ports = []
        if not subnet['dhcp_ips']:
            for i, _ in enumerate(range(2), 2):
                ip = str(netaddr.IPNetwork(subnet['cidr']).network + i)
                fixed_ips = [{'ip_address': ip}]
                port = self._create_port(self.fmt,
                                         net_id=subnet['network_id'],
                                         tenant_id=subnet['tenant_id'],
                                         fixed_ips=fixed_ips,
                                         device_owner='network:dhcp')
                ports.append(port)
        else:
            for dhcp_ip in subnet['dhcp_ips'][:2]:
                fixed_ips = [{'ip_address': dhcp_ip}]
                port = self._create_port(self.fmt,
                                         net_id=subnet['network_id'],
                                         tenant_id=subnet['tenant_id'],
                                         fixed_ips=fixed_ips,
                                         device_owner='network:dhcp')
                ports.append(port)
        return res, ports

    def _test_create_subnet(self, dhcp_ips, gateway_ip=None,
                            expect_fail=False, **kwargs):
        res = self._create_dhcp_ips_subnet(dhcp_ips, gateway_ip, **kwargs)
        if expect_fail:
            self.assertEqual(webob.exc.HTTPClientError.code,
                             res.status_int)
        else:
            subnet = self.deserialize('json', res)
            subnet = subnet['subnet']
            self.assertEqual(len(dhcp_ips),
                             len(subnet['dhcp_ips']))
            for dhcp_ip in subnet['dhcp_ips']:
                self.assertIn(dhcp_ip['ip_address'], dhcp_ips)

    def _test_update_subnet(self, subnet, dhcp_ips=None, gateway_ip=None,
                            fail_code=None):
        data = {'subnet': {}}
        if dhcp_ips:
            data['subnet']['dhcp_ips'] = dhcp_ips
        if gateway_ip:
            data['subnet']['gateway_ip'] = gateway_ip
        req = self.new_update_request('subnets', data, subnet['id'])
        res = self.deserialize(self.fmt, req.get_response(self.api))
        if fail_code is not None:
            self.assertEqual(fail_code,
                             res['NeutronError']['type'])
        else:
            subnet = res['subnet']
            self.assertEqual(len(dhcp_ips),
                             len(subnet['dhcp_ips']))
            for dhcp_ip in subnet['dhcp_ips']:
                self.assertIn(dhcp_ip['ip_address'], dhcp_ips)

    def test_create_subnet_blank_dhcp(self):
        self._test_create_subnet([], expect_fail=True)

    def test_create_subnet_less_than_net_agents(self):
        self._test_create_subnet(['********'], expect_fail=True)

    def test_create_subnet_dhcp(self):
        self._test_create_subnet(['********', '********'])

    def test_create_subnet_gateway_conflict_with_dhcp(self):
        self._test_create_subnet(['********', '********'], '********',
                                 expect_fail=True)

    def test_create_subnet_default_gateway_conflict_with_dhcp(self):
        self._test_create_subnet(['********', '********'], expect_fail=True)

    def test_create_subnet_dhcp_not_in_cidr(self):
        self._test_create_subnet(['********', '********'], expect_fail=True)

    def test_create_subnet_invalid_dhcp(self):
        self._test_create_subnet(['********', '********'], expect_fail=True)
        self._test_create_subnet(['********', 'fc00::123'], expect_fail=True)
        self._test_create_subnet(['1', 2], expect_fail=True)

    def test_update_subnet_dhcp_zero_to_two_not_contain_old_ip(self):
        # assume there are default dhcp ip: x.x.x.2, x.x.x.3
        res, _ = self._create_dhcp_ips_subnet_and_ports()
        subnet = self.deserialize('json', res)['subnet']
        self._test_update_subnet(subnet, ['********', '********'],
                                 fail_code='InvalidInput')

    def test_update_subnet_dhcp_zero_to_two(self):
        # assume there are default dhcp ip: x.x.x.2, x.x.x.3
        res, _ = self._create_dhcp_ips_subnet_and_ports()
        subnet = self.deserialize('json', res)['subnet']
        self._test_update_subnet(subnet,
                                 ['********', '********',
                                  '********', '********'])

    def test_update_subnet_dhcp(self):
        dhcp_ips = ['********', '********']
        res, _ = self._create_dhcp_ips_subnet_and_ports(dhcp_ips)
        subnet = self.deserialize('json', res)['subnet']
        dhcp_ips.extend(['********', '********'])
        self._test_update_subnet(subnet, dhcp_ips)

    def test_update_subnet_dhcp_and_gateway(self):
        dhcp_ips = ['********', '********']
        gateway = '********'
        allocation_pools = [{'start': '********', 'end': '********00'}]
        res, _ = self._create_dhcp_ips_subnet_and_ports(
            dhcp_ips, gateway, allocation_pools=allocation_pools)
        subnet = self.deserialize('json', res)['subnet']
        dhcp_ips.extend(['********', '********'])
        self._test_update_subnet(subnet, dhcp_ips, '********54')

    def test_update_subnet_dhcp_and_gateway_conflict(self):
        dhcp_ips = ['********', '********']
        gateway = '********54'
        allocation_pools = [{'start': '********', 'end': '********00'}]
        res, _ = self._create_dhcp_ips_subnet_and_ports(
            dhcp_ips, '********', allocation_pools=allocation_pools)
        subnet = self.deserialize('json', res)['subnet']
        dhcp_ips.append(gateway)
        self._test_update_subnet(subnet, dhcp_ips, gateway,
                                 fail_code='InvalidInput')

    def test_update_subnet_dhcp_conflict_with_gateway(self):
        dhcp_ips = ['********', '********']
        gateway = '********'
        res, _ = self._create_dhcp_ips_subnet_and_ports(dhcp_ips, gateway)
        subnet = self.deserialize('json', res)['subnet']
        dhcp_ips.append(gateway)
        self._test_update_subnet(subnet, dhcp_ips, fail_code='InvalidInput')

    def test_update_subnet_gateway_conflict_with_dhcp(self):
        dhcp_ips = ['********', '********', '********54']
        allocation_pools = [{'start': '********', 'end': '********00'}]
        res, _ = self._create_dhcp_ips_subnet_and_ports(
            dhcp_ips, '********', allocation_pools=allocation_pools)
        subnet = self.deserialize('json', res)['subnet']
        self._test_update_subnet(subnet, gateway_ip='********54',
                                 fail_code='InvalidInput')

    def test_update_subnet_dhcp_add_using_ip(self):
        dhcp_ips = ['********', '********']
        res, _ = self._create_dhcp_ips_subnet_and_ports(dhcp_ips)
        subnet = self.deserialize('json', res)['subnet']
        port = self._create_port(self.fmt,
                                 net_id=subnet['network_id'],
                                 tenant_id=subnet['tenant_id'],
                                 device_owner='compute:fake')
        port = self.deserialize('json', port)['port']
        dhcp_ips.append(port['fixed_ips'][0]['ip_address'])
        self._test_update_subnet(subnet, dhcp_ips)
