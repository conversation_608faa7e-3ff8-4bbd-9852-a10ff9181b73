- hosts: primary
  tasks:

    - name: Copy files from {{ ansible_user_dir }}/workspace/ on node
      synchronize:
        src: '{{ ansible_user_dir }}/workspace/'
        dest: '{{ zuul.executor.log_root }}'
        mode: pull
        copy_links: true
        verify_host: true
        rsync_opts:
          - --include=/logs/**
          - --include=*/
          - --exclude=*
          - --prune-empty-dirs
