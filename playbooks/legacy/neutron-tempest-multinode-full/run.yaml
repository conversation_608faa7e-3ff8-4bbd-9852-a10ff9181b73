- hosts: primary
  name: Autoconverted job legacy-tempest-dsvm-neutron-multinode-full from old job
    gate-tempest-dsvm-neutron-multinode-full-ubuntu-xenial-nv
  tasks:

    - name: Ensure legacy workspace directory
      file:
        path: '{{ ansible_user_dir }}/workspace'
        state: directory

    - shell:
        cmd: |
          set -e
          set -x
          cat > clonemap.yaml << EOF
          clonemap:
            - name: openstack/devstack-gate
              dest: devstack-gate
          EOF
          /usr/zuul-env/bin/zuul-cloner -m clonemap.yaml --cache-dir /opt/git \
              https://opendev.org \
              openstack/devstack-gate
        executable: /bin/bash
        chdir: '{{ ansible_user_dir }}/workspace'
      environment: '{{ zuul | zuul_legacy_vars }}'

    - shell:
        cmd: |
          set -e
          set -x
          cat << 'EOF' >>"/tmp/dg-local.conf"
          [[local|localrc]]
          NOVA_VNC_ENABLED=true
          VNCSERVER_LISTEN=0.0.0.0
          VNCSERVER_PROXYCLIENT_ADDRESS=$HOST_IP

          EOF
        executable: /bin/bash
        chdir: '{{ ansible_user_dir }}/workspace'
      environment: '{{ zuul | zuul_legacy_vars }}'

    - shell:
        cmd: |
          set -e
          set -x
          export PYTHONUNBUFFERED=true
          export DEVSTACK_GATE_TEMPEST=1
          export DEVSTACK_GATE_NEUTRON=1
          export DEVSTACK_GATE_CONFIGDRIVE=0
          export DEVSTACK_GATE_TEMPEST_FULL=1
          export DEVSTACK_GATE_TLSPROXY=1
          # Default to non DVR
          export DEVSTACK_GATE_NEUTRON_DVR=0
          export BRANCH_OVERRIDE=default
          if [ "$BRANCH_OVERRIDE" != "default" ] ; then
              export OVERRIDE_ZUUL_BRANCH=$BRANCH_OVERRIDE
          fi
          export DEVSTACK_GATE_TOPOLOGY="multinode"

          cp devstack-gate/devstack-vm-gate-wrap.sh ./safe-devstack-vm-gate-wrap.sh
          ./safe-devstack-vm-gate-wrap.sh
        executable: /bin/bash
        chdir: '{{ ansible_user_dir }}/workspace'
      environment: '{{ zuul | zuul_legacy_vars }}'
