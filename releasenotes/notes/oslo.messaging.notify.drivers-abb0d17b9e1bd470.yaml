---
upgrade:
  - Obsolete ``oslo.messaging.notify.drivers`` entrypoints that were left
    in tree for backwards compatibility with pre-Icehouse releases have been
    removed. Those are ``neutron.openstack.common.notifier.log_notifier``,
    ``neutron.openstack.common.notifier.no_op_notifier``,
    ``neutron.openstack.common.notifier.test_notifier``,
    ``neutron.openstack.common.notifier.rpc_notifier2``,
    ``neutron.openstack.common.notifier.rpc_notifier``.
    Use values provided by ``oslo.messaging`` library to configure notification
    drivers.
