---
features:
  - Enable creation of VXLANs with different multicast addresses
    in linuxbridge agent allocated by VNI-address mappings.
    A new config option ``multicast_ranges`` was introduced.
other:
  - Example configuration of ``multicast_ranges`` in
    ml2_conf.ini under the ``[vxlan]`` config. section
    ``multicast_ranges = **********:10:90,**********:100:900``.
    For VNI between 10 and 90, the multicast address
    *********.10 will be used, and for 100 through 900
    ********** will be used. Other VNI values will get
    standard ``vxlan_group`` address. For more info see RFE
    https://bugs.launchpad.net/neutron/+bug/1579068
