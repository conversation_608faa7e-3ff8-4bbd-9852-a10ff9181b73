# The order of packages is significant, because pip processes them in the order
# of appearance. Changing the order has an impact on the overall integration
# process, which may cause wedges in the gate later.
hacking==1.1.0,<1.2.0 # Apache-2.0

bandit==1.4.0,<1.5.0 # Apache-2.0
coverage==4.5.1 # Apache-2.0
fixtures==3.0.0 # Apache-2.0/BSD
flake8-import-order==0.12 # LGPLv3
mock==2.0.0 # BSD
python-subunit==1.3.0 # Apache-2.0/BSD
testtools==2.3.0 # MIT
testresources==2.0.1 # Apache-2.0/BSD
testscenarios==0.5.0 # Apache-2.0/BSD
WebTest==2.0.30 # MIT
oslotest==3.6.0 # Apache-2.0
stestr==2.1.0 # Apache-2.0
reno==2.9.2  # Apache-2.0
ddt==1.2.0 # MIT
pylint==1.4.5 # GPLv2
# Needed to run DB commands in virtualenvs
PyMySQL==0.9.2 # MIT License
bashate==0.6.0 # Apache-2.0
