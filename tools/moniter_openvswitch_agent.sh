#!/bin/bash

function handle_error {
        echo -n "`date "+%Y-%m-%d %H:%M:%S"` "
        echo -e "\033[31m$1\033[0m"
}

PROCESS_NAME="neutron-openvswitch-agent"
REPORT_INTERVAL=120
LOG_FILE=/var/log/neutron/openvswitch-agent.log
TMP_LOF_FILE=/tmp/neutron-openvswitch-agent.log
OVS_VSWITCHD_CPU_USAGE=100

while true; do

        # check ovs-vswitchd cpu usage
        for usage in `top -b -n 1 | grep ovs-vswitchd |  awk '{print $9}' | cut -d '.' -f 1`; do
                [ $usage -ge $OVS_VSWITCHD_CPU_USAGE ] && handle_error "The usage of ovs-vswitchd is more than $OVS_VSWITCHD_CPU_USAGE !"
        done

        # check process status
        if `systemctl status $PROCESS_NAME &> /dev/null`; then

                sleep $REPORT_INTERVAL &
                SLEEP_PID=$!

                tail --lines 0 -f --pid $SLEEP_PID $LOG_FILE > $TMP_LOF_FILE

                ERROR_LINE=`grep ERROR $TMP_LOF_FILE| grep -v "Error received from \[ovsdb-client monitor tcp:127.0.0.1:6640" | wc -l`
                if [ $ERROR_LINE -gt 0 ]; then
                        handle_error "Some ERROR appeared，please check $LOG_FILE"
                fi

                grep "Agent rpc_loop" $TMP_LOF_FILE &> /dev/null ||
                handle_error "No rpc loop in $LOG_FILE, the $PROCESS_NAME may dead, ignore this error if $PROCESS_NAME is just start or restart"
        else
                handle_error "$PROCESS_NAME is down"
                sleep $REPORT_INTERVAL
        fi

        rm -rf $TMP_LOF_FILE
done
