[tox]
envlist = docs,py35,py27,pep8
minversion = 2.3.2
skipsdist = True

[testenv]
setenv = VIRTUAL_ENV={envdir}
         OS_LOG_CAPTURE={env:OS_LOG_CAPTURE:true}
         OS_STDOUT_CAPTURE={env:OS_STDOUT_CAPTURE:true}
         OS_STDERR_CAPTURE={env:OS_STDERR_CAPTURE:true}
         PYTHONWARNINGS=default::DeprecationWarning
passenv = TRACE_FAILONLY GENERATE_HASHES http_proxy HTTP_PROXY https_proxy HTTPS_PROXY no_proxy NO_PROXY
usedevelop = True
recreate = False
install_command =
  pip install {opts} {packages}
deps =
  -c{env:UPPER_CONSTRAINTS_FILE:upper-constraints.txt}
  -r{toxinidir}/requirements.txt
  -r{toxinidir}/test-requirements.txt
whitelist_externals = sh
commands =
  {toxinidir}/tools/install_wo_neutron_lib.sh {envdir}
  stestr run {posargs}
# there is also secret magic in ostestr which lets you run in a fail only
# mode. To do this define the TRACE_FAILONLY environmental variable.

[testenv:debug]
basepython = python3
commands = oslo_debug_helper -t neutron/tests {posargs}

[testenv:common]
basepython = python3
# Fake job to define environment variables shared between dsvm/non-dsvm jobs
setenv = OS_TEST_TIMEOUT={env:OS_TEST_TIMEOUT:180}
commands = false

[testenv:dsvm]
basepython = python3
# Fake job to define environment variables shared between dsvm jobs
setenv = OS_SUDO_TESTING=1
         OS_ROOTWRAP_CMD=sudo {envdir}/bin/neutron-rootwrap {envdir}/etc/neutron/rootwrap.conf
         OS_ROOTWRAP_DAEMON_CMD=sudo {envdir}/bin/neutron-rootwrap-daemon {envdir}/etc/neutron/rootwrap.conf
         OS_FAIL_ON_MISSING_DEPS=1
         OS_LOG_PATH={env:OS_LOG_PATH:/opt/stack/logs}
commands = false

[testenv:functional]
basepython = python2.7
setenv = {[testenv]setenv}
         {[testenv:common]setenv}
         OS_TEST_PATH=./neutron/tests/functional
         OS_LOG_PATH={env:OS_LOG_PATH:/opt/stack/logs}
deps =
  {[testenv]deps}
  -r{toxinidir}/neutron/tests/functional/requirements.txt

[testenv:functional-python35]
basepython = python3.5
setenv = {[testenv:functional]setenv}
deps =
  {[testenv:functional]deps}

[testenv:dsvm-functional]
basepython = python2.7
setenv = {[testenv:functional]setenv}
         {[testenv:dsvm]setenv}
deps =
  {[testenv:functional]deps}
commands =
  {toxinidir}/tools/install_wo_neutron_lib.sh {envdir}
  {toxinidir}/tools/deploy_rootwrap.sh {toxinidir} {envdir}/etc {envdir}/bin
  stestr run --concurrency 4 {posargs}

[testenv:dsvm-functional-python35]
basepython = python3.5
setenv = {[testenv:dsvm-functional]setenv}
deps =
  {[testenv:dsvm-functional]deps}
commands =
  {toxinidir}/tools/deploy_rootwrap.sh {toxinidir} {envdir}/etc {envdir}/bin
  stestr run {posargs}

[testenv:dsvm-fullstack]
basepython = python2.7
setenv = {[testenv]setenv}
         {[testenv:common]setenv}
         {[testenv:dsvm]setenv}
         # workaround for DB teardown lock contention (bug/1541742)
         OS_TEST_TIMEOUT={env:OS_TEST_TIMEOUT:600}
         OS_TEST_PATH=./neutron/tests/fullstack
deps =
  {[testenv:functional]deps}
commands =
  {toxinidir}/tools/install_wo_neutron_lib.sh {envdir}
  {toxinidir}/tools/generate_dhclient_script_for_fullstack.sh {envdir}
  {toxinidir}/tools/deploy_rootwrap.sh {toxinidir} {envdir}/etc {envdir}/bin
  stestr run --concurrency 4 {posargs}

[testenv:dsvm-fullstack-python35]
basepython = python3.5
setenv = {[testenv:dsvm-fullstack]setenv}
deps =
  {[testenv:dsvm-fullstack]deps}
commands =
  {[testenv:dsvm-fullstack]commands}

[testenv:releasenotes]
basepython = python3
deps = -r{toxinidir}/doc/requirements.txt
commands = sphinx-build -a -E -W -d releasenotes/build/doctrees -b html releasenotes/source releasenotes/build/html

[testenv:pep8]
basepython = python2.7
deps =
  {[testenv]deps}
commands=
  {toxinidir}/tools/install_wo_neutron_lib.sh {envdir}
  # If it is easier to add a check via a shell script, consider adding it in this file
  sh ./tools/misc-sanity-checks.sh
  {toxinidir}/tools/check_unit_test_structure.sh
  # Checks for coding and style guidelines
  flake8
  sh ./tools/coding-checks.sh --pylint '{posargs}'
  neutron-db-manage --config-file neutron/tests/etc/neutron.conf check_migration
  python ./tools/list_moved_globals.py
  {[testenv:genconfig]commands}
  {[testenv:bashate]commands}
  {[testenv:bandit]commands}
whitelist_externals =
  sh
  bash

[testenv:cover]
basepython = python2.7
setenv =
  {[testenv]setenv}
  PYTHON=coverage run --source neutron --parallel-mode
commands =
  stestr run --no-subunit-trace {posargs}
  coverage combine
  coverage report --fail-under=82 --skip-covered
  coverage html -d cover
  coverage xml -o cover/coverage.xml

[testenv:venv]
basepython = python3
commands = {posargs}

[testenv:docs]
basepython = python3
envdir = {toxworkdir}/docs
deps =
  -c{env:UPPER_CONSTRAINTS_FILE:https://releases.openstack.org/constraints/upper/rocky}
  -r{toxinidir}/doc/requirements.txt
  -r{toxinidir}/requirements.txt
commands = sphinx-build -W -b html doc/source doc/build/html

[testenv:linkcheck]
basepython = python3
deps = -r{toxinidir}/doc/requirements.txt
commands = sphinx-build -W -b linkcheck doc/source doc/build/linkcheck

[flake8]
# E125 continuation line does not distinguish itself from next logical line
# E126 continuation line over-indented for hanging indent
# E128 continuation line under-indented for visual indent
# H404 multi line docstring should start with a summary
# H405 multi line docstring summary not separated with an empty line
# N530 direct neutron imports not allowed
# TODO(ihrachys) figure out what to do with N534
# N534 Untranslated exception message
# TODO(amotoki) check the following new rules should be fixed or ignored
# E731 do not assign a lambda expression, use a def
# W504 line break after binary operator
ignore = E125,E126,E128,E731,H404,H405,N530,N534,W504
# H106: Don't put vim configuration in source files
# H203: Use assertIs(Not)None to check for None
# H204: Use assert(Not)Equal to check for equality
# H205: Use assert(Greater|Less)(Equal) for comparison
# H904: Delay string interpolations at logging calls
enable-extensions=H106,H203,H204,H205,H904
show-source = true
exclude = ./.*,build,dist,doc
import-order-style = pep8

[hacking]
import_exceptions = neutron._i18n
local-check-factory = neutron.hacking.checks.factory

[testenv:bandit]
basepython = python3
# B104: Possible binding to all interfaces
# B111: Execute with run_as_root=True identified, possible security issue
# B311: Standard pseudo-random generators are not suitable for security/cryptographic purpose
deps = -r{toxinidir}/test-requirements.txt
commands = bandit -r neutron -x tests -n5 -s B104,B111,B311

[testenv:bashate]
basepython = python3
commands = bash -c "find {toxinidir}             \
         -not \( -type d -name .tox\* -prune \)  \
         -not \( -type d -name .venv\* -prune \) \
         -type f                                 \
         -name \*.sh                             \
# E005 file does not begin with #! or have a .sh prefix
# E006 check for lines longer than 79 columns
# E042 local declaration hides errors
# E043 Arithmetic compound has inconsistent return semantics
         -print0 | xargs -0 bashate -v -iE006 -eE005,E042,E043"

[testenv:genconfig]
basepython = python2
commands =
  {toxinidir}/tools/install_wo_neutron_lib.sh {envdir}
  {toxinidir}/tools/generate_config_file_samples.sh

# This environment can be used to quickly validate that all needed system
# packages required to successfully execute test targets are installed
[testenv:bindep]
basepython = python3
# Do not install any requirements. We want this to be fast and work even if
# system dependencies are missing, since it's used to tell you what system
# dependencies are missing! This also means that bindep must be installed
# separately, outside of the requirements files.
deps = bindep
commands = bindep test

[testenv:lower-constraints]
basepython = python3
setenv = OS_TEST_TIMEOUT={env:OS_TEST_TIMEOUT:60}
deps =
  -c{toxinidir}/lower-constraints.txt
  -r{toxinidir}/test-requirements.txt
  -r{toxinidir}/requirements.txt
